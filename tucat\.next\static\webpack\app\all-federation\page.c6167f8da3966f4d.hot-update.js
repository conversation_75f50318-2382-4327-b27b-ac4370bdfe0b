"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/all-federation/page",{

/***/ "(app-pages-browser)/./app/all-federation/page.tsx":
/*!*************************************!*\
  !*** ./app/all-federation/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AllFederationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_UserNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserNav */ \"(app-pages-browser)/./components/UserNav.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(app-pages-browser)/./components/ui/theme-toggle.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_AuthContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AuthContext */ \"(app-pages-browser)/./components/AuthContext.tsx\");\n/* harmony import */ var _components_dashboard_FederationAnalyticsChart__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/FederationAnalyticsChart */ \"(app-pages-browser)/./components/dashboard/FederationAnalyticsChart.tsx\");\n/* harmony import */ var _components_dashboard_SectionAverageChart__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dashboard/SectionAverageChart */ \"(app-pages-browser)/./components/dashboard/SectionAverageChart.tsx\");\n/* harmony import */ var _components_dashboard_YouthRepresentationChart__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dashboard/YouthRepresentationChart */ \"(app-pages-browser)/./components/dashboard/YouthRepresentationChart.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AllFederationPage() {\n    _s();\n    const { user } = (0,_components_AuthContext__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const [federations, setFederations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFederation, setSelectedFederation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"score\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desc\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Calculated averages\n    const [averagePresidentAge, setAveragePresidentAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [averageSecretaryAge, setAverageSecretaryAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Score analytics\n    const [averageTotalScore, setAverageTotalScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [averagePercentage, setAveragePercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [highestScore, setHighestScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lowestScore, setLowestScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [highestPercentage, setHighestPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lowestPercentage, setLowestPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Youth and section analytics\n    const [averageYouthPercentage, setAverageYouthPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [avgSectionScores, setAvgSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [minSectionScores, setMinSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [maxSectionScores, setMaxSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sectionList, setSectionList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Selected federation details\n    const [currentFederation, setCurrentFederation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSectionScores, setCurrentSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Filtered and sorted federations\n    const filteredAndSortedFederations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AllFederationPage.useMemo[filteredAndSortedFederations]\": ()=>{\n            let filtered = federations.filter({\n                \"AllFederationPage.useMemo[filteredAndSortedFederations].filtered\": (fed)=>{\n                    var _fed_name, _fed_president_name, _fed_secretary_name;\n                    const matchesSearch = !searchTerm || ((_fed_name = fed.name) === null || _fed_name === void 0 ? void 0 : _fed_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_fed_president_name = fed.president_name) === null || _fed_president_name === void 0 ? void 0 : _fed_president_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_fed_secretary_name = fed.secretary_name) === null || _fed_secretary_name === void 0 ? void 0 : _fed_secretary_name.toLowerCase().includes(searchTerm.toLowerCase()));\n                    const percentage = fed.percentage || 0;\n                    const matchesFilter = filterBy === \"all\" || filterBy === \"high\" && percentage >= 75 || filterBy === \"medium\" && percentage >= 50 && percentage < 75 || filterBy === \"low\" && percentage < 50;\n                    return matchesSearch && matchesFilter;\n                }\n            }[\"AllFederationPage.useMemo[filteredAndSortedFederations].filtered\"]);\n            // Sort the filtered results\n            filtered.sort({\n                \"AllFederationPage.useMemo[filteredAndSortedFederations]\": (a, b)=>{\n                    let aValue, bValue;\n                    switch(sortBy){\n                        case \"name\":\n                            aValue = a.name || \"\";\n                            bValue = b.name || \"\";\n                            break;\n                        case \"score\":\n                            aValue = a.totalScore || 0;\n                            bValue = b.totalScore || 0;\n                            break;\n                        case \"percentage\":\n                            aValue = a.percentage || 0;\n                            bValue = b.percentage || 0;\n                            break;\n                        case \"youth\":\n                            aValue = getYouthPercentageValue(a.youth_percentage || \"0-25%\");\n                            bValue = getYouthPercentageValue(b.youth_percentage || \"0-25%\");\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (typeof aValue === \"string\") {\n                        return sortOrder === \"asc\" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n                    } else {\n                        return sortOrder === \"asc\" ? aValue - bValue : bValue - aValue;\n                    }\n                }\n            }[\"AllFederationPage.useMemo[filteredAndSortedFederations]\"]);\n            return filtered;\n        }\n    }[\"AllFederationPage.useMemo[filteredAndSortedFederations]\"], [\n        federations,\n        searchTerm,\n        sortBy,\n        sortOrder,\n        filterBy\n    ]);\n    // Helper function to convert youth percentage to numeric value\n    const getYouthPercentageValue = (youthPercentage)=>{\n        switch(youthPercentage){\n            case \"above-75%\":\n                return 87.5;\n            case \"51-75%\":\n                return 63;\n            case \"26-50%\":\n                return 38;\n            case \"0-25%\":\n                return 12.5;\n            default:\n                return 0;\n        }\n    };\n    // Section titles (should match backend section numbers 3-11)\n    // Section max scores (from useAssessment.ts)\n    const sectionMaxScores = {\n        3: 14,\n        4: 4,\n        5: 11,\n        6: 9,\n        7: 7,\n        8: 8,\n        9: 11,\n        10: 16,\n        11: 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AllFederationPage.useEffect\": ()=>{\n            setSectionList([\n                {\n                    id: 3,\n                    title: \"Leadership\"\n                },\n                {\n                    id: 4,\n                    title: \"Organizational Structure\"\n                },\n                {\n                    id: 5,\n                    title: \"Management\"\n                },\n                {\n                    id: 6,\n                    title: \"Worker Participation\"\n                },\n                {\n                    id: 7,\n                    title: \"Culture and Gender\"\n                },\n                {\n                    id: 8,\n                    title: \"Collective Bargaining\"\n                },\n                {\n                    id: 9,\n                    title: \"Member Engagement\"\n                },\n                {\n                    id: 10,\n                    title: \"Financial Stability\"\n                },\n                {\n                    id: 11,\n                    title: \"Audit & Compliance\"\n                }\n            ]);\n        }\n    }[\"AllFederationPage.useEffect\"], []);\n    // Fetch all federations\n    const fetchFederations = async function() {\n        let showRefreshToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (showRefreshToast) setRefreshing(true);\n        try {\n            // This would be replaced with your actual API endpoint\n            const response = await fetch('/api/federations');\n            if (!response.ok) throw new Error('Failed to fetch federations');\n            const data = await response.json();\n            setFederations(data.federations || []);\n            // Calculate averages\n            if ((data.federations || []).length > 0) {\n                const presidentAges = data.federations.filter((fed)=>fed.president_age).map((fed)=>fed.president_age);\n                const secretaryAges = data.federations.filter((fed)=>fed.secretary_age).map((fed)=>fed.secretary_age);\n                const avgPresidentAge = presidentAges.length > 0 ? Math.round(presidentAges.reduce((sum, age)=>sum + age, 0) / presidentAges.length) : 0;\n                const avgSecretaryAge = secretaryAges.length > 0 ? Math.round(secretaryAges.reduce((sum, age)=>sum + age, 0) / secretaryAges.length) : 0;\n                setAveragePresidentAge(avgPresidentAge);\n                setAverageSecretaryAge(avgSecretaryAge);\n            }\n            // Set score analytics\n            setAverageTotalScore(data.avgTotalScore || 0);\n            setAveragePercentage(data.avgPercentage || 0);\n            setHighestScore(data.highestScore || 0);\n            setLowestScore(data.lowestScore || 0);\n            setHighestPercentage(data.highestPercentage || 0);\n            setLowestPercentage(data.lowestPercentage || 0);\n            setAverageYouthPercentage(data.avgYouthPercentage || 0);\n            setAvgSectionScores(data.avgSectionScores || {});\n            setMinSectionScores(data.minSectionScores || {});\n            setMaxSectionScores(data.maxSectionScores || {});\n            if (showRefreshToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_15__.toast.success(\"Data refreshed successfully!\");\n            }\n        } catch (error) {\n            console.error('Error fetching federations:', error);\n            if (showRefreshToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Failed to refresh data\");\n            }\n        } finally{\n            setIsLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AllFederationPage.useEffect\": ()=>{\n            fetchFederations();\n        }\n    }[\"AllFederationPage.useEffect\"], []);\n    // Handle federation selection change\n    const handleFederationChange = (value)=>{\n        setSelectedFederation(value);\n        if (value === 'all') {\n            setCurrentFederation(null);\n            setCurrentSectionScores({});\n        } else {\n            const selected = federations.find((fed)=>fed.id === value);\n            setCurrentFederation(selected || null);\n            setCurrentSectionScores((selected === null || selected === void 0 ? void 0 : selected.sectionScores) || {});\n        }\n    };\n    // Export functionality\n    const exportToCSV = ()=>{\n        const headers = [\n            \"Name\",\n            \"President\",\n            \"Secretary\",\n            \"Total Score\",\n            \"Percentage\",\n            \"Youth %\"\n        ];\n        const csvData = filteredAndSortedFederations.map((fed)=>[\n                fed.name || \"\",\n                fed.president_name || \"\",\n                fed.secretary_name || \"\",\n                fed.totalScore || 0,\n                fed.percentage || 0,\n                fed.youth_percentage || \"\"\n            ]);\n        const csvContent = [\n            headers,\n            ...csvData\n        ].map((row)=>row.map((cell)=>'\"'.concat(cell, '\"')).join(\",\")).join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"federations-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        a.click();\n        URL.revokeObjectURL(url);\n        sonner__WEBPACK_IMPORTED_MODULE_15__.toast.success(\"Data exported successfully!\");\n    };\n    // Performance metrics\n    const performanceMetrics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AllFederationPage.useMemo[performanceMetrics]\": ()=>{\n            const total = federations.length;\n            const highPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) >= 75\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            const mediumPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) >= 50 && (f.percentage || 0) < 75\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            const lowPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) < 50\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            return {\n                total,\n                highPerformers,\n                mediumPerformers,\n                lowPerformers,\n                highPercentage: total > 0 ? Math.round(highPerformers / total * 100) : 0,\n                mediumPercentage: total > 0 ? Math.round(mediumPerformers / total * 100) : 0,\n                lowPercentage: total > 0 ? Math.round(lowPerformers / total * 100) : 0\n            };\n        }\n    }[\"AllFederationPage.useMemo[performanceMetrics]\"], [\n        federations\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col bg-background/50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 md:px-6 flex h-16 items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/assessment\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Admin Dashboard\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"All Federations Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-auto flex items-center gap-2 sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: ()=>fetchFederations(true),\n                                    disabled: refreshing,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mr-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: exportToCSV,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mr-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export CSV\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserNav__WEBPACK_IMPORTED_MODULE_3__.UserNav, {}, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                        className: \"grid w-full grid-cols-5 md:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"overview\",\n                                                children: \"Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"analytics\",\n                                                children: \"Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"qa-stats\",\n                                                children: \"Q&A Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"reports\",\n                                                children: \"Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"federations\",\n                                                children: \"Federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 flex-wrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        placeholder: \"Search federations...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-8 w-[200px]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                value: filterBy,\n                                                onValueChange: (value)=>setFilterBy(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                        className: \"w-[130px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Scores\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"high\",\n                                                                children: \"High (75%+)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"medium\",\n                                                                children: \"Medium (50-74%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"low\",\n                                                                children: \"Low (<50%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                value: sortBy,\n                                                onValueChange: (value)=>setSortBy(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                        className: \"w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"score\",\n                                                                children: \"Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"name\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"percentage\",\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"youth\",\n                                                                children: \"Youth %\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\"),\n                                                children: sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 40\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 74\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"overview\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 animate-in fade-in duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"Support for Effective and Inclusive Trade Unions in Bangladesh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center gap-8 flex-wrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png\",\n                                                            alt: \"German Cooperation Bangladesh\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png\",\n                                                            alt: \"International Labour Organization\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png\",\n                                                            alt: \"Growing Together OPC\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png\",\n                                                            alt: \"Government of the people's republic of Bangladesh\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Average Performance\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-primary\",\n                                                                    children: [\n                                                                        averagePercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Overall assessment score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: averagePercentage >= 75 ? \"default\" : averagePercentage >= 50 ? \"secondary\" : \"destructive\",\n                                                                        children: averagePercentage >= 75 ? \"Excellent\" : averagePercentage >= 50 ? \"Good\" : \"Needs Improvement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Youth Engagement\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: [\n                                                                        averageYouthPercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Average youth representation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(averageYouthPercentage, 100), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Score Range\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Highest:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 423,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-green-600\",\n                                                                                children: [\n                                                                                    highestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Lowest:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-red-600\",\n                                                                                children: [\n                                                                                    lowestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Range:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: [\n                                                                                    highestPercentage - lowestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"analytics\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FederationAnalyticsChart__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            federations: federations,\n                                            selectedFederation: selectedFederation,\n                                            onSelectFederation: handleFederationChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SectionAverageChart__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            avgSectionScores: avgSectionScores,\n                                            minSectionScores: minSectionScores,\n                                            maxSectionScores: maxSectionScores,\n                                            sectionList: sectionList,\n                                            sectionMaxScores: sectionMaxScores\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"reports\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_YouthRepresentationChart__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        federations: federations\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"federations\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Federation Directory\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Interactive table showing all federations with their performance metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 animate-pulse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-12 w-12 bg-muted rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-4 bg-muted rounded w-1/4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-muted rounded w-1/2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-8 w-16 bg-muted rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        filteredAndSortedFederations.map((federation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/federation/\".concat(federation.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer animate-in fade-in slide-in-from-bottom-2\",\n                                                                    style: {\n                                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"h-6 w-6 text-primary\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                lineNumber: 502,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 501,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                    className: \"font-semibold text-lg\",\n                                                                                                    children: federation.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 505,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-4 text-sm text-muted-foreground\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"President: \",\n                                                                                                                federation.president_name || \"N/A\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                            lineNumber: 507,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"Secretary: \",\n                                                                                                                federation.secretary_name || \"N/A\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                            lineNumber: 508,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 506,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 504,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-right\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-2xl font-bold text-primary\",\n                                                                                                    children: [\n                                                                                                        federation.percentage || 0,\n                                                                                                        \"%\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 515,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: [\n                                                                                                        federation.totalScore || 0,\n                                                                                                        \"/\",\n                                                                                                        federation.maxScore || 0\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 518,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                            variant: (federation.percentage || 0) >= 75 ? \"default\" : (federation.percentage || 0) >= 50 ? \"secondary\" : \"destructive\",\n                                                                                            className: \"ml-2\",\n                                                                                            children: (federation.percentage || 0) >= 75 ? \"High\" : (federation.percentage || 0) >= 50 ? \"Medium\" : \"Low\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 523,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-medium text-green-600\",\n                                                                                                    children: federation.youth_percentage || \"N/A\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 535,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                                    children: \"Youth\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 538,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 534,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                lineNumber: 542,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 541,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 513,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full transition-all duration-500 \".concat((federation.percentage || 0) >= 75 ? 'bg-green-500' : (federation.percentage || 0) >= 50 ? 'bg-yellow-500' : 'bg-red-500'),\n                                                                                    style: {\n                                                                                        width: \"\".concat(Math.min(federation.percentage || 0, 100), \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 550,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 549,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, federation.id, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        filteredAndSortedFederations.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-2\",\n                                                                    children: \"No federations found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Try adjusting your search or filter criteria\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"shadow-md animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-20 bg-muted rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 582,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-2 bg-muted/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Leadership Overview\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Average leadership demographics across all federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-500/10 p-2.5 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-muted-foreground\",\n                                                                children: \"Average Ages\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-muted/30 p-2 rounded-md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"President\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 613,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    averagePresidentAge,\n                                                                                    \" years\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 612,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-muted/30 p-2 rounded-md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Secretary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 619,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    averageSecretaryAge,\n                                                                                    \" years\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-2 bg-muted/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Performance Summary\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Key performance indicators across all federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                    children: averageTotalScore\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Avg Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        averagePercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Avg Percentage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 648,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Highest Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-green-600\",\n                                                                    children: [\n                                                                        highestScore,\n                                                                        \" (\",\n                                                                        highestPercentage,\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Lowest Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-red-600\",\n                                                                    children: [\n                                                                        lowestScore,\n                                                                        \" (\",\n                                                                        lowestPercentage,\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Youth Engagement:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-purple-600\",\n                                                                    children: [\n                                                                        averageYouthPercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 663,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 654,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n_s(AllFederationPage, \"Fo7Cs3et470Q19GJX6/8j65+4ms=\", false, function() {\n    return [\n        _components_AuthContext__WEBPACK_IMPORTED_MODULE_11__.useAuth\n    ];\n});\n_c = AllFederationPage;\nvar _c;\n$RefreshReg$(_c, \"AllFederationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hbGwtZmVkZXJhdGlvbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFcUQ7QUFFNEM7QUFDbEQ7QUFDWTtBQUM5QjtBQUNtQjtBQUNGO0FBQ0E7QUFDa0M7QUFNMUQ7QUFDaUY7QUFFcEQ7QUFDb0M7QUFDVjtBQUNVO0FBQ3hEO0FBRWhCLFNBQVMyQzs7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR04saUVBQU9BO0lBQ3hCLE1BQU0sQ0FBQ08sYUFBYUMsZUFBZSxHQUFHOUMsK0NBQVFBLENBQXdCLEVBQUU7SUFDeEUsTUFBTSxDQUFDK0Msb0JBQW9CQyxzQkFBc0IsR0FBR2hELCtDQUFRQSxDQUFTO0lBQ3JFLE1BQU0sQ0FBQ2lELFdBQVdDLGFBQWEsR0FBR2xELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ21ELFlBQVlDLGNBQWMsR0FBR3BELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3FELFFBQVFDLFVBQVUsR0FBR3RELCtDQUFRQSxDQUE0QztJQUNoRixNQUFNLENBQUN1RCxXQUFXQyxhQUFhLEdBQUd4RCwrQ0FBUUEsQ0FBaUI7SUFDM0QsTUFBTSxDQUFDeUQsVUFBVUMsWUFBWSxHQUFHMUQsK0NBQVFBLENBQW9DO0lBQzVFLE1BQU0sQ0FBQzJELFdBQVdDLGFBQWEsR0FBRzVELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzZELFlBQVlDLGNBQWMsR0FBRzlELCtDQUFRQSxDQUFDO0lBRTdDLHNCQUFzQjtJQUN0QixNQUFNLENBQUMrRCxxQkFBcUJDLHVCQUF1QixHQUFHaEUsK0NBQVFBLENBQVM7SUFDdkUsTUFBTSxDQUFDaUUscUJBQXFCQyx1QkFBdUIsR0FBR2xFLCtDQUFRQSxDQUFTO0lBQ3ZFLGtCQUFrQjtJQUNsQixNQUFNLENBQUNtRSxtQkFBbUJDLHFCQUFxQixHQUFHcEUsK0NBQVFBLENBQVM7SUFDbkUsTUFBTSxDQUFDcUUsbUJBQW1CQyxxQkFBcUIsR0FBR3RFLCtDQUFRQSxDQUFTO0lBQ25FLE1BQU0sQ0FBQ3VFLGNBQWNDLGdCQUFnQixHQUFHeEUsK0NBQVFBLENBQVM7SUFDekQsTUFBTSxDQUFDeUUsYUFBYUMsZUFBZSxHQUFHMUUsK0NBQVFBLENBQVM7SUFDdkQsTUFBTSxDQUFDMkUsbUJBQW1CQyxxQkFBcUIsR0FBRzVFLCtDQUFRQSxDQUFTO0lBQ25FLE1BQU0sQ0FBQzZFLGtCQUFrQkMsb0JBQW9CLEdBQUc5RSwrQ0FBUUEsQ0FBUztJQUNqRSw4QkFBOEI7SUFDOUIsTUFBTSxDQUFDK0Usd0JBQXdCQywwQkFBMEIsR0FBR2hGLCtDQUFRQSxDQUFTO0lBQzdFLE1BQU0sQ0FBQ2lGLGtCQUFrQkMsb0JBQW9CLEdBQUdsRiwrQ0FBUUEsQ0FBZ0MsQ0FBQztJQUN6RixNQUFNLENBQUNtRixrQkFBa0JDLG9CQUFvQixHQUFHcEYsK0NBQVFBLENBQWdDLENBQUM7SUFDekYsTUFBTSxDQUFDcUYsa0JBQWtCQyxvQkFBb0IsR0FBR3RGLCtDQUFRQSxDQUFnQyxDQUFDO0lBQ3pGLE1BQU0sQ0FBQ3VGLGFBQWFDLGVBQWUsR0FBR3hGLCtDQUFRQSxDQUFrQyxFQUFFO0lBQ2xGLDhCQUE4QjtJQUM5QixNQUFNLENBQUN5RixtQkFBbUJDLHFCQUFxQixHQUFHMUYsK0NBQVFBLENBQTZCO0lBQ3ZGLE1BQU0sQ0FBQzJGLHNCQUFzQkMsd0JBQXdCLEdBQUc1RiwrQ0FBUUEsQ0FBZ0MsQ0FBQztJQUVqRyxrQ0FBa0M7SUFDbEMsTUFBTTZGLCtCQUErQjNGLDhDQUFPQTttRUFBQztZQUMzQyxJQUFJNEYsV0FBV2pELFlBQVlrRCxNQUFNO29GQUFDQyxDQUFBQTt3QkFFOUJBLFdBQ0FBLHFCQUNBQTtvQkFIRixNQUFNQyxnQkFBZ0IsQ0FBQzlDLGdCQUNyQjZDLFlBQUFBLElBQUlFLElBQUksY0FBUkYsZ0NBQUFBLFVBQVVHLFdBQVcsR0FBR0MsUUFBUSxDQUFDakQsV0FBV2dELFdBQVcsVUFDdkRILHNCQUFBQSxJQUFJSyxjQUFjLGNBQWxCTCwwQ0FBQUEsb0JBQW9CRyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ2pELFdBQVdnRCxXQUFXLFVBQ2pFSCxzQkFBQUEsSUFBSU0sY0FBYyxjQUFsQk4sMENBQUFBLG9CQUFvQkcsV0FBVyxHQUFHQyxRQUFRLENBQUNqRCxXQUFXZ0QsV0FBVztvQkFFbkUsTUFBTUksYUFBYVAsSUFBSU8sVUFBVSxJQUFJO29CQUNyQyxNQUFNQyxnQkFBZ0IvQyxhQUFhLFNBQ2hDQSxhQUFhLFVBQVU4QyxjQUFjLE1BQ3JDOUMsYUFBYSxZQUFZOEMsY0FBYyxNQUFNQSxhQUFhLE1BQzFEOUMsYUFBYSxTQUFTOEMsYUFBYTtvQkFFdEMsT0FBT04saUJBQWlCTztnQkFDMUI7O1lBRUEsNEJBQTRCO1lBQzVCVixTQUFTVyxJQUFJOzJFQUFDLENBQUNDLEdBQUdDO29CQUNoQixJQUFJQyxRQUFhQztvQkFFakIsT0FBUXhEO3dCQUNOLEtBQUs7NEJBQ0h1RCxTQUFTRixFQUFFUixJQUFJLElBQUk7NEJBQ25CVyxTQUFTRixFQUFFVCxJQUFJLElBQUk7NEJBQ25CO3dCQUNGLEtBQUs7NEJBQ0hVLFNBQVNGLEVBQUVJLFVBQVUsSUFBSTs0QkFDekJELFNBQVNGLEVBQUVHLFVBQVUsSUFBSTs0QkFDekI7d0JBQ0YsS0FBSzs0QkFDSEYsU0FBU0YsRUFBRUgsVUFBVSxJQUFJOzRCQUN6Qk0sU0FBU0YsRUFBRUosVUFBVSxJQUFJOzRCQUN6Qjt3QkFDRixLQUFLOzRCQUNISyxTQUFTRyx3QkFBd0JMLEVBQUVNLGdCQUFnQixJQUFJOzRCQUN2REgsU0FBU0Usd0JBQXdCSixFQUFFSyxnQkFBZ0IsSUFBSTs0QkFDdkQ7d0JBQ0Y7NEJBQ0UsT0FBTztvQkFDWDtvQkFFQSxJQUFJLE9BQU9KLFdBQVcsVUFBVTt3QkFDOUIsT0FBT3JELGNBQWMsUUFBUXFELE9BQU9LLGFBQWEsQ0FBQ0osVUFBVUEsT0FBT0ksYUFBYSxDQUFDTDtvQkFDbkYsT0FBTzt3QkFDTCxPQUFPckQsY0FBYyxRQUFRcUQsU0FBU0MsU0FBU0EsU0FBU0Q7b0JBQzFEO2dCQUNGOztZQUVBLE9BQU9kO1FBQ1Q7a0VBQUc7UUFBQ2pEO1FBQWFNO1FBQVlFO1FBQVFFO1FBQVdFO0tBQVM7SUFFekQsK0RBQStEO0lBQy9ELE1BQU1zRCwwQkFBMEIsQ0FBQ0c7UUFDL0IsT0FBUUE7WUFDTixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFTLE9BQU87WUFDckI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsNkRBQTZEO0lBQzdELDZDQUE2QztJQUM3QyxNQUFNQyxtQkFBa0Q7UUFDdEQsR0FBRztRQUNILEdBQUc7UUFDSCxHQUFHO1FBQ0gsR0FBRztRQUNILEdBQUc7UUFDSCxHQUFHO1FBQ0gsR0FBRztRQUNILElBQUk7UUFDSixJQUFJO0lBQ047SUFFQWxILGdEQUFTQTt1Q0FBQztZQUNSdUYsZUFBZTtnQkFDYjtvQkFBRTRCLElBQUk7b0JBQUdDLE9BQU87Z0JBQWE7Z0JBQzdCO29CQUFFRCxJQUFJO29CQUFHQyxPQUFPO2dCQUEyQjtnQkFDM0M7b0JBQUVELElBQUk7b0JBQUdDLE9BQU87Z0JBQWE7Z0JBQzdCO29CQUFFRCxJQUFJO29CQUFHQyxPQUFPO2dCQUF1QjtnQkFDdkM7b0JBQUVELElBQUk7b0JBQUdDLE9BQU87Z0JBQXFCO2dCQUNyQztvQkFBRUQsSUFBSTtvQkFBR0MsT0FBTztnQkFBd0I7Z0JBQ3hDO29CQUFFRCxJQUFJO29CQUFHQyxPQUFPO2dCQUFvQjtnQkFDcEM7b0JBQUVELElBQUk7b0JBQUlDLE9BQU87Z0JBQXNCO2dCQUN2QztvQkFBRUQsSUFBSTtvQkFBSUMsT0FBTztnQkFBcUI7YUFDdkM7UUFDSDtzQ0FBRyxFQUFFO0lBQ0wsd0JBQXdCO0lBQ3hCLE1BQU1DLG1CQUFtQjtZQUFPQyxvRkFBbUI7UUFDakQsSUFBSUEsa0JBQWtCekQsY0FBYztRQUVwQyxJQUFJO1lBQ0YsdURBQXVEO1lBQ3ZELE1BQU0wRCxXQUFXLE1BQU1DLE1BQU07WUFDN0IsSUFBSSxDQUFDRCxTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBRWxDLE1BQU1DLE9BQU8sTUFBTUosU0FBU0ssSUFBSTtZQUNoQy9FLGVBQWU4RSxLQUFLL0UsV0FBVyxJQUFJLEVBQUU7WUFFckMscUJBQXFCO1lBQ3JCLElBQUksQ0FBQytFLEtBQUsvRSxXQUFXLElBQUksRUFBRSxFQUFFaUYsTUFBTSxHQUFHLEdBQUc7Z0JBQ3ZDLE1BQU1DLGdCQUFnQkgsS0FBSy9FLFdBQVcsQ0FBQ2tELE1BQU0sQ0FBQyxDQUFDQyxNQUE2QkEsSUFBSWdDLGFBQWEsRUFBRUMsR0FBRyxDQUFDLENBQUNqQyxNQUE2QkEsSUFBSWdDLGFBQWE7Z0JBQ2xKLE1BQU1FLGdCQUFnQk4sS0FBSy9FLFdBQVcsQ0FBQ2tELE1BQU0sQ0FBQyxDQUFDQyxNQUE2QkEsSUFBSW1DLGFBQWEsRUFBRUYsR0FBRyxDQUFDLENBQUNqQyxNQUE2QkEsSUFBSW1DLGFBQWE7Z0JBQ2xKLE1BQU1DLGtCQUFrQkwsY0FBY0QsTUFBTSxHQUFHLElBQzNDTyxLQUFLQyxLQUFLLENBQUNQLGNBQWNRLE1BQU0sQ0FBQyxDQUFDQyxLQUFhQyxNQUFnQkQsTUFBTUMsS0FBSyxLQUFLVixjQUFjRCxNQUFNLElBQ2xHO2dCQUNKLE1BQU1ZLGtCQUFrQlIsY0FBY0osTUFBTSxHQUFHLElBQzNDTyxLQUFLQyxLQUFLLENBQUNKLGNBQWNLLE1BQU0sQ0FBQyxDQUFDQyxLQUFhQyxNQUFnQkQsTUFBTUMsS0FBSyxLQUFLUCxjQUFjSixNQUFNLElBQ2xHO2dCQUNKOUQsdUJBQXVCb0U7Z0JBQ3ZCbEUsdUJBQXVCd0U7WUFDekI7WUFFQSxzQkFBc0I7WUFDdEJ0RSxxQkFBcUJ3RCxLQUFLZSxhQUFhLElBQUk7WUFDM0NyRSxxQkFBcUJzRCxLQUFLZ0IsYUFBYSxJQUFJO1lBQzNDcEUsZ0JBQWdCb0QsS0FBS3JELFlBQVksSUFBSTtZQUNyQ0csZUFBZWtELEtBQUtuRCxXQUFXLElBQUk7WUFDbkNHLHFCQUFxQmdELEtBQUtqRCxpQkFBaUIsSUFBSTtZQUMvQ0csb0JBQW9COEMsS0FBSy9DLGdCQUFnQixJQUFJO1lBQzdDRywwQkFBMEI0QyxLQUFLaUIsa0JBQWtCLElBQUk7WUFDckQzRCxvQkFBb0IwQyxLQUFLM0MsZ0JBQWdCLElBQUksQ0FBQztZQUM5Q0csb0JBQW9Cd0MsS0FBS3pDLGdCQUFnQixJQUFJLENBQUM7WUFDOUNHLG9CQUFvQnNDLEtBQUt2QyxnQkFBZ0IsSUFBSSxDQUFDO1lBRTlDLElBQUlrQyxrQkFBa0I7Z0JBQ3BCN0UsMENBQUtBLENBQUNvRyxPQUFPLENBQUM7WUFDaEI7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsSUFBSXhCLGtCQUFrQjtnQkFDcEI3RSwwQ0FBS0EsQ0FBQ3FHLEtBQUssQ0FBQztZQUNkO1FBQ0YsU0FBVTtZQUNSN0YsYUFBYTtZQUNiWSxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQTdELGdEQUFTQTt1Q0FBQztZQUNScUg7UUFDRjtzQ0FBRyxFQUFFO0lBRUwscUNBQXFDO0lBQ3JDLE1BQU0yQix5QkFBeUIsQ0FBQ0M7UUFDOUJsRyxzQkFBc0JrRztRQUV0QixJQUFJQSxVQUFVLE9BQU87WUFDbkJ4RCxxQkFBcUI7WUFDckJFLHdCQUF3QixDQUFDO1FBQzNCLE9BQU87WUFDTCxNQUFNdUQsV0FBV3RHLFlBQVl1RyxJQUFJLENBQUNwRCxDQUFBQSxNQUFPQSxJQUFJb0IsRUFBRSxLQUFLOEI7WUFDcER4RCxxQkFBcUJ5RCxZQUFZO1lBQ2pDdkQsd0JBQXdCdUQsQ0FBQUEscUJBQUFBLCtCQUFBQSxTQUFVRSxhQUFhLEtBQUksQ0FBQztRQUN0RDtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU1DLGNBQWM7UUFDbEIsTUFBTUMsVUFBVTtZQUFDO1lBQVE7WUFBYTtZQUFhO1lBQWU7WUFBYztTQUFVO1FBQzFGLE1BQU1DLFVBQVUzRCw2QkFBNkJvQyxHQUFHLENBQUNqQyxDQUFBQSxNQUFPO2dCQUN0REEsSUFBSUUsSUFBSSxJQUFJO2dCQUNaRixJQUFJSyxjQUFjLElBQUk7Z0JBQ3RCTCxJQUFJTSxjQUFjLElBQUk7Z0JBQ3RCTixJQUFJYyxVQUFVLElBQUk7Z0JBQ2xCZCxJQUFJTyxVQUFVLElBQUk7Z0JBQ2xCUCxJQUFJZ0IsZ0JBQWdCLElBQUk7YUFDekI7UUFFRCxNQUFNeUMsYUFBYTtZQUFDRjtlQUFZQztTQUFRLENBQ3JDdkIsR0FBRyxDQUFDeUIsQ0FBQUEsTUFBT0EsSUFBSXpCLEdBQUcsQ0FBQzBCLENBQUFBLE9BQVEsSUFBUyxPQUFMQSxNQUFLLE1BQUlDLElBQUksQ0FBQyxNQUM3Q0EsSUFBSSxDQUFDO1FBRVIsTUFBTUMsT0FBTyxJQUFJQyxLQUFLO1lBQUNMO1NBQVcsRUFBRTtZQUFFTSxNQUFNO1FBQVc7UUFDdkQsTUFBTUMsTUFBTUMsSUFBSUMsZUFBZSxDQUFDTDtRQUNoQyxNQUFNbkQsSUFBSXlELFNBQVNDLGFBQWEsQ0FBQztRQUNqQzFELEVBQUUyRCxJQUFJLEdBQUdMO1FBQ1R0RCxFQUFFNEQsUUFBUSxHQUFHLGVBQXNELE9BQXZDLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7UUFDbkUvRCxFQUFFZ0UsS0FBSztRQUNQVCxJQUFJVSxlQUFlLENBQUNYO1FBQ3BCdEgsMENBQUtBLENBQUNvRyxPQUFPLENBQUM7SUFDaEI7SUFFQSxzQkFBc0I7SUFDdEIsTUFBTThCLHFCQUFxQjFLLDhDQUFPQTt5REFBQztZQUNqQyxNQUFNMkssUUFBUWhJLFlBQVlpRixNQUFNO1lBQ2hDLE1BQU1nRCxpQkFBaUJqSSxZQUFZa0QsTUFBTTtpRUFBQ2dGLENBQUFBLElBQUssQ0FBQ0EsRUFBRXhFLFVBQVUsSUFBSSxNQUFNO2dFQUFJdUIsTUFBTTtZQUNoRixNQUFNa0QsbUJBQW1CbkksWUFBWWtELE1BQU07aUVBQUNnRixDQUFBQSxJQUFLLENBQUNBLEVBQUV4RSxVQUFVLElBQUksTUFBTSxNQUFNLENBQUN3RSxFQUFFeEUsVUFBVSxJQUFJLEtBQUs7Z0VBQUl1QixNQUFNO1lBQzlHLE1BQU1tRCxnQkFBZ0JwSSxZQUFZa0QsTUFBTTtpRUFBQ2dGLENBQUFBLElBQUssQ0FBQ0EsRUFBRXhFLFVBQVUsSUFBSSxLQUFLO2dFQUFJdUIsTUFBTTtZQUU5RSxPQUFPO2dCQUNMK0M7Z0JBQ0FDO2dCQUNBRTtnQkFDQUM7Z0JBQ0FDLGdCQUFnQkwsUUFBUSxJQUFJeEMsS0FBS0MsS0FBSyxDQUFDLGlCQUFrQnVDLFFBQVMsT0FBTztnQkFDekVNLGtCQUFrQk4sUUFBUSxJQUFJeEMsS0FBS0MsS0FBSyxDQUFDLG1CQUFvQnVDLFFBQVMsT0FBTztnQkFDN0VPLGVBQWVQLFFBQVEsSUFBSXhDLEtBQUtDLEtBQUssQ0FBQyxnQkFBaUJ1QyxRQUFTLE9BQU87WUFDekU7UUFDRjt3REFBRztRQUFDaEk7S0FBWTtJQUVoQixxQkFDRSw4REFBQ3dJO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBT0QsV0FBVTswQkFDaEIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNUssa0RBQUlBO29DQUFDMkosTUFBSzs4Q0FDVCw0RUFBQzFKLHlEQUFNQTt3Q0FBQzZLLFNBQVE7d0NBQVFDLE1BQUs7d0NBQU9ILFdBQVU7a0RBQzVDLDRFQUFDcEssOE1BQVNBOzRDQUFDb0ssV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHekIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0k7NENBQUdKLFdBQVU7OzhEQUNaLDhEQUFDdEosOE1BQWVBO29EQUFDc0osV0FBVTs7Ozs7O2dEQUF5Qjs7Ozs7OztzREFHdEQsOERBQUNLOzRDQUFFTCxXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUdqRCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDM0sseURBQU1BO29DQUNMaUwsU0FBUyxJQUFNdEUsaUJBQWlCO29DQUNoQ3VFLFVBQVVoSTtvQ0FDVjJILFNBQVE7b0NBQ1JDLE1BQUs7b0NBQ0xILFdBQVU7O3NEQUVWLDhEQUFDMUosOE1BQVNBOzRDQUFDMEosV0FBVyxnQkFBaUQsT0FBakN6SCxhQUFhLGlCQUFpQjs7Ozs7O3dDQUFROzs7Ozs7OzhDQUc5RSw4REFBQ2xELHlEQUFNQTtvQ0FBQ2lMLFNBQVN0QztvQ0FBYWtDLFNBQVE7b0NBQVVDLE1BQUs7b0NBQUtILFdBQVU7O3NEQUNsRSw4REFBQy9KLDhNQUFRQTs0Q0FBQytKLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7OENBR3ZDLDhEQUFDN0ssb0VBQVdBOzs7Ozs4Q0FDWiw4REFBQ0Qsd0RBQU9BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtkLDhEQUFDc0w7Z0JBQUtSLFdBQVU7O2tDQUNkLDhEQUFDeEsscURBQUlBO3dCQUFDb0ksT0FBT3ZGO3dCQUFXb0ksZUFBZW5JO3dCQUFjMEgsV0FBVTs7MENBQzdELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN0Syx5REFBUUE7d0NBQUNzSyxXQUFVOzswREFDbEIsOERBQUNySyw0REFBV0E7Z0RBQUNpSSxPQUFNOzBEQUFXOzs7Ozs7MERBQzlCLDhEQUFDakksNERBQVdBO2dEQUFDaUksT0FBTTswREFBWTs7Ozs7OzBEQUMvQiw4REFBQ2pJLDREQUFXQTtnREFBQ2lJLE9BQU07MERBQVc7Ozs7OzswREFDOUIsOERBQUNqSSw0REFBV0E7Z0RBQUNpSSxPQUFNOzBEQUFVOzs7Ozs7MERBQzdCLDhEQUFDakksNERBQVdBO2dEQUFDaUksT0FBTTswREFBYzs7Ozs7Ozs7Ozs7O2tEQUduQyw4REFBQ21DO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDakssOE1BQU1BO3dEQUFDaUssV0FBVTs7Ozs7O2tFQUNsQiw4REFBQzFLLHVEQUFLQTt3REFDSm9MLGFBQVk7d0RBQ1o5QyxPQUFPL0Y7d0RBQ1A4SSxVQUFVLENBQUNDLElBQU05SSxjQUFjOEksRUFBRUMsTUFBTSxDQUFDakQsS0FBSzt3REFDN0NvQyxXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNySiwwREFBTUE7Z0RBQUNpSCxPQUFPekY7Z0RBQVVzSSxlQUFlLENBQUM3QyxRQUFleEYsWUFBWXdGOztrRUFDbEUsOERBQUM5RyxpRUFBYUE7d0RBQUNrSixXQUFVOzswRUFDdkIsOERBQUNoSyw4TUFBTUE7Z0VBQUNnSyxXQUFVOzs7Ozs7MEVBQ2xCLDhEQUFDakosK0RBQVdBOzs7Ozs7Ozs7OztrRUFFZCw4REFBQ0gsaUVBQWFBOzswRUFDWiw4REFBQ0MsOERBQVVBO2dFQUFDK0csT0FBTTswRUFBTTs7Ozs7OzBFQUN4Qiw4REFBQy9HLDhEQUFVQTtnRUFBQytHLE9BQU07MEVBQU87Ozs7OzswRUFDekIsOERBQUMvRyw4REFBVUE7Z0VBQUMrRyxPQUFNOzBFQUFTOzs7Ozs7MEVBQzNCLDhEQUFDL0csOERBQVVBO2dFQUFDK0csT0FBTTswRUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUk1Qiw4REFBQ2pILDBEQUFNQTtnREFBQ2lILE9BQU83RjtnREFBUTBJLGVBQWUsQ0FBQzdDLFFBQWU1RixVQUFVNEY7O2tFQUM5RCw4REFBQzlHLGlFQUFhQTt3REFBQ2tKLFdBQVU7a0VBQ3ZCLDRFQUFDakosK0RBQVdBOzs7Ozs7Ozs7O2tFQUVkLDhEQUFDSCxpRUFBYUE7OzBFQUNaLDhEQUFDQyw4REFBVUE7Z0VBQUMrRyxPQUFNOzBFQUFROzs7Ozs7MEVBQzFCLDhEQUFDL0csOERBQVVBO2dFQUFDK0csT0FBTTswRUFBTzs7Ozs7OzBFQUN6Qiw4REFBQy9HLDhEQUFVQTtnRUFBQytHLE9BQU07MEVBQWE7Ozs7OzswRUFDL0IsOERBQUMvRyw4REFBVUE7Z0VBQUMrRyxPQUFNOzBFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSTlCLDhEQUFDdkkseURBQU1BO2dEQUNMNkssU0FBUTtnREFDUkMsTUFBSztnREFDTEcsU0FBUyxJQUFNcEksYUFBYUQsY0FBYyxRQUFRLFNBQVM7MERBRTFEQSxjQUFjLHNCQUFRLDhEQUFDekIsOE1BQU9BO29EQUFDd0osV0FBVTs7Ozs7eUVBQWUsOERBQUN2Siw4TUFBUUE7b0RBQUN1SixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLbkYsOERBQUN2Syw0REFBV0E7Z0NBQUNtSSxPQUFNO2dDQUFXb0MsV0FBVTswQ0FDdEMsNEVBQUNEO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDSTtvREFBR0osV0FBVTs4REFBMEI7Ozs7Ozs4REFDeEMsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2M7NERBQUlDLEtBQUk7NERBQTZGQyxLQUFJOzREQUFnQ2hCLFdBQVU7Ozs7OztzRUFDcEosOERBQUNjOzREQUFJQyxLQUFJOzREQUFpR0MsS0FBSTs0REFBb0NoQixXQUFVOzs7Ozs7c0VBQzVKLDhEQUFDYzs0REFBSUMsS0FBSTs0REFBb0ZDLEtBQUk7NERBQXVCaEIsV0FBVTs7Ozs7O3NFQUNsSSw4REFBQ2M7NERBQUlDLEtBQUk7NERBQWlIQyxLQUFJOzREQUFvRGhCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLaE0sOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ25MLHFEQUFJQTs7c0VBQ0gsOERBQUNFLDJEQUFVQTs0REFBQ2lMLFdBQVU7c0VBQ3BCLDRFQUFDaEwsMERBQVNBO2dFQUFDZ0wsV0FBVTs7a0ZBQ25CLDhEQUFDNUosOE1BQUtBO3dFQUFDNEosV0FBVTs7Ozs7O29FQUE0Qjs7Ozs7Ozs7Ozs7O3NFQUlqRCw4REFBQ2xMLDREQUFXQTs7OEVBQ1YsOERBQUNpTDtvRUFBSUMsV0FBVTs7d0VBQW1Dakg7d0VBQWtCOzs7Ozs7OzhFQUNwRSw4REFBQ3NIO29FQUFFTCxXQUFVOzhFQUFnQzs7Ozs7OzhFQUM3Qyw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUN6Syx1REFBS0E7d0VBQUMySyxTQUFTbkgscUJBQXFCLEtBQUssWUFBWUEscUJBQXFCLEtBQUssY0FBYztrRkFDM0ZBLHFCQUFxQixLQUFLLGNBQWNBLHFCQUFxQixLQUFLLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU1wRiw4REFBQ2xFLHFEQUFJQTs7c0VBQ0gsOERBQUNFLDJEQUFVQTs0REFBQ2lMLFdBQVU7c0VBQ3BCLDRFQUFDaEwsMERBQVNBO2dFQUFDZ0wsV0FBVTs7a0ZBQ25CLDhEQUFDN0osOE1BQUtBO3dFQUFDNkosV0FBVTs7Ozs7O29FQUEyQjs7Ozs7Ozs7Ozs7O3NFQUloRCw4REFBQ2xMLDREQUFXQTs7OEVBQ1YsOERBQUNpTDtvRUFBSUMsV0FBVTs7d0VBQXFDdkc7d0VBQXVCOzs7Ozs7OzhFQUMzRSw4REFBQzRHO29FQUFFTCxXQUFVOzhFQUFnQzs7Ozs7OzhFQUM3Qyw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDRDs0RUFDQ0MsV0FBVTs0RUFDVmlCLE9BQU87Z0ZBQUVDLE9BQU8sR0FBeUMsT0FBdENuRSxLQUFLb0UsR0FBRyxDQUFDMUgsd0JBQXdCLE1BQUs7NEVBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT3RFLDhEQUFDNUUscURBQUlBOztzRUFDSCw4REFBQ0UsMkRBQVVBOzREQUFDaUwsV0FBVTtzRUFDcEIsNEVBQUNoTCwwREFBU0E7Z0VBQUNnTCxXQUFVOztrRkFDbkIsOERBQUMzSiw4TUFBTUE7d0VBQUMySixXQUFVOzs7Ozs7b0VBQTBCOzs7Ozs7Ozs7Ozs7c0VBSWhELDhEQUFDbEwsNERBQVdBO3NFQUNWLDRFQUFDaUw7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNvQjtnRkFBS3BCLFdBQVU7MEZBQVU7Ozs7OzswRkFDMUIsOERBQUNvQjtnRkFBS3BCLFdBQVU7O29GQUFnQzNHO29GQUFrQjs7Ozs7Ozs7Ozs7OztrRkFFcEUsOERBQUMwRzt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNvQjtnRkFBS3BCLFdBQVU7MEZBQVU7Ozs7OzswRkFDMUIsOERBQUNvQjtnRkFBS3BCLFdBQVU7O29GQUE4QnpHO29GQUFpQjs7Ozs7Ozs7Ozs7OztrRkFFakUsOERBQUN3Rzt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNvQjtnRkFBS3BCLFdBQVU7MEZBQVU7Ozs7OzswRkFDMUIsOERBQUNvQjtnRkFBS3BCLFdBQVU7O29GQUFpQjNHLG9CQUFvQkU7b0ZBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FTcEYsOERBQUM5RCw0REFBV0E7Z0NBQUNtSSxPQUFNO2dDQUFZb0MsV0FBVTswQ0FDdkMsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQy9JLHVGQUF3QkE7NENBQ3ZCTSxhQUFhQTs0Q0FDYkUsb0JBQW9CQTs0Q0FDcEI0SixvQkFBb0IxRDs7Ozs7O3NEQUd0Qiw4REFBQ3pHLGtGQUFtQkE7NENBQ2xCeUMsa0JBQWtCQTs0Q0FDbEJFLGtCQUFrQkE7NENBQ2xCRSxrQkFBa0JBOzRDQUNsQkUsYUFBYUE7NENBQ2I0QixrQkFBa0JBOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLeEIsOERBQUNwRyw0REFBV0E7Z0NBQUNtSSxPQUFNO2dDQUFVb0MsV0FBVTswQ0FDckMsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDN0ksdUZBQXdCQTt3Q0FBQ0ksYUFBYUE7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSTNDLDhEQUFDOUIsNERBQVdBO2dDQUFDbUksT0FBTTtnQ0FBY29DLFdBQVU7MENBQ3pDLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ25MLHFEQUFJQTs7MERBQ0gsOERBQUNFLDJEQUFVQTs7a0VBQ1QsOERBQUNDLDBEQUFTQTt3REFBQ2dMLFdBQVU7OzBFQUNuQiw4REFBQ25LLDhNQUFTQTtnRUFBQ21LLFdBQVU7Ozs7Ozs0REFBWTs7Ozs7OztrRUFHbkMsOERBQUMvSyxnRUFBZUE7a0VBQUM7Ozs7Ozs7Ozs7OzswREFJbkIsOERBQUNILDREQUFXQTswREFDVDZDLDBCQUNDLDhEQUFDb0k7b0RBQUlDLFdBQVU7OERBQ1o7d0RBQUM7d0RBQUc7d0RBQUc7d0RBQUc7d0RBQUc7cURBQUUsQ0FBQ3JELEdBQUcsQ0FBQyxDQUFDMkUsa0JBQ3BCLDhEQUFDdkI7NERBQVlDLFdBQVU7OzhFQUNyQiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7Ozs4RUFDZiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7Ozs7O3NGQUNmLDhEQUFDRDs0RUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzhFQUVqQiw4REFBQ0Q7b0VBQUlDLFdBQVU7Ozs7Ozs7MkRBTlBzQjs7Ozs7Ozs7O3lFQVdkLDhEQUFDdkI7b0RBQUlDLFdBQVU7O3dEQUNaekYsNkJBQTZCb0MsR0FBRyxDQUFDLENBQUM0RSxZQUFZQyxzQkFDN0MsOERBQUNwTSxrREFBSUE7Z0VBQUMySixNQUFNLGVBQTZCLE9BQWR3QyxXQUFXekYsRUFBRTswRUFDdEMsNEVBQUNpRTtvRUFDQ0MsV0FBVTtvRUFDVmlCLE9BQU87d0VBQUVRLGdCQUFnQixHQUFjLE9BQVhELFFBQVEsSUFBRztvRUFBSTs7c0ZBRTdDLDhEQUFDekI7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNEOzRGQUFJQyxXQUFVO3NHQUNiLDRFQUFDbkssOE1BQVNBO2dHQUFDbUssV0FBVTs7Ozs7Ozs7Ozs7c0dBRXZCLDhEQUFDRDs7OEdBQ0MsOERBQUMyQjtvR0FBRzFCLFdBQVU7OEdBQXlCdUIsV0FBVzNHLElBQUk7Ozs7Ozs4R0FDdEQsOERBQUNtRjtvR0FBSUMsV0FBVTs7c0hBQ2IsOERBQUNvQjs7Z0hBQUs7Z0hBQVlHLFdBQVd4RyxjQUFjLElBQUk7Ozs7Ozs7c0hBQy9DLDhEQUFDcUc7O2dIQUFLO2dIQUFZRyxXQUFXdkcsY0FBYyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhGQUtyRCw4REFBQytFO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDRDtvR0FBSUMsV0FBVTs7d0dBQ1p1QixXQUFXdEcsVUFBVSxJQUFJO3dHQUFFOzs7Ozs7OzhHQUU5Qiw4REFBQzhFO29HQUFJQyxXQUFVOzt3R0FDWnVCLFdBQVcvRixVQUFVLElBQUk7d0dBQUU7d0dBQUUrRixXQUFXSSxRQUFRLElBQUk7Ozs7Ozs7Ozs7Ozs7c0dBSXpELDhEQUFDcE0sdURBQUtBOzRGQUNKMkssU0FDRSxDQUFDcUIsV0FBV3RHLFVBQVUsSUFBSSxNQUFNLEtBQUssWUFDckMsQ0FBQ3NHLFdBQVd0RyxVQUFVLElBQUksTUFBTSxLQUFLLGNBQWM7NEZBRXJEK0UsV0FBVTtzR0FFVCxDQUFDdUIsV0FBV3RHLFVBQVUsSUFBSSxNQUFNLEtBQUssU0FDckMsQ0FBQ3NHLFdBQVd0RyxVQUFVLElBQUksTUFBTSxLQUFLLFdBQVc7Ozs7OztzR0FHbkQsOERBQUM4RTs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNEO29HQUFJQyxXQUFVOzhHQUNadUIsV0FBVzdGLGdCQUFnQixJQUFJOzs7Ozs7OEdBRWxDLDhEQUFDcUU7b0dBQUlDLFdBQVU7OEdBQWdDOzs7Ozs7Ozs7Ozs7c0dBR2pELDhEQUFDM0sseURBQU1BOzRGQUFDNkssU0FBUTs0RkFBUUMsTUFBSztzR0FDM0IsNEVBQUM1Siw4TUFBR0E7Z0dBQUN5SixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRkFNckIsOERBQUNEOzRFQUFJQyxXQUFVO3NGQUNiLDRFQUFDRDtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ0Q7b0ZBQ0NDLFdBQVcsZ0RBR1YsT0FGQyxDQUFDdUIsV0FBV3RHLFVBQVUsSUFBSSxNQUFNLEtBQUssaUJBQ3JDLENBQUNzRyxXQUFXdEcsVUFBVSxJQUFJLE1BQU0sS0FBSyxrQkFBa0I7b0ZBRXpEZ0csT0FBTzt3RkFBRUMsT0FBTyxHQUE2QyxPQUExQ25FLEtBQUtvRSxHQUFHLENBQUNJLFdBQVd0RyxVQUFVLElBQUksR0FBRyxNQUFLO29GQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytEQTdEdkJzRyxXQUFXekYsRUFBRTs7Ozs7d0RBcUUvRHZCLDZCQUE2QmlDLE1BQU0sS0FBSyxtQkFDdkMsOERBQUN1RDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNuSyw4TUFBU0E7b0VBQUNtSyxXQUFVOzs7Ozs7OEVBQ3JCLDhEQUFDMEI7b0VBQUcxQixXQUFVOzhFQUE2Qjs7Ozs7OzhFQUMzQyw4REFBQ0s7b0VBQUVMLFdBQVU7OEVBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBYXREckksMEJBQ0MsOERBQUNvSTt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ25MLHFEQUFJQTs0QkFBQ21MLFdBQVU7OzhDQUNkLDhEQUFDakwsMkRBQVVBOzhDQUNULDRFQUFDQywwREFBU0E7a0RBQUM7Ozs7Ozs7Ozs7OzhDQUViLDhEQUFDRiw0REFBV0E7OENBQ1YsNEVBQUNpTDt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZDQUtyQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDbkwscURBQUlBO2dDQUFDbUwsV0FBVTs7a0RBQ2QsOERBQUNqTCwyREFBVUE7d0NBQUNpTCxXQUFVOzswREFDcEIsOERBQUNoTCwwREFBU0E7Z0RBQUNnTCxXQUFVOztrRUFDbkIsOERBQUNuSyw4TUFBU0E7d0RBQUNtSyxXQUFVOzs7Ozs7b0RBQXlCOzs7Ozs7OzBEQUdoRCw4REFBQy9LLGdFQUFlQTswREFBQzs7Ozs7Ozs7Ozs7O2tEQUluQiw4REFBQ0gsNERBQVdBO3dDQUFDa0wsV0FBVTtrREFDckIsNEVBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDbEssOE1BQVVBOzREQUFDa0ssV0FBVTs7Ozs7Ozs7Ozs7a0VBRXhCLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUMwQjtnRUFBRzFCLFdBQVU7MEVBQTRDOzs7Ozs7MEVBQzFELDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0s7Z0ZBQUVMLFdBQVU7MEZBQWdDOzs7Ozs7MEZBQzdDLDhEQUFDSztnRkFBRUwsV0FBVTs7b0ZBQ1Z2SDtvRkFBb0I7Ozs7Ozs7Ozs7Ozs7a0ZBR3pCLDhEQUFDc0g7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDSztnRkFBRUwsV0FBVTswRkFBZ0M7Ozs7OzswRkFDN0MsOERBQUNLO2dGQUFFTCxXQUFVOztvRkFDVnJIO29GQUFvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBVXJDLDhEQUFDOUQscURBQUlBO2dDQUFDbUwsV0FBVTs7a0RBQ2QsOERBQUNqTCwyREFBVUE7d0NBQUNpTCxXQUFVOzswREFDcEIsOERBQUNoTCwwREFBU0E7Z0RBQUNnTCxXQUFVOztrRUFDbkIsOERBQUM5Siw4TUFBU0E7d0RBQUM4SixXQUFVOzs7Ozs7b0RBQXlCOzs7Ozs7OzBEQUdoRCw4REFBQy9LLGdFQUFlQTswREFBQzs7Ozs7Ozs7Ozs7O2tEQUluQiw4REFBQ0gsNERBQVdBO3dDQUFDa0wsV0FBVTtrREFDckIsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUFxQ25IOzs7Ozs7OEVBQ3BELDhEQUFDa0g7b0VBQUlDLFdBQVU7OEVBQWdDOzs7Ozs7Ozs7Ozs7c0VBRWpELDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzt3RUFBb0NqSDt3RUFBa0I7Ozs7Ozs7OEVBQ3JFLDhEQUFDZ0g7b0VBQUlDLFdBQVU7OEVBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSW5ELDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ29COzhFQUFLOzs7Ozs7OEVBQ04sOERBQUNBO29FQUFLcEIsV0FBVTs7d0VBQWdDL0c7d0VBQWE7d0VBQUdJO3dFQUFrQjs7Ozs7Ozs7Ozs7OztzRUFFcEYsOERBQUMwRzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNvQjs4RUFBSzs7Ozs7OzhFQUNOLDhEQUFDQTtvRUFBS3BCLFdBQVU7O3dFQUE4QjdHO3dFQUFZO3dFQUFHSTt3RUFBaUI7Ozs7Ozs7Ozs7Ozs7c0VBRWhGLDhEQUFDd0c7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDb0I7OEVBQUs7Ozs7Ozs4RUFDTiw4REFBQ0E7b0VBQUtwQixXQUFVOzt3RUFBaUN2Rzt3RUFBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVc5RjtHQXpvQndCcEM7O1FBQ0xMLDZEQUFPQTs7O0tBREZLIiwic291cmNlcyI6WyJEOlxcdHVjYXRcXHR1Y2F0XFxhcHBcXGFsbC1mZWRlcmF0aW9uXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IERhc2hib2FyZFByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkUHJvdmlkZXJcIjtcclxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jYXJkXCI7XHJcbmltcG9ydCB7IFVzZXJOYXYgfSBmcm9tIFwiQC9jb21wb25lbnRzL1VzZXJOYXZcIjtcclxuaW1wb3J0IHsgVGhlbWVUb2dnbGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RoZW1lLXRvZ2dsZVwiO1xyXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XHJcbmltcG9ydCB7IElucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9pbnB1dFwiO1xyXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIjtcclxuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGFic1wiO1xyXG5pbXBvcnQge1xyXG4gIEFycm93TGVmdCwgQnVpbGRpbmcyLCBVc2VyQ2lyY2xlLCBTZWFyY2gsIEZpbHRlciwgRG93bmxvYWQsXHJcbiAgVHJlbmRpbmdVcCwgVHJlbmRpbmdEb3duLCBCYXJDaGFydDMsIFBpZUNoYXJ0LCBVc2VycyxcclxuICBDYWxlbmRhciwgQXdhcmQsIFRhcmdldCwgUmVmcmVzaEN3LCBFeWUsIFNldHRpbmdzLFxyXG4gIENoZXZyb25Eb3duLCBDaGV2cm9uVXAsIFNvcnRBc2MsIFNvcnREZXNjLCBMYXlvdXREYXNoYm9hcmRcclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlbGVjdFwiO1xyXG5pbXBvcnQgeyBGZWRlcmF0aW9uIH0gZnJvbSBcIkAvbGliL3R5cGVzXCI7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tIFwiQC9jb21wb25lbnRzL0F1dGhDb250ZXh0XCI7XHJcbmltcG9ydCBGZWRlcmF0aW9uQW5hbHl0aWNzQ2hhcnQgZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvRmVkZXJhdGlvbkFuYWx5dGljc0NoYXJ0XCI7XHJcbmltcG9ydCBTZWN0aW9uQXZlcmFnZUNoYXJ0IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1NlY3Rpb25BdmVyYWdlQ2hhcnRcIjtcclxuaW1wb3J0IFlvdXRoUmVwcmVzZW50YXRpb25DaGFydCBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9Zb3V0aFJlcHJlc2VudGF0aW9uQ2hhcnRcIjtcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwic29ubmVyXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbGxGZWRlcmF0aW9uUGFnZSgpIHtcclxuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcclxuICBjb25zdCBbZmVkZXJhdGlvbnMsIHNldEZlZGVyYXRpb25zXSA9IHVzZVN0YXRlPFBhcnRpYWw8RmVkZXJhdGlvbj5bXT4oW10pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEZlZGVyYXRpb24sIHNldFNlbGVjdGVkRmVkZXJhdGlvbl0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiYWxsXCIpO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2VhcmNoVGVybSwgc2V0U2VhcmNoVGVybV0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc29ydEJ5LCBzZXRTb3J0QnldID0gdXNlU3RhdGU8XCJuYW1lXCIgfCBcInNjb3JlXCIgfCBcInBlcmNlbnRhZ2VcIiB8IFwieW91dGhcIj4oXCJzY29yZVwiKTtcclxuICBjb25zdCBbc29ydE9yZGVyLCBzZXRTb3J0T3JkZXJdID0gdXNlU3RhdGU8XCJhc2NcIiB8IFwiZGVzY1wiPihcImRlc2NcIik7XHJcbiAgY29uc3QgW2ZpbHRlckJ5LCBzZXRGaWx0ZXJCeV0gPSB1c2VTdGF0ZTxcImFsbFwiIHwgXCJoaWdoXCIgfCBcIm1lZGl1bVwiIHwgXCJsb3dcIj4oXCJhbGxcIik7XHJcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKFwib3ZlcnZpZXdcIik7XHJcbiAgY29uc3QgW3JlZnJlc2hpbmcsIHNldFJlZnJlc2hpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAvLyBDYWxjdWxhdGVkIGF2ZXJhZ2VzXHJcbiAgY29uc3QgW2F2ZXJhZ2VQcmVzaWRlbnRBZ2UsIHNldEF2ZXJhZ2VQcmVzaWRlbnRBZ2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcclxuICBjb25zdCBbYXZlcmFnZVNlY3JldGFyeUFnZSwgc2V0QXZlcmFnZVNlY3JldGFyeUFnZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIC8vIFNjb3JlIGFuYWx5dGljc1xyXG4gIGNvbnN0IFthdmVyYWdlVG90YWxTY29yZSwgc2V0QXZlcmFnZVRvdGFsU2NvcmVdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcclxuICBjb25zdCBbYXZlcmFnZVBlcmNlbnRhZ2UsIHNldEF2ZXJhZ2VQZXJjZW50YWdlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW2hpZ2hlc3RTY29yZSwgc2V0SGlnaGVzdFNjb3JlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW2xvd2VzdFNjb3JlLCBzZXRMb3dlc3RTY29yZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIGNvbnN0IFtoaWdoZXN0UGVyY2VudGFnZSwgc2V0SGlnaGVzdFBlcmNlbnRhZ2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcclxuICBjb25zdCBbbG93ZXN0UGVyY2VudGFnZSwgc2V0TG93ZXN0UGVyY2VudGFnZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIC8vIFlvdXRoIGFuZCBzZWN0aW9uIGFuYWx5dGljc1xyXG4gIGNvbnN0IFthdmVyYWdlWW91dGhQZXJjZW50YWdlLCBzZXRBdmVyYWdlWW91dGhQZXJjZW50YWdlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW2F2Z1NlY3Rpb25TY29yZXMsIHNldEF2Z1NlY3Rpb25TY29yZXNdID0gdXNlU3RhdGU8eyBbc2VjdGlvbjogbnVtYmVyXTogbnVtYmVyIH0+KHt9KTtcclxuICBjb25zdCBbbWluU2VjdGlvblNjb3Jlcywgc2V0TWluU2VjdGlvblNjb3Jlc10gPSB1c2VTdGF0ZTx7IFtzZWN0aW9uOiBudW1iZXJdOiBudW1iZXIgfT4oe30pO1xyXG4gIGNvbnN0IFttYXhTZWN0aW9uU2NvcmVzLCBzZXRNYXhTZWN0aW9uU2NvcmVzXSA9IHVzZVN0YXRlPHsgW3NlY3Rpb246IG51bWJlcl06IG51bWJlciB9Pih7fSk7XHJcbiAgY29uc3QgW3NlY3Rpb25MaXN0LCBzZXRTZWN0aW9uTGlzdF0gPSB1c2VTdGF0ZTx7IGlkOiBudW1iZXI7IHRpdGxlOiBzdHJpbmcgfVtdPihbXSk7XHJcbiAgLy8gU2VsZWN0ZWQgZmVkZXJhdGlvbiBkZXRhaWxzXHJcbiAgY29uc3QgW2N1cnJlbnRGZWRlcmF0aW9uLCBzZXRDdXJyZW50RmVkZXJhdGlvbl0gPSB1c2VTdGF0ZTxQYXJ0aWFsPEZlZGVyYXRpb24+IHwgbnVsbD4obnVsbCk7XHJcbiAgY29uc3QgW2N1cnJlbnRTZWN0aW9uU2NvcmVzLCBzZXRDdXJyZW50U2VjdGlvblNjb3Jlc10gPSB1c2VTdGF0ZTx7IFtzZWN0aW9uOiBudW1iZXJdOiBudW1iZXIgfT4oe30pO1xyXG5cclxuICAvLyBGaWx0ZXJlZCBhbmQgc29ydGVkIGZlZGVyYXRpb25zXHJcbiAgY29uc3QgZmlsdGVyZWRBbmRTb3J0ZWRGZWRlcmF0aW9ucyA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgbGV0IGZpbHRlcmVkID0gZmVkZXJhdGlvbnMuZmlsdGVyKGZlZCA9PiB7XHJcbiAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSAhc2VhcmNoVGVybSB8fFxyXG4gICAgICAgIGZlZC5uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICBmZWQucHJlc2lkZW50X25hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxyXG4gICAgICAgIGZlZC5zZWNyZXRhcnlfbmFtZT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpO1xyXG5cclxuICAgICAgY29uc3QgcGVyY2VudGFnZSA9IGZlZC5wZXJjZW50YWdlIHx8IDA7XHJcbiAgICAgIGNvbnN0IG1hdGNoZXNGaWx0ZXIgPSBmaWx0ZXJCeSA9PT0gXCJhbGxcIiB8fFxyXG4gICAgICAgIChmaWx0ZXJCeSA9PT0gXCJoaWdoXCIgJiYgcGVyY2VudGFnZSA+PSA3NSkgfHxcclxuICAgICAgICAoZmlsdGVyQnkgPT09IFwibWVkaXVtXCIgJiYgcGVyY2VudGFnZSA+PSA1MCAmJiBwZXJjZW50YWdlIDwgNzUpIHx8XHJcbiAgICAgICAgKGZpbHRlckJ5ID09PSBcImxvd1wiICYmIHBlcmNlbnRhZ2UgPCA1MCk7XHJcblxyXG4gICAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzRmlsdGVyO1xyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gU29ydCB0aGUgZmlsdGVyZWQgcmVzdWx0c1xyXG4gICAgZmlsdGVyZWQuc29ydCgoYSwgYikgPT4ge1xyXG4gICAgICBsZXQgYVZhbHVlOiBhbnksIGJWYWx1ZTogYW55O1xyXG5cclxuICAgICAgc3dpdGNoIChzb3J0QnkpIHtcclxuICAgICAgICBjYXNlIFwibmFtZVwiOlxyXG4gICAgICAgICAgYVZhbHVlID0gYS5uYW1lIHx8IFwiXCI7XHJcbiAgICAgICAgICBiVmFsdWUgPSBiLm5hbWUgfHwgXCJcIjtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgXCJzY29yZVwiOlxyXG4gICAgICAgICAgYVZhbHVlID0gYS50b3RhbFNjb3JlIHx8IDA7XHJcbiAgICAgICAgICBiVmFsdWUgPSBiLnRvdGFsU2NvcmUgfHwgMDtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgXCJwZXJjZW50YWdlXCI6XHJcbiAgICAgICAgICBhVmFsdWUgPSBhLnBlcmNlbnRhZ2UgfHwgMDtcclxuICAgICAgICAgIGJWYWx1ZSA9IGIucGVyY2VudGFnZSB8fCAwO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSBcInlvdXRoXCI6XHJcbiAgICAgICAgICBhVmFsdWUgPSBnZXRZb3V0aFBlcmNlbnRhZ2VWYWx1ZShhLnlvdXRoX3BlcmNlbnRhZ2UgfHwgXCIwLTI1JVwiKTtcclxuICAgICAgICAgIGJWYWx1ZSA9IGdldFlvdXRoUGVyY2VudGFnZVZhbHVlKGIueW91dGhfcGVyY2VudGFnZSB8fCBcIjAtMjUlXCIpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgIHJldHVybiAwO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAodHlwZW9mIGFWYWx1ZSA9PT0gXCJzdHJpbmdcIikge1xyXG4gICAgICAgIHJldHVybiBzb3J0T3JkZXIgPT09IFwiYXNjXCIgPyBhVmFsdWUubG9jYWxlQ29tcGFyZShiVmFsdWUpIDogYlZhbHVlLmxvY2FsZUNvbXBhcmUoYVZhbHVlKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICByZXR1cm4gc29ydE9yZGVyID09PSBcImFzY1wiID8gYVZhbHVlIC0gYlZhbHVlIDogYlZhbHVlIC0gYVZhbHVlO1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4gZmlsdGVyZWQ7XHJcbiAgfSwgW2ZlZGVyYXRpb25zLCBzZWFyY2hUZXJtLCBzb3J0QnksIHNvcnRPcmRlciwgZmlsdGVyQnldKTtcclxuXHJcbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNvbnZlcnQgeW91dGggcGVyY2VudGFnZSB0byBudW1lcmljIHZhbHVlXHJcbiAgY29uc3QgZ2V0WW91dGhQZXJjZW50YWdlVmFsdWUgPSAoeW91dGhQZXJjZW50YWdlOiBzdHJpbmcpOiBudW1iZXIgPT4ge1xyXG4gICAgc3dpdGNoICh5b3V0aFBlcmNlbnRhZ2UpIHtcclxuICAgICAgY2FzZSBcImFib3ZlLTc1JVwiOiByZXR1cm4gODcuNTtcclxuICAgICAgY2FzZSBcIjUxLTc1JVwiOiByZXR1cm4gNjM7XHJcbiAgICAgIGNhc2UgXCIyNi01MCVcIjogcmV0dXJuIDM4O1xyXG4gICAgICBjYXNlIFwiMC0yNSVcIjogcmV0dXJuIDEyLjU7XHJcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAwO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIFNlY3Rpb24gdGl0bGVzIChzaG91bGQgbWF0Y2ggYmFja2VuZCBzZWN0aW9uIG51bWJlcnMgMy0xMSlcclxuICAvLyBTZWN0aW9uIG1heCBzY29yZXMgKGZyb20gdXNlQXNzZXNzbWVudC50cylcclxuICBjb25zdCBzZWN0aW9uTWF4U2NvcmVzOiB7IFtzZWN0aW9uOiBudW1iZXJdOiBudW1iZXIgfSA9IHtcclxuICAgIDM6IDE0LFxyXG4gICAgNDogNCxcclxuICAgIDU6IDExLFxyXG4gICAgNjogOSxcclxuICAgIDc6IDcsXHJcbiAgICA4OiA4LFxyXG4gICAgOTogMTEsXHJcbiAgICAxMDogMTYsXHJcbiAgICAxMTogMTAsXHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldFNlY3Rpb25MaXN0KFtcclxuICAgICAgeyBpZDogMywgdGl0bGU6IFwiTGVhZGVyc2hpcFwiIH0sXHJcbiAgICAgIHsgaWQ6IDQsIHRpdGxlOiBcIk9yZ2FuaXphdGlvbmFsIFN0cnVjdHVyZVwiIH0sXHJcbiAgICAgIHsgaWQ6IDUsIHRpdGxlOiBcIk1hbmFnZW1lbnRcIiB9LFxyXG4gICAgICB7IGlkOiA2LCB0aXRsZTogXCJXb3JrZXIgUGFydGljaXBhdGlvblwiIH0sXHJcbiAgICAgIHsgaWQ6IDcsIHRpdGxlOiBcIkN1bHR1cmUgYW5kIEdlbmRlclwiIH0sXHJcbiAgICAgIHsgaWQ6IDgsIHRpdGxlOiBcIkNvbGxlY3RpdmUgQmFyZ2FpbmluZ1wiIH0sXHJcbiAgICAgIHsgaWQ6IDksIHRpdGxlOiBcIk1lbWJlciBFbmdhZ2VtZW50XCIgfSxcclxuICAgICAgeyBpZDogMTAsIHRpdGxlOiBcIkZpbmFuY2lhbCBTdGFiaWxpdHlcIiB9LFxyXG4gICAgICB7IGlkOiAxMSwgdGl0bGU6IFwiQXVkaXQgJiBDb21wbGlhbmNlXCIgfSxcclxuICAgIF0pO1xyXG4gIH0sIFtdKTtcclxuICAvLyBGZXRjaCBhbGwgZmVkZXJhdGlvbnNcclxuICBjb25zdCBmZXRjaEZlZGVyYXRpb25zID0gYXN5bmMgKHNob3dSZWZyZXNoVG9hc3QgPSBmYWxzZSkgPT4ge1xyXG4gICAgaWYgKHNob3dSZWZyZXNoVG9hc3QpIHNldFJlZnJlc2hpbmcodHJ1ZSk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gVGhpcyB3b3VsZCBiZSByZXBsYWNlZCB3aXRoIHlvdXIgYWN0dWFsIEFQSSBlbmRwb2ludFxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2ZlZGVyYXRpb25zJyk7XHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGZlZGVyYXRpb25zJyk7XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xyXG4gICAgICBzZXRGZWRlcmF0aW9ucyhkYXRhLmZlZGVyYXRpb25zIHx8IFtdKTtcclxuXHJcbiAgICAgIC8vIENhbGN1bGF0ZSBhdmVyYWdlc1xyXG4gICAgICBpZiAoKGRhdGEuZmVkZXJhdGlvbnMgfHwgW10pLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBjb25zdCBwcmVzaWRlbnRBZ2VzID0gZGF0YS5mZWRlcmF0aW9ucy5maWx0ZXIoKGZlZDogUGFydGlhbDxGZWRlcmF0aW9uPikgPT4gZmVkLnByZXNpZGVudF9hZ2UpLm1hcCgoZmVkOiBQYXJ0aWFsPEZlZGVyYXRpb24+KSA9PiBmZWQucHJlc2lkZW50X2FnZSBhcyBudW1iZXIpO1xyXG4gICAgICAgIGNvbnN0IHNlY3JldGFyeUFnZXMgPSBkYXRhLmZlZGVyYXRpb25zLmZpbHRlcigoZmVkOiBQYXJ0aWFsPEZlZGVyYXRpb24+KSA9PiBmZWQuc2VjcmV0YXJ5X2FnZSkubWFwKChmZWQ6IFBhcnRpYWw8RmVkZXJhdGlvbj4pID0+IGZlZC5zZWNyZXRhcnlfYWdlIGFzIG51bWJlcik7XHJcbiAgICAgICAgY29uc3QgYXZnUHJlc2lkZW50QWdlID0gcHJlc2lkZW50QWdlcy5sZW5ndGggPiAwXHJcbiAgICAgICAgICA/IE1hdGgucm91bmQocHJlc2lkZW50QWdlcy5yZWR1Y2UoKHN1bTogbnVtYmVyLCBhZ2U6IG51bWJlcikgPT4gc3VtICsgYWdlLCAwKSAvIHByZXNpZGVudEFnZXMubGVuZ3RoKVxyXG4gICAgICAgICAgOiAwO1xyXG4gICAgICAgIGNvbnN0IGF2Z1NlY3JldGFyeUFnZSA9IHNlY3JldGFyeUFnZXMubGVuZ3RoID4gMFxyXG4gICAgICAgICAgPyBNYXRoLnJvdW5kKHNlY3JldGFyeUFnZXMucmVkdWNlKChzdW06IG51bWJlciwgYWdlOiBudW1iZXIpID0+IHN1bSArIGFnZSwgMCkgLyBzZWNyZXRhcnlBZ2VzLmxlbmd0aClcclxuICAgICAgICAgIDogMDtcclxuICAgICAgICBzZXRBdmVyYWdlUHJlc2lkZW50QWdlKGF2Z1ByZXNpZGVudEFnZSk7XHJcbiAgICAgICAgc2V0QXZlcmFnZVNlY3JldGFyeUFnZShhdmdTZWNyZXRhcnlBZ2UpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBTZXQgc2NvcmUgYW5hbHl0aWNzXHJcbiAgICAgIHNldEF2ZXJhZ2VUb3RhbFNjb3JlKGRhdGEuYXZnVG90YWxTY29yZSB8fCAwKTtcclxuICAgICAgc2V0QXZlcmFnZVBlcmNlbnRhZ2UoZGF0YS5hdmdQZXJjZW50YWdlIHx8IDApO1xyXG4gICAgICBzZXRIaWdoZXN0U2NvcmUoZGF0YS5oaWdoZXN0U2NvcmUgfHwgMCk7XHJcbiAgICAgIHNldExvd2VzdFNjb3JlKGRhdGEubG93ZXN0U2NvcmUgfHwgMCk7XHJcbiAgICAgIHNldEhpZ2hlc3RQZXJjZW50YWdlKGRhdGEuaGlnaGVzdFBlcmNlbnRhZ2UgfHwgMCk7XHJcbiAgICAgIHNldExvd2VzdFBlcmNlbnRhZ2UoZGF0YS5sb3dlc3RQZXJjZW50YWdlIHx8IDApO1xyXG4gICAgICBzZXRBdmVyYWdlWW91dGhQZXJjZW50YWdlKGRhdGEuYXZnWW91dGhQZXJjZW50YWdlIHx8IDApO1xyXG4gICAgICBzZXRBdmdTZWN0aW9uU2NvcmVzKGRhdGEuYXZnU2VjdGlvblNjb3JlcyB8fCB7fSk7XHJcbiAgICAgIHNldE1pblNlY3Rpb25TY29yZXMoZGF0YS5taW5TZWN0aW9uU2NvcmVzIHx8IHt9KTtcclxuICAgICAgc2V0TWF4U2VjdGlvblNjb3JlcyhkYXRhLm1heFNlY3Rpb25TY29yZXMgfHwge30pO1xyXG5cclxuICAgICAgaWYgKHNob3dSZWZyZXNoVG9hc3QpIHtcclxuICAgICAgICB0b2FzdC5zdWNjZXNzKFwiRGF0YSByZWZyZXNoZWQgc3VjY2Vzc2Z1bGx5IVwiKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgZmVkZXJhdGlvbnM6JywgZXJyb3IpO1xyXG4gICAgICBpZiAoc2hvd1JlZnJlc2hUb2FzdCkge1xyXG4gICAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIHJlZnJlc2ggZGF0YVwiKTtcclxuICAgICAgfVxyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgICAgc2V0UmVmcmVzaGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoRmVkZXJhdGlvbnMoKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIEhhbmRsZSBmZWRlcmF0aW9uIHNlbGVjdGlvbiBjaGFuZ2VcclxuICBjb25zdCBoYW5kbGVGZWRlcmF0aW9uQ2hhbmdlID0gKHZhbHVlOiBzdHJpbmcpID0+IHtcclxuICAgIHNldFNlbGVjdGVkRmVkZXJhdGlvbih2YWx1ZSk7XHJcblxyXG4gICAgaWYgKHZhbHVlID09PSAnYWxsJykge1xyXG4gICAgICBzZXRDdXJyZW50RmVkZXJhdGlvbihudWxsKTtcclxuICAgICAgc2V0Q3VycmVudFNlY3Rpb25TY29yZXMoe30pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgY29uc3Qgc2VsZWN0ZWQgPSBmZWRlcmF0aW9ucy5maW5kKGZlZCA9PiBmZWQuaWQgPT09IHZhbHVlKTtcclxuICAgICAgc2V0Q3VycmVudEZlZGVyYXRpb24oc2VsZWN0ZWQgfHwgbnVsbCk7XHJcbiAgICAgIHNldEN1cnJlbnRTZWN0aW9uU2NvcmVzKHNlbGVjdGVkPy5zZWN0aW9uU2NvcmVzIHx8IHt9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBFeHBvcnQgZnVuY3Rpb25hbGl0eVxyXG4gIGNvbnN0IGV4cG9ydFRvQ1NWID0gKCkgPT4ge1xyXG4gICAgY29uc3QgaGVhZGVycyA9IFtcIk5hbWVcIiwgXCJQcmVzaWRlbnRcIiwgXCJTZWNyZXRhcnlcIiwgXCJUb3RhbCBTY29yZVwiLCBcIlBlcmNlbnRhZ2VcIiwgXCJZb3V0aCAlXCJdO1xyXG4gICAgY29uc3QgY3N2RGF0YSA9IGZpbHRlcmVkQW5kU29ydGVkRmVkZXJhdGlvbnMubWFwKGZlZCA9PiBbXHJcbiAgICAgIGZlZC5uYW1lIHx8IFwiXCIsXHJcbiAgICAgIGZlZC5wcmVzaWRlbnRfbmFtZSB8fCBcIlwiLFxyXG4gICAgICBmZWQuc2VjcmV0YXJ5X25hbWUgfHwgXCJcIixcclxuICAgICAgZmVkLnRvdGFsU2NvcmUgfHwgMCxcclxuICAgICAgZmVkLnBlcmNlbnRhZ2UgfHwgMCxcclxuICAgICAgZmVkLnlvdXRoX3BlcmNlbnRhZ2UgfHwgXCJcIlxyXG4gICAgXSk7XHJcblxyXG4gICAgY29uc3QgY3N2Q29udGVudCA9IFtoZWFkZXJzLCAuLi5jc3ZEYXRhXVxyXG4gICAgICAubWFwKHJvdyA9PiByb3cubWFwKGNlbGwgPT4gYFwiJHtjZWxsfVwiYCkuam9pbihcIixcIikpXHJcbiAgICAgIC5qb2luKFwiXFxuXCIpO1xyXG5cclxuICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY3N2Q29udGVudF0sIHsgdHlwZTogXCJ0ZXh0L2NzdlwiIH0pO1xyXG4gICAgY29uc3QgdXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTtcclxuICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiYVwiKTtcclxuICAgIGEuaHJlZiA9IHVybDtcclxuICAgIGEuZG93bmxvYWQgPSBgZmVkZXJhdGlvbnMtJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0uY3N2YDtcclxuICAgIGEuY2xpY2soKTtcclxuICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcclxuICAgIHRvYXN0LnN1Y2Nlc3MoXCJEYXRhIGV4cG9ydGVkIHN1Y2Nlc3NmdWxseSFcIik7XHJcbiAgfTtcclxuXHJcbiAgLy8gUGVyZm9ybWFuY2UgbWV0cmljc1xyXG4gIGNvbnN0IHBlcmZvcm1hbmNlTWV0cmljcyA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgY29uc3QgdG90YWwgPSBmZWRlcmF0aW9ucy5sZW5ndGg7XHJcbiAgICBjb25zdCBoaWdoUGVyZm9ybWVycyA9IGZlZGVyYXRpb25zLmZpbHRlcihmID0+IChmLnBlcmNlbnRhZ2UgfHwgMCkgPj0gNzUpLmxlbmd0aDtcclxuICAgIGNvbnN0IG1lZGl1bVBlcmZvcm1lcnMgPSBmZWRlcmF0aW9ucy5maWx0ZXIoZiA9PiAoZi5wZXJjZW50YWdlIHx8IDApID49IDUwICYmIChmLnBlcmNlbnRhZ2UgfHwgMCkgPCA3NSkubGVuZ3RoO1xyXG4gICAgY29uc3QgbG93UGVyZm9ybWVycyA9IGZlZGVyYXRpb25zLmZpbHRlcihmID0+IChmLnBlcmNlbnRhZ2UgfHwgMCkgPCA1MCkubGVuZ3RoO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIHRvdGFsLFxyXG4gICAgICBoaWdoUGVyZm9ybWVycyxcclxuICAgICAgbWVkaXVtUGVyZm9ybWVycyxcclxuICAgICAgbG93UGVyZm9ybWVycyxcclxuICAgICAgaGlnaFBlcmNlbnRhZ2U6IHRvdGFsID4gMCA/IE1hdGgucm91bmQoKGhpZ2hQZXJmb3JtZXJzIC8gdG90YWwpICogMTAwKSA6IDAsXHJcbiAgICAgIG1lZGl1bVBlcmNlbnRhZ2U6IHRvdGFsID4gMCA/IE1hdGgucm91bmQoKG1lZGl1bVBlcmZvcm1lcnMgLyB0b3RhbCkgKiAxMDApIDogMCxcclxuICAgICAgbG93UGVyY2VudGFnZTogdG90YWwgPiAwID8gTWF0aC5yb3VuZCgobG93UGVyZm9ybWVycyAvIHRvdGFsKSAqIDEwMCkgOiAwXHJcbiAgICB9O1xyXG4gIH0sIFtmZWRlcmF0aW9uc10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLXNjcmVlbiBmbGV4LWNvbCBiZy1iYWNrZ3JvdW5kLzUwXCI+XHJcbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwic3RpY2t5IHRvcC0wIHotNDAgYm9yZGVyLWIgYmctYmFja2dyb3VuZC85NSBiYWNrZHJvcC1ibHVyIHN1cHBvcnRzLVtiYWNrZHJvcC1maWx0ZXJdOmJnLWJhY2tncm91bmQvNjBcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBweC00IG1kOnB4LTYgZmxleCBoLTE2IGl0ZW1zLWNlbnRlciBweS00XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XHJcbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYXNzZXNzbWVudFwiPlxyXG4gICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cImljb25cIiBjbGFzc05hbWU9XCJyb3VuZGVkLWZ1bGxcIj5cclxuICAgICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XHJcbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICA8TGF5b3V0RGFzaGJvYXJkIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cclxuICAgICAgICAgICAgICAgIEFkbWluIERhc2hib2FyZFxyXG4gICAgICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BbGwgRmVkZXJhdGlvbnMgT3ZlcnZpZXc8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLWF1dG8gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgc206Z2FwLTRcIj5cclxuICAgICAgICAgICAgPEJ1dHRvbiBcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBmZXRjaEZlZGVyYXRpb25zKHRydWUpfSBcclxuICAgICAgICAgICAgICBkaXNhYmxlZD17cmVmcmVzaGluZ31cclxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXHJcbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtci0yXCJcclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPXtgaC00IHctNCBtci0yICR7cmVmcmVzaGluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gLz5cclxuICAgICAgICAgICAgICBSZWZyZXNoXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2V4cG9ydFRvQ1NWfSB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cIm1yLTJcIj5cclxuICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICBFeHBvcnQgQ1NWXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8VGhlbWVUb2dnbGUgLz5cclxuICAgICAgICAgICAgPFVzZXJOYXYgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2hlYWRlcj5cclxuXHJcbiAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBjb250YWluZXIgcHktNiBweC00IG1kOnB4LTYgbWF4LXctN3hsIG14LWF1dG9cIj5cclxuICAgICAgICA8VGFicyB2YWx1ZT17YWN0aXZlVGFifSBvblZhbHVlQ2hhbmdlPXtzZXRBY3RpdmVUYWJ9IGNsYXNzTmFtZT1cInctZnVsbFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IGZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQgaXRlbXMtc3RhcnQgc206aXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTUgbWQ6dy1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwib3ZlcnZpZXdcIj5PdmVydmlldzwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiYW5hbHl0aWNzXCI+QW5hbHl0aWNzPC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJxYS1zdGF0c1wiPlEmQSBTdGF0czwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwicmVwb3J0c1wiPlJlcG9ydHM8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImZlZGVyYXRpb25zXCI+RmVkZXJhdGlvbnM8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8L1RhYnNMaXN0PlxyXG5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yIGZsZXgtd3JhcFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cclxuICAgICAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0yIHRvcC0yLjUgaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIGZlZGVyYXRpb25zLi4uXCJcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBsLTggdy1bMjAwcHhdXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17ZmlsdGVyQnl9IG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZTogYW55KSA9PiBzZXRGaWx0ZXJCeSh2YWx1ZSl9PlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1bMTMwcHhdXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxGaWx0ZXIgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJhbGxcIj5BbGwgU2NvcmVzPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImhpZ2hcIj5IaWdoICg3NSUrKTwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJtZWRpdW1cIj5NZWRpdW0gKDUwLTc0JSk8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwibG93XCI+TG93ICgmbHQ7NTAlKTwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cclxuICAgICAgICAgICAgICA8L1NlbGVjdD5cclxuXHJcbiAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17c29ydEJ5fSBvblZhbHVlQ2hhbmdlPXsodmFsdWU6IGFueSkgPT4gc2V0U29ydEJ5KHZhbHVlKX0+XHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlciBjbGFzc05hbWU9XCJ3LVsxMjBweF1cIj5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJzY29yZVwiPlNjb3JlPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIm5hbWVcIj5OYW1lPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cInBlcmNlbnRhZ2VcIj5QZXJjZW50YWdlPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cInlvdXRoXCI+WW91dGggJTwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cclxuICAgICAgICAgICAgICA8L1NlbGVjdD5cclxuXHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcclxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNvcnRPcmRlcihzb3J0T3JkZXIgPT09IFwiYXNjXCIgPyBcImRlc2NcIiA6IFwiYXNjXCIpfVxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHtzb3J0T3JkZXIgPT09IFwiYXNjXCIgPyA8U29ydEFzYyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz4gOiA8U29ydERlc2MgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cIm92ZXJ2aWV3XCIgY2xhc3NOYW1lPVwibXQtMFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNiBhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICAgICAgey8qIFRpdGxlIGFuZCBMb2dvcyBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTUwIGRhcms6ZnJvbS1ibHVlLTk1MC8yMCBkYXJrOnRvLWluZGlnby05NTAvMjAgcm91bmRlZC1sZyBwLTZcIj5cclxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItNFwiPlN1cHBvcnQgZm9yIEVmZmVjdGl2ZSBhbmQgSW5jbHVzaXZlIFRyYWRlIFVuaW9ucyBpbiBCYW5nbGFkZXNoPC9oMT5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtOCBmbGV4LXdyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCJodHRwczovL3Jlcy5jbG91ZGluYXJ5LmNvbS9kcmFrY3l5cmkvaW1hZ2UvdXBsb2FkL2dlcm1hbl9jb29wZXJhdGlvbl9iYW5nbGFkZXNoX2llM3Ricy5wbmdcIiBhbHQ9XCJHZXJtYW4gQ29vcGVyYXRpb24gQmFuZ2xhZGVzaFwiIGNsYXNzTmFtZT1cImgtMTYgdy1hdXRvXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCJodHRwczovL3Jlcy5jbG91ZGluYXJ5LmNvbS9kcmFrY3l5cmkvaW1hZ2UvdXBsb2FkL0ludGVybmF0aW9uYWxfTGFib3VyX09yZ2FuaXphdGlvbl9seWl4YWQucG5nXCIgYWx0PVwiSW50ZXJuYXRpb25hbCBMYWJvdXIgT3JnYW5pemF0aW9uXCIgY2xhc3NOYW1lPVwiaC0xNiB3LWF1dG9cIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cImh0dHBzOi8vcmVzLmNsb3VkaW5hcnkuY29tL2RyYWtjeXlyaS9pbWFnZS91cGxvYWQvZ3Jvd2luZy10b2dldGhlci1vcGNfamlqNWZwLnBuZ1wiIGFsdD1cIkdyb3dpbmcgVG9nZXRoZXIgT1BDXCIgY2xhc3NOYW1lPVwiaC0xNiB3LWF1dG9cIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8aW1nIHNyYz1cImh0dHBzOi8vcmVzLmNsb3VkaW5hcnkuY29tL2RyYWtjeXlyaS9pbWFnZS91cGxvYWQvZ292ZXJubWVudC1vZi10aGUtcGVyb3BsZXMtcmVwdWJsaWMtb2YtYmFuZ2xhZGVzaF9xZ2hsa3EucG5nXCIgYWx0PVwiR292ZXJubWVudCBvZiB0aGUgcGVvcGxlJ3MgcmVwdWJsaWMgb2YgQmFuZ2xhZGVzaFwiIGNsYXNzTmFtZT1cImgtMTYgdy1hdXRvXCIgLz5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICB7LyogUXVpY2sgU3RhdHMgR3JpZCAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QXdhcmQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXllbGxvdy01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgQXZlcmFnZSBQZXJmb3JtYW5jZVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnlcIj57YXZlcmFnZVBlcmNlbnRhZ2V9JTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+T3ZlcmFsbCBhc3Nlc3NtZW50IHNjb3JlPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMiBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9e2F2ZXJhZ2VQZXJjZW50YWdlID49IDc1ID8gXCJkZWZhdWx0XCIgOiBhdmVyYWdlUGVyY2VudGFnZSA+PSA1MCA/IFwic2Vjb25kYXJ5XCIgOiBcImRlc3RydWN0aXZlXCJ9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7YXZlcmFnZVBlcmNlbnRhZ2UgPj0gNzUgPyBcIkV4Y2VsbGVudFwiIDogYXZlcmFnZVBlcmNlbnRhZ2UgPj0gNTAgPyBcIkdvb2RcIiA6IFwiTmVlZHMgSW1wcm92ZW1lbnRcIn1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGcgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxVc2VycyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIFlvdXRoIEVuZ2FnZW1lbnRcclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57YXZlcmFnZVlvdXRoUGVyY2VudGFnZX0lPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BdmVyYWdlIHlvdXRoIHJlcHJlc2VudGF0aW9uPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7TWF0aC5taW4oYXZlcmFnZVlvdXRoUGVyY2VudGFnZSwgMTAwKX0lYCB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cclxuXHJcbiAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgU2NvcmUgUmFuZ2VcclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkhpZ2hlc3Q6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNjAwXCI+e2hpZ2hlc3RQZXJjZW50YWdlfSU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkxvd2VzdDo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1yZWQtNjAwXCI+e2xvd2VzdFBlcmNlbnRhZ2V9JTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+UmFuZ2U6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e2hpZ2hlc3RQZXJjZW50YWdlIC0gbG93ZXN0UGVyY2VudGFnZX0lPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cclxuXHJcbiAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJhbmFseXRpY3NcIiBjbGFzc05hbWU9XCJtdC0wXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04IGFuaW1hdGUtaW4gZmFkZS1pbiBkdXJhdGlvbi0zMDBcIj5cclxuICAgICAgICAgICAgICA8RmVkZXJhdGlvbkFuYWx5dGljc0NoYXJ0XHJcbiAgICAgICAgICAgICAgICBmZWRlcmF0aW9ucz17ZmVkZXJhdGlvbnN9XHJcbiAgICAgICAgICAgICAgICBzZWxlY3RlZEZlZGVyYXRpb249e3NlbGVjdGVkRmVkZXJhdGlvbn1cclxuICAgICAgICAgICAgICAgIG9uU2VsZWN0RmVkZXJhdGlvbj17aGFuZGxlRmVkZXJhdGlvbkNoYW5nZX1cclxuICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICA8U2VjdGlvbkF2ZXJhZ2VDaGFydFxyXG4gICAgICAgICAgICAgICAgYXZnU2VjdGlvblNjb3Jlcz17YXZnU2VjdGlvblNjb3Jlc31cclxuICAgICAgICAgICAgICAgIG1pblNlY3Rpb25TY29yZXM9e21pblNlY3Rpb25TY29yZXN9XHJcbiAgICAgICAgICAgICAgICBtYXhTZWN0aW9uU2NvcmVzPXttYXhTZWN0aW9uU2NvcmVzfVxyXG4gICAgICAgICAgICAgICAgc2VjdGlvbkxpc3Q9e3NlY3Rpb25MaXN0fVxyXG4gICAgICAgICAgICAgICAgc2VjdGlvbk1heFNjb3Jlcz17c2VjdGlvbk1heFNjb3Jlc31cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcblxyXG4gICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwicmVwb3J0c1wiIGNsYXNzTmFtZT1cIm10LTBcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTggYW5pbWF0ZS1pbiBmYWRlLWluIGR1cmF0aW9uLTMwMFwiPlxyXG4gICAgICAgICAgICAgIDxZb3V0aFJlcHJlc2VudGF0aW9uQ2hhcnQgZmVkZXJhdGlvbnM9e2ZlZGVyYXRpb25zfSAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcblxyXG4gICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiZmVkZXJhdGlvbnNcIiBjbGFzc05hbWU9XCJtdC0wXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1pbiBmYWRlLWluIGR1cmF0aW9uLTMwMFwiPlxyXG4gICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIEZlZGVyYXRpb24gRGlyZWN0b3J5XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgIEludGVyYWN0aXZlIHRhYmxlIHNob3dpbmcgYWxsIGZlZGVyYXRpb25zIHdpdGggdGhlaXIgcGVyZm9ybWFuY2UgbWV0cmljc1xyXG4gICAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAge2lzTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge1sxLCAyLCAzLCA0LCA1XS5tYXAoKGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBhbmltYXRlLXB1bHNlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEyIHctMTIgYmctbXV0ZWQgcm91bmRlZC1mdWxsXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTIgZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1tdXRlZCByb3VuZGVkIHctMS80XCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZCByb3VuZGVkIHctMS8yXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggdy0xNiBiZy1tdXRlZCByb3VuZGVkXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEFuZFNvcnRlZEZlZGVyYXRpb25zLm1hcCgoZmVkZXJhdGlvbiwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj17YC9mZWRlcmF0aW9uLyR7ZmVkZXJhdGlvbi5pZH1gfSBrZXk9e2ZlZGVyYXRpb24uaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNCBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGN1cnNvci1wb2ludGVyIGFuaW1hdGUtaW4gZmFkZS1pbiBzbGlkZS1pbi1mcm9tLWJvdHRvbS0yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiBgJHtpbmRleCAqIDUwfW1zYCB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEyIHctMTIgYmctcHJpbWFyeS8xMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1wcmltYXJ5XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPntmZWRlcmF0aW9uLm5hbWV9PC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00IHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5QcmVzaWRlbnQ6IHtmZWRlcmF0aW9uLnByZXNpZGVudF9uYW1lIHx8IFwiTi9BXCJ9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+U2VjcmV0YXJ5OiB7ZmVkZXJhdGlvbi5zZWNyZXRhcnlfbmFtZSB8fCBcIk4vQVwifTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHJpbWFyeVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2ZlZGVyYXRpb24ucGVyY2VudGFnZSB8fCAwfSVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVkZXJhdGlvbi50b3RhbFNjb3JlIHx8IDB9L3tmZWRlcmF0aW9uLm1heFNjb3JlIHx8IDB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDApID49IDc1ID8gXCJkZWZhdWx0XCIgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGZlZGVyYXRpb24ucGVyY2VudGFnZSB8fCAwKSA+PSA1MCA/IFwic2Vjb25kYXJ5XCIgOiBcImRlc3RydWN0aXZlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWwtMlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7KGZlZGVyYXRpb24ucGVyY2VudGFnZSB8fCAwKSA+PSA3NSA/IFwiSGlnaFwiIDpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGZlZGVyYXRpb24ucGVyY2VudGFnZSB8fCAwKSA+PSA1MCA/IFwiTWVkaXVtXCIgOiBcIkxvd1wifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2ZlZGVyYXRpb24ueW91dGhfcGVyY2VudGFnZSB8fCBcIk4vQVwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Zb3V0aDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFByb2dyZXNzIGJhciAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNTAwICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDApID49IDc1ID8gJ2JnLWdyZWVuLTUwMCcgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGZlZGVyYXRpb24ucGVyY2VudGFnZSB8fCAwKSA+PSA1MCA/ICdiZy15ZWxsb3ctNTAwJyA6ICdiZy1yZWQtNTAwJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHtNYXRoLm1pbihmZWRlcmF0aW9uLnBlcmNlbnRhZ2UgfHwgMCwgMTAwKX0lYCB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQW5kU29ydGVkRmVkZXJhdGlvbnMubGVuZ3RoID09PSAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCdWlsZGluZzIgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBteC1hdXRvIG1iLTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPk5vIGZlZGVyYXRpb25zIGZvdW5kPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyeSBhZGp1c3RpbmcgeW91ciBzZWFyY2ggb3IgZmlsdGVyIGNyaXRlcmlhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxyXG4gICAgICAgIDwvVGFicz5cclxuXHJcbiAgICAgICAge2lzTG9hZGluZyA/IChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNiBtZDpncmlkLWNvbHMtMiBtdC04XCI+XHJcbiAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cInNoYWRvdy1tZCBhbmltYXRlLXB1bHNlXCI+XHJcbiAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPkxvYWRpbmcuLi48L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTIwIGJnLW11dGVkIHJvdW5kZWRcIj48L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC02IG1kOmdyaWQtY29scy0yIG10LThcIj5cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3ctbGcgaC1mdWxsIG92ZXJmbG93LWhpZGRlbiBib3JkZXItbXV0ZWQvNDBcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yIGJnLW11dGVkLzEwXCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQteGwgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPEJ1aWxkaW5nMiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHJpbWFyeVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIExlYWRlcnNoaXAgT3ZlcnZpZXdcclxuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgICAgQXZlcmFnZSBsZWFkZXJzaGlwIGRlbW9ncmFwaGljcyBhY3Jvc3MgYWxsIGZlZGVyYXRpb25zXHJcbiAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB0LTZcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwLzEwIHAtMi41IHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFVzZXJDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+QXZlcmFnZSBBZ2VzPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtMiBtdC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctbXV0ZWQvMzAgcC0yIHJvdW5kZWQtbWRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlByZXNpZGVudDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2F2ZXJhZ2VQcmVzaWRlbnRBZ2V9IHllYXJzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1tdXRlZC8zMCBwLTIgcm91bmRlZC1tZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+U2VjcmV0YXJ5PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXZlcmFnZVNlY3JldGFyeUFnZX0geWVhcnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzaGFkb3ctbGcgaC1mdWxsIG92ZXJmbG93LWhpZGRlbiBib3JkZXItbXV0ZWQvNDBcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yIGJnLW11dGVkLzEwXCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQteGwgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHJpbWFyeVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIFBlcmZvcm1hbmNlIFN1bW1hcnlcclxuICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICAgICAgS2V5IHBlcmZvcm1hbmNlIGluZGljYXRvcnMgYWNyb3NzIGFsbCBmZWRlcmF0aW9uc1xyXG4gICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtMyBiZy1ncmVlbi01MCBkYXJrOmJnLWdyZWVuLTk1MC8yMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPnthdmVyYWdlVG90YWxTY29yZX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BdmcgU2NvcmU8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtMyBiZy1ibHVlLTUwIGRhcms6YmctYmx1ZS05NTAvMjAgcm91bmRlZC1sZ1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPnthdmVyYWdlUGVyY2VudGFnZX0lPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+QXZnIFBlcmNlbnRhZ2U8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+SGlnaGVzdCBTY29yZTo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNjAwXCI+e2hpZ2hlc3RTY29yZX0gKHtoaWdoZXN0UGVyY2VudGFnZX0lKTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkxvd2VzdCBTY29yZTo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtcmVkLTYwMFwiPntsb3dlc3RTY29yZX0gKHtsb3dlc3RQZXJjZW50YWdlfSUpPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+WW91dGggRW5nYWdlbWVudDo8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtcHVycGxlLTYwMFwiPnthdmVyYWdlWW91dGhQZXJjZW50YWdlfSU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9tYWluPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZU1lbW8iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQ2FyZERlc2NyaXB0aW9uIiwiVXNlck5hdiIsIlRoZW1lVG9nZ2xlIiwiTGluayIsIkJ1dHRvbiIsIklucHV0IiwiQmFkZ2UiLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiQXJyb3dMZWZ0IiwiQnVpbGRpbmcyIiwiVXNlckNpcmNsZSIsIlNlYXJjaCIsIkZpbHRlciIsIkRvd25sb2FkIiwiQmFyQ2hhcnQzIiwiVXNlcnMiLCJBd2FyZCIsIlRhcmdldCIsIlJlZnJlc2hDdyIsIkV5ZSIsIlNvcnRBc2MiLCJTb3J0RGVzYyIsIkxheW91dERhc2hib2FyZCIsIlNlbGVjdCIsIlNlbGVjdENvbnRlbnQiLCJTZWxlY3RJdGVtIiwiU2VsZWN0VHJpZ2dlciIsIlNlbGVjdFZhbHVlIiwidXNlQXV0aCIsIkZlZGVyYXRpb25BbmFseXRpY3NDaGFydCIsIlNlY3Rpb25BdmVyYWdlQ2hhcnQiLCJZb3V0aFJlcHJlc2VudGF0aW9uQ2hhcnQiLCJ0b2FzdCIsIkFsbEZlZGVyYXRpb25QYWdlIiwidXNlciIsImZlZGVyYXRpb25zIiwic2V0RmVkZXJhdGlvbnMiLCJzZWxlY3RlZEZlZGVyYXRpb24iLCJzZXRTZWxlY3RlZEZlZGVyYXRpb24iLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNvcnRCeSIsInNldFNvcnRCeSIsInNvcnRPcmRlciIsInNldFNvcnRPcmRlciIsImZpbHRlckJ5Iiwic2V0RmlsdGVyQnkiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJyZWZyZXNoaW5nIiwic2V0UmVmcmVzaGluZyIsImF2ZXJhZ2VQcmVzaWRlbnRBZ2UiLCJzZXRBdmVyYWdlUHJlc2lkZW50QWdlIiwiYXZlcmFnZVNlY3JldGFyeUFnZSIsInNldEF2ZXJhZ2VTZWNyZXRhcnlBZ2UiLCJhdmVyYWdlVG90YWxTY29yZSIsInNldEF2ZXJhZ2VUb3RhbFNjb3JlIiwiYXZlcmFnZVBlcmNlbnRhZ2UiLCJzZXRBdmVyYWdlUGVyY2VudGFnZSIsImhpZ2hlc3RTY29yZSIsInNldEhpZ2hlc3RTY29yZSIsImxvd2VzdFNjb3JlIiwic2V0TG93ZXN0U2NvcmUiLCJoaWdoZXN0UGVyY2VudGFnZSIsInNldEhpZ2hlc3RQZXJjZW50YWdlIiwibG93ZXN0UGVyY2VudGFnZSIsInNldExvd2VzdFBlcmNlbnRhZ2UiLCJhdmVyYWdlWW91dGhQZXJjZW50YWdlIiwic2V0QXZlcmFnZVlvdXRoUGVyY2VudGFnZSIsImF2Z1NlY3Rpb25TY29yZXMiLCJzZXRBdmdTZWN0aW9uU2NvcmVzIiwibWluU2VjdGlvblNjb3JlcyIsInNldE1pblNlY3Rpb25TY29yZXMiLCJtYXhTZWN0aW9uU2NvcmVzIiwic2V0TWF4U2VjdGlvblNjb3JlcyIsInNlY3Rpb25MaXN0Iiwic2V0U2VjdGlvbkxpc3QiLCJjdXJyZW50RmVkZXJhdGlvbiIsInNldEN1cnJlbnRGZWRlcmF0aW9uIiwiY3VycmVudFNlY3Rpb25TY29yZXMiLCJzZXRDdXJyZW50U2VjdGlvblNjb3JlcyIsImZpbHRlcmVkQW5kU29ydGVkRmVkZXJhdGlvbnMiLCJmaWx0ZXJlZCIsImZpbHRlciIsImZlZCIsIm1hdGNoZXNTZWFyY2giLCJuYW1lIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInByZXNpZGVudF9uYW1lIiwic2VjcmV0YXJ5X25hbWUiLCJwZXJjZW50YWdlIiwibWF0Y2hlc0ZpbHRlciIsInNvcnQiLCJhIiwiYiIsImFWYWx1ZSIsImJWYWx1ZSIsInRvdGFsU2NvcmUiLCJnZXRZb3V0aFBlcmNlbnRhZ2VWYWx1ZSIsInlvdXRoX3BlcmNlbnRhZ2UiLCJsb2NhbGVDb21wYXJlIiwieW91dGhQZXJjZW50YWdlIiwic2VjdGlvbk1heFNjb3JlcyIsImlkIiwidGl0bGUiLCJmZXRjaEZlZGVyYXRpb25zIiwic2hvd1JlZnJlc2hUb2FzdCIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJsZW5ndGgiLCJwcmVzaWRlbnRBZ2VzIiwicHJlc2lkZW50X2FnZSIsIm1hcCIsInNlY3JldGFyeUFnZXMiLCJzZWNyZXRhcnlfYWdlIiwiYXZnUHJlc2lkZW50QWdlIiwiTWF0aCIsInJvdW5kIiwicmVkdWNlIiwic3VtIiwiYWdlIiwiYXZnU2VjcmV0YXJ5QWdlIiwiYXZnVG90YWxTY29yZSIsImF2Z1BlcmNlbnRhZ2UiLCJhdmdZb3V0aFBlcmNlbnRhZ2UiLCJzdWNjZXNzIiwiZXJyb3IiLCJjb25zb2xlIiwiaGFuZGxlRmVkZXJhdGlvbkNoYW5nZSIsInZhbHVlIiwic2VsZWN0ZWQiLCJmaW5kIiwic2VjdGlvblNjb3JlcyIsImV4cG9ydFRvQ1NWIiwiaGVhZGVycyIsImNzdkRhdGEiLCJjc3ZDb250ZW50Iiwicm93IiwiY2VsbCIsImpvaW4iLCJibG9iIiwiQmxvYiIsInR5cGUiLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZG93bmxvYWQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImNsaWNrIiwicmV2b2tlT2JqZWN0VVJMIiwicGVyZm9ybWFuY2VNZXRyaWNzIiwidG90YWwiLCJoaWdoUGVyZm9ybWVycyIsImYiLCJtZWRpdW1QZXJmb3JtZXJzIiwibG93UGVyZm9ybWVycyIsImhpZ2hQZXJjZW50YWdlIiwibWVkaXVtUGVyY2VudGFnZSIsImxvd1BlcmNlbnRhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJoZWFkZXIiLCJ2YXJpYW50Iiwic2l6ZSIsImgxIiwicCIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsIm1haW4iLCJvblZhbHVlQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJpbWciLCJzcmMiLCJhbHQiLCJzdHlsZSIsIndpZHRoIiwibWluIiwic3BhbiIsIm9uU2VsZWN0RmVkZXJhdGlvbiIsImkiLCJmZWRlcmF0aW9uIiwiaW5kZXgiLCJhbmltYXRpb25EZWxheSIsImgzIiwibWF4U2NvcmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/all-federation/page.tsx\n"));

/***/ })

});