"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/all-federation/page",{

/***/ "(app-pages-browser)/./app/all-federation/page.tsx":
/*!*************************************!*\
  !*** ./app/all-federation/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AllFederationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_UserNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserNav */ \"(app-pages-browser)/./components/UserNav.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(app-pages-browser)/./components/ui/theme-toggle.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_AuthContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AuthContext */ \"(app-pages-browser)/./components/AuthContext.tsx\");\n/* harmony import */ var _components_dashboard_FederationAnalyticsChart__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/FederationAnalyticsChart */ \"(app-pages-browser)/./components/dashboard/FederationAnalyticsChart.tsx\");\n/* harmony import */ var _components_dashboard_SectionAverageChart__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dashboard/SectionAverageChart */ \"(app-pages-browser)/./components/dashboard/SectionAverageChart.tsx\");\n/* harmony import */ var _components_dashboard_YouthRepresentationChart__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dashboard/YouthRepresentationChart */ \"(app-pages-browser)/./components/dashboard/YouthRepresentationChart.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AllFederationPage() {\n    _s();\n    const { user } = (0,_components_AuthContext__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const [federations, setFederations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFederation, setSelectedFederation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"score\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desc\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Calculated averages\n    const [averagePresidentAge, setAveragePresidentAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [averageSecretaryAge, setAverageSecretaryAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Score analytics\n    const [averageTotalScore, setAverageTotalScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [averagePercentage, setAveragePercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [highestScore, setHighestScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lowestScore, setLowestScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [highestPercentage, setHighestPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lowestPercentage, setLowestPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Youth and section analytics\n    const [averageYouthPercentage, setAverageYouthPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [avgSectionScores, setAvgSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [minSectionScores, setMinSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [maxSectionScores, setMaxSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sectionList, setSectionList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Selected federation details\n    const [currentFederation, setCurrentFederation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSectionScores, setCurrentSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Filtered and sorted federations\n    const filteredAndSortedFederations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AllFederationPage.useMemo[filteredAndSortedFederations]\": ()=>{\n            let filtered = federations.filter({\n                \"AllFederationPage.useMemo[filteredAndSortedFederations].filtered\": (fed)=>{\n                    var _fed_name, _fed_president_name, _fed_secretary_name;\n                    const matchesSearch = !searchTerm || ((_fed_name = fed.name) === null || _fed_name === void 0 ? void 0 : _fed_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_fed_president_name = fed.president_name) === null || _fed_president_name === void 0 ? void 0 : _fed_president_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_fed_secretary_name = fed.secretary_name) === null || _fed_secretary_name === void 0 ? void 0 : _fed_secretary_name.toLowerCase().includes(searchTerm.toLowerCase()));\n                    const percentage = fed.percentage || 0;\n                    const matchesFilter = filterBy === \"all\" || filterBy === \"high\" && percentage >= 75 || filterBy === \"medium\" && percentage >= 50 && percentage < 75 || filterBy === \"low\" && percentage < 50;\n                    return matchesSearch && matchesFilter;\n                }\n            }[\"AllFederationPage.useMemo[filteredAndSortedFederations].filtered\"]);\n            // Sort the filtered results\n            filtered.sort({\n                \"AllFederationPage.useMemo[filteredAndSortedFederations]\": (a, b)=>{\n                    let aValue, bValue;\n                    switch(sortBy){\n                        case \"name\":\n                            aValue = a.name || \"\";\n                            bValue = b.name || \"\";\n                            break;\n                        case \"score\":\n                            aValue = a.totalScore || 0;\n                            bValue = b.totalScore || 0;\n                            break;\n                        case \"percentage\":\n                            aValue = a.percentage || 0;\n                            bValue = b.percentage || 0;\n                            break;\n                        case \"youth\":\n                            aValue = getYouthPercentageValue(a.youth_percentage || \"0-25%\");\n                            bValue = getYouthPercentageValue(b.youth_percentage || \"0-25%\");\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (typeof aValue === \"string\") {\n                        return sortOrder === \"asc\" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n                    } else {\n                        return sortOrder === \"asc\" ? aValue - bValue : bValue - aValue;\n                    }\n                }\n            }[\"AllFederationPage.useMemo[filteredAndSortedFederations]\"]);\n            return filtered;\n        }\n    }[\"AllFederationPage.useMemo[filteredAndSortedFederations]\"], [\n        federations,\n        searchTerm,\n        sortBy,\n        sortOrder,\n        filterBy\n    ]);\n    // Helper function to convert youth percentage to numeric value\n    const getYouthPercentageValue = (youthPercentage)=>{\n        switch(youthPercentage){\n            case \"above-75%\":\n                return 87.5;\n            case \"51-75%\":\n                return 63;\n            case \"26-50%\":\n                return 38;\n            case \"0-25%\":\n                return 12.5;\n            default:\n                return 0;\n        }\n    };\n    // Section titles (should match backend section numbers 3-11)\n    // Section max scores (from useAssessment.ts)\n    const sectionMaxScores = {\n        3: 14,\n        4: 4,\n        5: 11,\n        6: 9,\n        7: 7,\n        8: 8,\n        9: 11,\n        10: 16,\n        11: 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AllFederationPage.useEffect\": ()=>{\n            setSectionList([\n                {\n                    id: 3,\n                    title: \"Leadership\"\n                },\n                {\n                    id: 4,\n                    title: \"Organizational Structure\"\n                },\n                {\n                    id: 5,\n                    title: \"Management\"\n                },\n                {\n                    id: 6,\n                    title: \"Worker Participation\"\n                },\n                {\n                    id: 7,\n                    title: \"Culture and Gender\"\n                },\n                {\n                    id: 8,\n                    title: \"Collective Bargaining\"\n                },\n                {\n                    id: 9,\n                    title: \"Member Engagement\"\n                },\n                {\n                    id: 10,\n                    title: \"Financial Stability\"\n                },\n                {\n                    id: 11,\n                    title: \"Audit & Compliance\"\n                }\n            ]);\n        }\n    }[\"AllFederationPage.useEffect\"], []);\n    // Fetch all federations\n    const fetchFederations = async function() {\n        let showRefreshToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (showRefreshToast) setRefreshing(true);\n        try {\n            // This would be replaced with your actual API endpoint\n            const response = await fetch('/api/federations');\n            if (!response.ok) throw new Error('Failed to fetch federations');\n            const data = await response.json();\n            setFederations(data.federations || []);\n            // Calculate averages\n            if ((data.federations || []).length > 0) {\n                const presidentAges = data.federations.filter((fed)=>fed.president_age).map((fed)=>fed.president_age);\n                const secretaryAges = data.federations.filter((fed)=>fed.secretary_age).map((fed)=>fed.secretary_age);\n                const avgPresidentAge = presidentAges.length > 0 ? Math.round(presidentAges.reduce((sum, age)=>sum + age, 0) / presidentAges.length) : 0;\n                const avgSecretaryAge = secretaryAges.length > 0 ? Math.round(secretaryAges.reduce((sum, age)=>sum + age, 0) / secretaryAges.length) : 0;\n                setAveragePresidentAge(avgPresidentAge);\n                setAverageSecretaryAge(avgSecretaryAge);\n            }\n            // Set score analytics\n            setAverageTotalScore(data.avgTotalScore || 0);\n            setAveragePercentage(data.avgPercentage || 0);\n            setHighestScore(data.highestScore || 0);\n            setLowestScore(data.lowestScore || 0);\n            setHighestPercentage(data.highestPercentage || 0);\n            setLowestPercentage(data.lowestPercentage || 0);\n            setAverageYouthPercentage(data.avgYouthPercentage || 0);\n            setAvgSectionScores(data.avgSectionScores || {});\n            setMinSectionScores(data.minSectionScores || {});\n            setMaxSectionScores(data.maxSectionScores || {});\n            if (showRefreshToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_15__.toast.success(\"Data refreshed successfully!\");\n            }\n        } catch (error) {\n            console.error('Error fetching federations:', error);\n            if (showRefreshToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_15__.toast.error(\"Failed to refresh data\");\n            }\n        } finally{\n            setIsLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AllFederationPage.useEffect\": ()=>{\n            fetchFederations();\n        }\n    }[\"AllFederationPage.useEffect\"], []);\n    // Handle federation selection change\n    const handleFederationChange = (value)=>{\n        setSelectedFederation(value);\n        if (value === 'all') {\n            setCurrentFederation(null);\n            setCurrentSectionScores({});\n        } else {\n            const selected = federations.find((fed)=>fed.id === value);\n            setCurrentFederation(selected || null);\n            setCurrentSectionScores((selected === null || selected === void 0 ? void 0 : selected.sectionScores) || {});\n        }\n    };\n    // Export functionality\n    const exportToCSV = ()=>{\n        const headers = [\n            \"Name\",\n            \"President\",\n            \"Secretary\",\n            \"Total Score\",\n            \"Percentage\",\n            \"Youth %\"\n        ];\n        const csvData = filteredAndSortedFederations.map((fed)=>[\n                fed.name || \"\",\n                fed.president_name || \"\",\n                fed.secretary_name || \"\",\n                fed.totalScore || 0,\n                fed.percentage || 0,\n                fed.youth_percentage || \"\"\n            ]);\n        const csvContent = [\n            headers,\n            ...csvData\n        ].map((row)=>row.map((cell)=>'\"'.concat(cell, '\"')).join(\",\")).join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"federations-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        a.click();\n        URL.revokeObjectURL(url);\n        sonner__WEBPACK_IMPORTED_MODULE_15__.toast.success(\"Data exported successfully!\");\n    };\n    // Performance metrics\n    const performanceMetrics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AllFederationPage.useMemo[performanceMetrics]\": ()=>{\n            const total = federations.length;\n            const highPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) >= 75\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            const mediumPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) >= 50 && (f.percentage || 0) < 75\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            const lowPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) < 50\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            return {\n                total,\n                highPerformers,\n                mediumPerformers,\n                lowPerformers,\n                highPercentage: total > 0 ? Math.round(highPerformers / total * 100) : 0,\n                mediumPercentage: total > 0 ? Math.round(mediumPerformers / total * 100) : 0,\n                lowPercentage: total > 0 ? Math.round(lowPerformers / total * 100) : 0\n            };\n        }\n    }[\"AllFederationPage.useMemo[performanceMetrics]\"], [\n        federations\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col bg-background/50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 md:px-6 flex h-16 items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/assessment\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Admin Dashboard\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"All Federations Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-auto flex items-center gap-2 sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: ()=>fetchFederations(true),\n                                    disabled: refreshing,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mr-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: exportToCSV,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mr-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export CSV\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserNav__WEBPACK_IMPORTED_MODULE_3__.UserNav, {}, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                        className: \"grid w-full grid-cols-5 md:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"overview\",\n                                                children: \"Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"analytics\",\n                                                children: \"Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"qa-stats\",\n                                                children: \"Q&A Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"reports\",\n                                                children: \"Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"federations\",\n                                                children: \"Federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 flex-wrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        placeholder: \"Search federations...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-8 w-[200px]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                value: filterBy,\n                                                onValueChange: (value)=>setFilterBy(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                        className: \"w-[130px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Scores\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"high\",\n                                                                children: \"High (75%+)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"medium\",\n                                                                children: \"Medium (50-74%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"low\",\n                                                                children: \"Low (<50%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                value: sortBy,\n                                                onValueChange: (value)=>setSortBy(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                        className: \"w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"score\",\n                                                                children: \"Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"name\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"percentage\",\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"youth\",\n                                                                children: \"Youth %\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\"),\n                                                children: sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 40\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 74\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"overview\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 animate-in fade-in duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"Support for Effective and Inclusive Trade Unions in Bangladesh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center gap-8 flex-wrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png\",\n                                                            alt: \"German Cooperation Bangladesh\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png\",\n                                                            alt: \"International Labour Organization\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png\",\n                                                            alt: \"Growing Together OPC\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png\",\n                                                            alt: \"Government of the people's republic of Bangladesh\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Average Performance\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-primary\",\n                                                                    children: [\n                                                                        averagePercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Overall assessment score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: averagePercentage >= 75 ? \"default\" : averagePercentage >= 50 ? \"secondary\" : \"destructive\",\n                                                                        children: averagePercentage >= 75 ? \"Excellent\" : averagePercentage >= 50 ? \"Good\" : \"Needs Improvement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Youth Engagement\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: [\n                                                                        averageYouthPercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Average youth representation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(averageYouthPercentage, 100), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Score Range\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Highest:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-green-600\",\n                                                                                children: [\n                                                                                    highestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Lowest:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-red-600\",\n                                                                                children: [\n                                                                                    lowestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Range:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: [\n                                                                                    highestPercentage - lowestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"analytics\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FederationAnalyticsChart__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            federations: federations,\n                                            selectedFederation: selectedFederation,\n                                            onSelectFederation: handleFederationChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SectionAverageChart__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            avgSectionScores: avgSectionScores,\n                                            minSectionScores: minSectionScores,\n                                            maxSectionScores: maxSectionScores,\n                                            sectionList: sectionList,\n                                            sectionMaxScores: sectionMaxScores\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"reports\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_YouthRepresentationChart__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        federations: federations\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"federations\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Federation Directory\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Interactive table showing all federations with their performance metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 animate-pulse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-12 w-12 bg-muted rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-4 bg-muted rounded w-1/4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-muted rounded w-1/2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 484,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-8 w-16 bg-muted rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        filteredAndSortedFederations.map((federation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/federation/\".concat(federation.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer animate-in fade-in slide-in-from-bottom-2\",\n                                                                    style: {\n                                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"h-6 w-6 text-primary\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                lineNumber: 503,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 502,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                    className: \"font-semibold text-lg\",\n                                                                                                    children: federation.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 506,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-4 text-sm text-muted-foreground\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"President: \",\n                                                                                                                federation.president_name || \"N/A\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                            lineNumber: 508,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"Secretary: \",\n                                                                                                                federation.secretary_name || \"N/A\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                            lineNumber: 509,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 507,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 505,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 501,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-right\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-2xl font-bold text-primary\",\n                                                                                                    children: [\n                                                                                                        federation.percentage || 0,\n                                                                                                        \"%\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 516,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: [\n                                                                                                        federation.totalScore || 0,\n                                                                                                        \"/\",\n                                                                                                        federation.maxScore || 0\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 519,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 515,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                            variant: (federation.percentage || 0) >= 75 ? \"default\" : (federation.percentage || 0) >= 50 ? \"secondary\" : \"destructive\",\n                                                                                            className: \"ml-2\",\n                                                                                            children: (federation.percentage || 0) >= 75 ? \"High\" : (federation.percentage || 0) >= 50 ? \"Medium\" : \"Low\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 524,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-medium text-green-600\",\n                                                                                                    children: federation.youth_percentage || \"N/A\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 536,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                                    children: \"Youth\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 539,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 535,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                lineNumber: 543,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 542,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 514,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full transition-all duration-500 \".concat((federation.percentage || 0) >= 75 ? 'bg-green-500' : (federation.percentage || 0) >= 50 ? 'bg-yellow-500' : 'bg-red-500'),\n                                                                                    style: {\n                                                                                        width: \"\".concat(Math.min(federation.percentage || 0, 100), \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 550,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, federation.id, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        filteredAndSortedFederations.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-2\",\n                                                                    children: \"No federations found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Try adjusting your search or filter criteria\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"shadow-md animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-20 bg-muted rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-2 bg-muted/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Leadership Overview\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Average leadership demographics across all federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-500/10 p-2.5 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 608,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-muted-foreground\",\n                                                                children: \"Average Ages\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-muted/30 p-2 rounded-md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"President\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    averagePresidentAge,\n                                                                                    \" years\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 615,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-muted/30 p-2 rounded-md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Secretary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    averageSecretaryAge,\n                                                                                    \" years\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-2 bg-muted/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Performance Summary\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Key performance indicators across all federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                    children: averageTotalScore\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Avg Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        averagePercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 650,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Avg Percentage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 649,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Highest Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-green-600\",\n                                                                    children: [\n                                                                        highestScore,\n                                                                        \" (\",\n                                                                        highestPercentage,\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 656,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Lowest Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-red-600\",\n                                                                    children: [\n                                                                        lowestScore,\n                                                                        \" (\",\n                                                                        lowestPercentage,\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Youth Engagement:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-purple-600\",\n                                                                    children: [\n                                                                        averageYouthPercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 643,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(AllFederationPage, \"Fo7Cs3et470Q19GJX6/8j65+4ms=\", false, function() {\n    return [\n        _components_AuthContext__WEBPACK_IMPORTED_MODULE_11__.useAuth\n    ];\n});\n_c = AllFederationPage;\nvar _c;\n$RefreshReg$(_c, \"AllFederationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/all-federation/page.tsx\n"));

/***/ })

});