import { NextRequest, NextResponse } from 'next/server';
import { redis, safeRedisOperation } from '@/lib/upstash';

// Function to calculate section score using the same logic as useAssessment.ts
function calculateSectionScore(assessment: any, sectionNumber: number): number {
  if (!assessment) return 0;

  let sectionScore = 0;

  // Section III scoring
  if (sectionNumber === 3) {
    if (assessment.vision_mission_status === '4') sectionScore += 3;
    else if (assessment.vision_mission_status === '3') sectionScore += 2;
    else if (assessment.vision_mission_status === '2') sectionScore += 1;

    // Yes/No/NA questions scoring
    if (assessment.vision_posted === 'yes') sectionScore += 1;
    if (assessment.vision_in_documents === 'yes') sectionScore += 1;
    if (assessment.vision_for_planning === 'yes') sectionScore += 1;

    if (assessment.decision_making === 'a') sectionScore += 3;
    else if (assessment.decision_making === 'b') sectionScore += 2;
    else if (assessment.decision_making === 'c') sectionScore += 1;

    if (assessment.emerging_issues_handling === 'a') sectionScore += 3;
    else if (assessment.emerging_issues_handling === 'b') sectionScore += 2;
    else if (assessment.emerging_issues_handling === 'c') sectionScore += 1;

    if (assessment.leadership_development_plan === 'a') sectionScore += 3;
    else if (assessment.leadership_development_plan === 'b') sectionScore += 2;
    else if (assessment.leadership_development_plan === 'c') sectionScore += 1;
  }

  // Section IV scoring
  else if (sectionNumber === 4) {
    if (assessment.organizational_structure === 'a') sectionScore += 2;
    else if (assessment.organizational_structure === 'b') sectionScore += 1;

    if (assessment.roles_defined === 'a') sectionScore += 2;
    else if (assessment.roles_defined === 'b') sectionScore += 1;
  }

  // Section V scoring
  else if (sectionNumber === 5) {
    if (assessment.management_approach === 'a') sectionScore += 2;
    else if (assessment.management_approach === 'c') sectionScore += 1;

    if (assessment.authority_delegation === 'a') sectionScore += 2;
    else if (assessment.authority_delegation === 'c') sectionScore += 1;

    if (assessment.decision_making_process === '4') sectionScore += 3;
    else if (assessment.decision_making_process === '3') sectionScore += 2;
    else if (assessment.decision_making_process === '2') sectionScore += 1;

    if (assessment.deputy_availability === 'a') sectionScore += 2;
    else if (assessment.deputy_availability === 'b') sectionScore += 1;

    if (assessment.transition_plan === 'a') sectionScore += 2;
    else if (assessment.transition_plan === 'b') sectionScore += 1;
  }

  // Section VI scoring
  else if (sectionNumber === 6) {
    if (assessment.workers_involvement_level === '4') sectionScore += 4;
    else if (assessment.workers_involvement_level === '3') sectionScore += 3;
    else if (assessment.workers_involvement_level === '2') sectionScore += 2;
    else if (assessment.workers_involvement_level === '1') sectionScore += 1;

    if (assessment.involve_program_activities === 'yes') sectionScore += 1;
    if (assessment.involve_leaders_orientation === 'yes') sectionScore += 1;
    if (assessment.solicit_feedback === 'yes') sectionScore += 1;
    if (assessment.regular_interaction === 'yes') sectionScore += 1;
    if (assessment.share_results === 'yes') sectionScore += 1;
  }

  // Section VII scoring
  else if (sectionNumber === 7) {
    if (assessment.cultural_gender_consideration === '4') sectionScore += 3;
    else if (assessment.cultural_gender_consideration === '3') sectionScore += 2;
    else if (assessment.cultural_gender_consideration === '2') sectionScore += 1;

    if (assessment.consider_local_culture === 'yes') sectionScore += 1;
    if (assessment.documented_guidelines === 'yes') sectionScore += 1;
    if (assessment.provide_training === 'yes') sectionScore += 1;
    if (assessment.use_assessment_findings === 'yes') sectionScore += 1;
  }

  // Section VIII scoring
  else if (sectionNumber === 8) {
    if (assessment.representation_effectiveness === '4') sectionScore += 3;
    else if (assessment.representation_effectiveness === '3') sectionScore += 2;
    else if (assessment.representation_effectiveness === '2') sectionScore += 1;

    if (assessment.member_involvement === '4') sectionScore += 3;
    else if (assessment.member_involvement === '3') sectionScore += 2;
    else if (assessment.member_involvement === '2') sectionScore += 1;

    if (assessment.bargaining_strategy === 'a') sectionScore += 2;
    else if (assessment.bargaining_strategy === 'b') sectionScore += 1;
  }

  // Section IX scoring
  else if (sectionNumber === 9) {
    if (assessment.communication_effectiveness === '4') sectionScore += 3;
    else if (assessment.communication_effectiveness === '3') sectionScore += 2;
    else if (assessment.communication_effectiveness === '2') sectionScore += 1;

    if (assessment.member_engagement === '4') sectionScore += 3;
    else if (assessment.member_engagement === '3') sectionScore += 2;
    else if (assessment.member_engagement === '2') sectionScore += 1;

    if (assessment.participation_opportunities === '4') sectionScore += 3;
    else if (assessment.participation_opportunities === '3') sectionScore += 2;
    else if (assessment.participation_opportunities === '2') sectionScore += 1;

    if (assessment.feedback_collection === 'a') sectionScore += 2;
    else if (assessment.feedback_collection === 'b') sectionScore += 1;
  }

  // Section X scoring
  else if (sectionNumber === 10) {
    if (assessment.fee_collection === '4') sectionScore += 3;
    else if (assessment.fee_collection === '3') sectionScore += 2;
    else if (assessment.fee_collection === '2') sectionScore += 1;

    if (assessment.financial_management === '4') sectionScore += 3;
    else if (assessment.financial_management === '3') sectionScore += 2;
    else if (assessment.financial_management === '2') sectionScore += 1;

    if (assessment.financial_planning === '4') sectionScore += 3;
    else if (assessment.financial_planning === '3') sectionScore += 2;
    else if (assessment.financial_planning === '2') sectionScore += 1;

    if (assessment.financial_system_quality === '4') sectionScore += 3;
    else if (assessment.financial_system_quality === '3') sectionScore += 2;
    else if (assessment.financial_system_quality === '2') sectionScore += 1;

    if (assessment.has_cash_system === 'yes') sectionScore += 1;
    if (assessment.uses_accounting_software === 'yes') sectionScore += 1;
    if (assessment.has_chart_accounts === 'yes') sectionScore += 1;
    if (assessment.reconciles_monthly === 'yes') sectionScore += 1;
  }

  // Section XI scoring
  else if (sectionNumber === 11) {
    if (assessment.audit_system_quality === '4') sectionScore += 3;
    else if (assessment.audit_system_quality === '3') sectionScore += 2;
    else if (assessment.audit_system_quality === '2') sectionScore += 1;

    if (assessment.requires_annual_audit === 'yes') sectionScore += 1;
    if (assessment.regularly_audited === 'yes') sectionScore += 1;
    if (assessment.auditor_selection === 'yes') sectionScore += 1;
    if (assessment.audit_manager === 'yes') sectionScore += 1;
    if (assessment.implements_recommendations === 'yes') sectionScore += 1;
    if (assessment.shares_reports === 'yes') sectionScore += 1;
    if (assessment.report_provides_info === 'yes') sectionScore += 1;
  }

  return sectionScore;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: federationId } = await params;

    // Fetch federation basic data
    const federationData = await safeRedisOperation(() => 
      redis.get(`federation:${federationId}`)
    );

    if (!federationData) {
      return NextResponse.json(
        { error: 'Federation not found' },
        { status: 404 }
      );
    }

    // Parse federation data
    let federation;
    if (typeof federationData === 'string') {
      try {
        federation = JSON.parse(federationData);
      } catch {
        return NextResponse.json(
          { error: 'Invalid federation data' },
          { status: 500 }
        );
      }
    } else {
      federation = federationData;
    }

    // Fetch all assessments for this federation
    const assessmentKeys = await safeRedisOperation(() => 
      redis.keys(`assessment:${federationId}:*`)
    );

    const assessmentsRaw = await Promise.all(
      assessmentKeys.map(key => safeRedisOperation(() => redis.get(key)))
    );

    // Parse assessments
    const assessments = assessmentsRaw
      .map((a) => {
        if (!a) return null;
        if (typeof a === 'string') {
          try {
            return JSON.parse(a);
          } catch {
            return null;
          }
        }
        return a;
      })
      .filter((assessment): assessment is any =>
        assessment !== null &&
        typeof assessment === 'object' &&
        'section_number' in assessment
      );

    // Section max scores (raw scores from useAssessment.ts)
    const sectionMaxScores: { [section: number]: number } = {
      2: 0, // Legal Status and Compliance (no scoring in useAssessment)
      3: 14, // Leadership
      4: 4, // Organizational Structure
      5: 11, // Management
      6: 9, // Worker Participation
      7: 7, // Culture and Gender
      8: 8, // Collective Bargaining
      9: 11, // Member Engagement
      10: 16, // Financial Stability
      11: 10, // Audit & Compliance
    };

    // Section titles
    const sectionTitles: { [key: number]: string } = {
      2: "Legal Status and Compliance",
      3: "Leadership",
      4: "Organizational Structure",
      5: "Management",
      6: "Worker Participation",
      7: "Culture and Gender",
      8: "Collective Bargaining",
      9: "Member Engagement",
      10: "Financial Stability",
      11: "Audit & Compliance"
    };

    // Calculate section scores and build detailed response
    const sectionScores: { [section: number]: number } = {};
    const sectionDetails: { [sectionNumber: number]: any } = {};
    let totalScore = 0;
    let maxPossible = 0;

    // Process each section that has assessments
    const allSectionNumbers = new Set([
      ...Object.keys(sectionMaxScores).map(Number),
      ...assessments.map(a => a.section_number)
    ]);

    for (const sectionNum of Array.from(allSectionNumbers).sort()) {
      const sectionAssessment = assessments.find(a => a.section_number === sectionNum);

      // Calculate raw score using the same logic as useAssessment.ts
      const rawScore = calculateSectionScore(sectionAssessment, sectionNum);
      const maxSectionScore = sectionMaxScores[sectionNum] || 0;

      sectionScores[sectionNum] = rawScore;

      // Only include sections with max scores > 0 in total calculation
      if (maxSectionScore > 0) {
        totalScore += rawScore;
        maxPossible += maxSectionScore;
      }

      // Build section details with questions and answers
      const questions = extractQuestionsFromAssessment(sectionAssessment, sectionNum);

      sectionDetails[sectionNum] = {
        title: sectionTitles[sectionNum] || `Section ${sectionNum}`,
        questions,
        sectionScore: rawScore,
        maxSectionScore,
        percentage: maxSectionScore > 0 ? Math.round((rawScore / maxSectionScore) * 100) : 0
      };
    }

    const percentage = maxPossible > 0 ? Math.round((totalScore / maxPossible) * 100) : 0;

    const response = {
      ...federation,
      assessments,
      totalScore,
      maxScore: maxPossible,
      percentage,
      sectionScores,
      sectionDetails
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in GET /api/federation/[id]:', error);
    return NextResponse.json(
      { error: 'Failed to fetch federation details', details: error?.message },
      { status: 500 }
    );
  }
}

// Helper function to extract questions and answers from assessment data
function extractQuestionsFromAssessment(assessment: any, sectionNumber: number) {
  if (!assessment) return [];

  const questions: { question: string; answer: string; score?: number; maxScore?: number }[] = [];

  // Define questions and answer mappings for each section
  const sectionData: { [key: number]: { [field: string]: { question: string; answerMap?: { [key: string]: string } } } } = {
    2: {
      'legal_registration_status': {
        question: 'What is the legal registration status of the Federation?',
        answerMap: {
          'a': 'Registered with proper documentation',
          'b': 'Registered but documentation incomplete',
          'c': 'Registration in progress',
          'd': 'Not registered'
        }
      },
      'registration_authority': {
        question: 'Which authority is the Federation registered with?'
      }
    },
    3: {
      'vision_mission_status': {
        question: 'Does the Federation have a clear vision and mission?',
        answerMap: {
          '1': 'No vision or mission statement',
          '2': 'Vision/mission exists but not well communicated',
          '3': 'Vision/mission exists and somewhat communicated',
          '4': 'Clear vision/mission, well communicated to all'
        }
      },
      'vision_posted': {
        question: 'Is the vision/mission posted in visible locations?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'vision_in_documents': {
        question: 'Is the vision/mission included in official documents?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'vision_for_planning': {
        question: 'Is the vision/mission used for strategic planning?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'decision_making': {
        question: 'How are decisions made in the Federation?',
        answerMap: {
          'a': 'Democratic process with member consultation',
          'b': 'Executive committee decisions',
          'c': 'Top-down leadership decisions',
          'd': 'Ad-hoc decision making'
        }
      },
      'emerging_issues_handling': {
        question: 'How does the Federation handle emerging issues?',
        answerMap: {
          'a': 'Proactive approach with systematic response',
          'b': 'Reactive approach with some planning',
          'c': 'Minimal response to issues',
          'd': 'No systematic approach'
        }
      },
      'leadership_development_plan': {
        question: 'Does the Federation have a leadership development plan?',
        answerMap: {
          'a': 'Comprehensive leadership development program',
          'b': 'Basic leadership training available',
          'c': 'Informal leadership development',
          'd': 'No leadership development plan'
        }
      }
    },
    4: {
      'organizational_structure': {
        question: 'What is the organizational structure of the Federation?',
        answerMap: {
          'a': 'Well-defined hierarchical structure',
          'b': 'Basic organizational structure',
          'c': 'Informal structure',
          'd': 'No clear structure'
        }
      },
      'roles_defined': {
        question: 'Are roles and responsibilities clearly defined?',
        answerMap: {
          'a': 'All roles clearly documented and communicated',
          'b': 'Most roles defined',
          'c': 'Some roles defined',
          'd': 'Roles not clearly defined'
        }
      }
    },
    5: {
      'management_approach': {
        question: 'How is the Federation managed?',
        answerMap: {
          'a': 'Professional management with clear processes',
          'b': 'Structured management approach',
          'c': 'Basic management practices',
          'd': 'Informal management'
        }
      },
      'authority_delegation': {
        question: 'How is authority delegated in the Federation?',
        answerMap: {
          'a': 'Clear delegation with proper oversight',
          'b': 'Some delegation with monitoring',
          'c': 'Limited delegation',
          'd': 'Centralized authority, no delegation'
        }
      },
      'decision_making_process': {
        question: 'What is the decision-making process?',
        answerMap: {
          '1': 'No formal process',
          '2': 'Basic process exists',
          '3': 'Structured process with documentation',
          '4': 'Comprehensive process with stakeholder involvement'
        }
      },
      'deputy_availability': {
        question: 'Is there a deputy available for key positions?',
        answerMap: {
          'a': 'All key positions have designated deputies',
          'b': 'Most positions have deputies',
          'c': 'Some positions have deputies',
          'd': 'No deputy system in place'
        }
      },
      'transition_plan': {
        question: 'Is there a succession/transition plan?',
        answerMap: {
          'a': 'Comprehensive succession planning',
          'b': 'Basic transition planning',
          'c': 'Informal succession arrangements',
          'd': 'No succession planning'
        }
      }
    },
    6: {
      'workers_involvement_level': {
        question: 'What is the level of workers\' involvement in the Federation?',
        answerMap: {
          '1': 'Minimal involvement',
          '2': 'Basic involvement in some activities',
          '3': 'Good involvement in most activities',
          '4': 'High level of involvement in all activities'
        }
      },
      'involve_program_activities': {
        question: 'Are workers involved in program activities?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'involve_leaders_orientation': {
        question: 'Are workers involved in leadership orientation?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'solicit_feedback': {
        question: 'Does the Federation solicit feedback from workers?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'regular_interaction': {
        question: 'Is there regular interaction with workers?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'share_results': {
        question: 'Are results shared with workers?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      }
    }
  };

  // Continue with remaining sections...
  const remainingSections = {
    7: {
      'cultural_gender_consideration': {
        question: 'How does the Federation consider cultural and gender aspects?',
        answerMap: {
          '1': 'No consideration',
          '2': 'Basic awareness',
          '3': 'Good consideration with some policies',
          '4': 'Comprehensive cultural and gender policies'
        }
      },
      'consider_local_culture': {
        question: 'Does the Federation consider local culture?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'documented_guidelines': {
        question: 'Are there documented cultural guidelines?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'provide_training': {
        question: 'Does the Federation provide cultural sensitivity training?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'use_assessment_findings': {
        question: 'Are assessment findings used for improvement?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      }
    },
    8: {
      'representation_effectiveness': {
        question: 'How effective is the Federation\'s representation?',
        answerMap: {
          '1': 'Poor representation',
          '2': 'Basic representation',
          '3': 'Good representation',
          '4': 'Excellent representation'
        }
      },
      'member_involvement': {
        question: 'What is the level of member involvement in collective bargaining?',
        answerMap: {
          '1': 'No member involvement',
          '2': 'Limited involvement',
          '3': 'Good member involvement',
          '4': 'High member involvement'
        }
      },
      'bargaining_strategy': {
        question: 'What is the Federation\'s bargaining strategy?',
        answerMap: {
          'a': 'Comprehensive strategic approach',
          'b': 'Structured bargaining approach',
          'c': 'Basic bargaining tactics',
          'd': 'Ad-hoc bargaining'
        }
      }
    },
    9: {
      'communication_effectiveness': {
        question: 'How effective is the Federation\'s communication?',
        answerMap: {
          '1': 'Poor communication',
          '2': 'Basic communication',
          '3': 'Good communication',
          '4': 'Excellent communication'
        }
      },
      'member_engagement': {
        question: 'What is the level of member engagement?',
        answerMap: {
          '1': 'Low engagement',
          '2': 'Basic engagement',
          '3': 'Good engagement',
          '4': 'High engagement'
        }
      },
      'participation_opportunities': {
        question: 'What participation opportunities are available?',
        answerMap: {
          '1': 'Limited opportunities',
          '2': 'Basic opportunities',
          '3': 'Good opportunities',
          '4': 'Extensive opportunities'
        }
      },
      'feedback_collection': {
        question: 'How does the Federation collect member feedback?',
        answerMap: {
          'a': 'Systematic feedback collection',
          'b': 'Regular feedback sessions',
          'c': 'Occasional feedback collection',
          'd': 'No formal feedback system'
        }
      }
    },
    10: {
      'fee_collection': {
        question: 'How does the Federation collect fees?',
        answerMap: {
          '1': 'No systematic collection',
          '2': 'Basic collection system',
          '3': 'Good collection system',
          '4': 'Excellent systematic collection'
        }
      },
      'financial_management': {
        question: 'How is financial management handled?',
        answerMap: {
          '1': 'Poor financial management',
          '2': 'Basic financial management',
          '3': 'Good financial management',
          '4': 'Excellent financial management'
        }
      },
      'financial_planning': {
        question: 'What is the approach to financial planning?',
        answerMap: {
          '1': 'No financial planning',
          '2': 'Basic financial planning',
          '3': 'Good financial planning',
          '4': 'Comprehensive financial planning'
        }
      },
      'financial_system_quality': {
        question: 'What is the quality of the financial system?',
        answerMap: {
          '1': 'Poor financial systems',
          '2': 'Basic financial systems',
          '3': 'Good financial systems',
          '4': 'Excellent financial systems'
        }
      },
      'has_cash_system': {
        question: 'Does the Federation have a cash management system?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'uses_accounting_software': {
        question: 'Does the Federation use accounting software?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'has_chart_accounts': {
        question: 'Does the Federation have a chart of accounts?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      },
      'reconciles_monthly': {
        question: 'Does the Federation reconcile accounts monthly?',
        answerMap: { 'yes': 'Yes', 'no': 'No' }
      }
    },
    11: {
      'audit_system_quality': {
        question: 'What is the quality of the audit system?',
        answerMap: {
          '1': 'No audit system',
          '2': 'Basic audit system',
          '3': 'Good audit system',
          '4': 'Excellent audit system'
        }
      },
      'requires_annual_audit': {
        question: 'Does the Federation require annual audits?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'regularly_audited': {
        question: 'Is the Federation regularly audited?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'auditor_selection': {
        question: 'How are auditors selected?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'audit_manager': {
        question: 'Who manages the audit process?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'implements_recommendations': {
        question: 'Does the Federation implement audit recommendations?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'shares_reports': {
        question: 'Are audit reports shared with stakeholders?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      },
      'report_provides_info': {
        question: 'Do audit reports provide useful information?',
        answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
      }
    }
  };

  // Merge all sections
  const allSections = { ...sectionData, ...remainingSections };
  const sectionQuestionMap = allSections[sectionNumber] || {};

  // Extract questions and answers
  Object.entries(sectionQuestionMap).forEach(([field, config]) => {
    const rawAnswer = assessment[field];
    if (rawAnswer !== undefined && rawAnswer !== null && rawAnswer !== '') {
      let decodedAnswer = rawAnswer;

      // Decode answer if mapping exists
      if (config.answerMap && config.answerMap[rawAnswer]) {
        decodedAnswer = config.answerMap[rawAnswer];
      }

      questions.push({
        question: config.question,
        answer: decodedAnswer,
        // Note: Individual question scores are not stored separately in current structure
        // They would need to be added to the assessment data structure if needed
      });
    }
  });

  return questions;
}
