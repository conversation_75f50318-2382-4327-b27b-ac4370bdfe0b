/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/qa-stats/route";
exports.ids = ["app/api/qa-stats/route"];
exports.modules = {

/***/ "(rsc)/./app/api/qa-stats/route.ts":
/*!***********************************!*\
  !*** ./app/api/qa-stats/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_upstash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/upstash */ \"(rsc)/./lib/upstash.ts\");\n\n\n// Question mappings for each section\nconst sectionQuestions = {\n    3: {\n        'vision_mission_status': {\n            question: 'Does the Federation have a clear vision and mission?',\n            answerMap: {\n                '1': 'No vision or mission statement',\n                '2': 'Vision/mission exists but not well communicated',\n                '3': 'Vision/mission exists and somewhat communicated',\n                '4': 'Clear vision/mission, well communicated to all'\n            }\n        },\n        'decision_making': {\n            question: 'How are decisions made in the Federation?',\n            answerMap: {\n                'a': 'Democratic process with member consultation',\n                'b': 'Executive committee decisions',\n                'c': 'Top-down leadership decisions',\n                'd': 'Ad-hoc decision making'\n            }\n        },\n        'emerging_issues_handling': {\n            question: 'How does the Federation handle emerging issues?',\n            answerMap: {\n                'a': 'Proactive approach with systematic response',\n                'b': 'Reactive approach with some planning',\n                'c': 'Minimal response to issues',\n                'd': 'No systematic approach'\n            }\n        },\n        'leadership_development_plan': {\n            question: 'Does the Federation have a leadership development plan?',\n            answerMap: {\n                'a': 'Comprehensive leadership development program',\n                'b': 'Basic leadership training available',\n                'c': 'Informal leadership development',\n                'd': 'No leadership development plan'\n            }\n        }\n    },\n    4: {\n        'organizational_structure': {\n            question: 'What is the organizational structure of the Federation?',\n            answerMap: {\n                'a': 'Well-defined hierarchical structure',\n                'b': 'Basic organizational structure',\n                'c': 'Informal structure',\n                'd': 'No clear structure'\n            }\n        },\n        'roles_defined': {\n            question: 'Are roles and responsibilities clearly defined?',\n            answerMap: {\n                'a': 'All roles clearly documented and communicated',\n                'b': 'Most roles defined',\n                'c': 'Some roles defined',\n                'd': 'Roles not clearly defined'\n            }\n        }\n    },\n    5: {\n        'management_approach': {\n            question: 'How is the Federation managed?',\n            answerMap: {\n                'a': 'Professional management with clear processes',\n                'b': 'Structured management approach',\n                'c': 'Basic management practices',\n                'd': 'Informal management'\n            }\n        },\n        'authority_delegation': {\n            question: 'How is authority delegated in the Federation?',\n            answerMap: {\n                'a': 'Clear delegation with proper oversight',\n                'b': 'Some delegation with monitoring',\n                'c': 'Limited delegation',\n                'd': 'Centralized authority, no delegation'\n            }\n        },\n        'decision_making_process': {\n            question: 'What is the decision-making process?',\n            answerMap: {\n                '1': 'No formal process',\n                '2': 'Basic process exists',\n                '3': 'Structured process with documentation',\n                '4': 'Comprehensive process with stakeholder involvement'\n            }\n        }\n    },\n    6: {\n        'workers_involvement_level': {\n            question: 'What is the level of workers\\' involvement in the Federation?',\n            answerMap: {\n                '1': 'Minimal involvement',\n                '2': 'Basic involvement in some activities',\n                '3': 'Good involvement in most activities',\n                '4': 'High level of involvement in all activities'\n            }\n        }\n    },\n    7: {\n        'cultural_gender_consideration': {\n            question: 'How does the Federation consider cultural and gender aspects?',\n            answerMap: {\n                '1': 'No consideration',\n                '2': 'Basic awareness',\n                '3': 'Good consideration with some policies',\n                '4': 'Comprehensive cultural and gender policies'\n            }\n        }\n    },\n    8: {\n        'representation_effectiveness': {\n            question: 'How effective is the Federation\\'s representation?',\n            answerMap: {\n                '1': 'Poor representation',\n                '2': 'Basic representation',\n                '3': 'Good representation',\n                '4': 'Excellent representation'\n            }\n        },\n        'member_involvement': {\n            question: 'What is the level of member involvement in collective bargaining?',\n            answerMap: {\n                '1': 'No member involvement',\n                '2': 'Limited involvement',\n                '3': 'Good member involvement',\n                '4': 'High member involvement'\n            }\n        }\n    },\n    9: {\n        'communication_effectiveness': {\n            question: 'How effective is the Federation\\'s communication?',\n            answerMap: {\n                '1': 'Poor communication',\n                '2': 'Basic communication',\n                '3': 'Good communication',\n                '4': 'Excellent communication'\n            }\n        },\n        'member_engagement': {\n            question: 'What is the level of member engagement?',\n            answerMap: {\n                '1': 'Low engagement',\n                '2': 'Basic engagement',\n                '3': 'Good engagement',\n                '4': 'High engagement'\n            }\n        }\n    },\n    10: {\n        'fee_collection': {\n            question: 'How does the Federation collect fees?',\n            answerMap: {\n                '1': 'No systematic collection',\n                '2': 'Basic collection system',\n                '3': 'Good collection system',\n                '4': 'Excellent systematic collection'\n            }\n        },\n        'financial_management': {\n            question: 'How is financial management handled?',\n            answerMap: {\n                '1': 'Poor financial management',\n                '2': 'Basic financial management',\n                '3': 'Good financial management',\n                '4': 'Excellent financial management'\n            }\n        }\n    },\n    11: {\n        'audit_system_quality': {\n            question: 'What is the quality of the audit system?',\n            answerMap: {\n                '1': 'No audit system',\n                '2': 'Basic audit system',\n                '3': 'Good audit system',\n                '4': 'Excellent audit system'\n            }\n        },\n        'requires_annual_audit': {\n            question: 'Does the Federation require annual audits?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        }\n    }\n};\nconst sectionTitles = {\n    3: \"Leadership\",\n    4: \"Organizational Structure\",\n    5: \"Management\",\n    6: \"Worker Participation\",\n    7: \"Culture and Gender\",\n    8: \"Collective Bargaining\",\n    9: \"Member Engagement\",\n    10: \"Financial Stability\",\n    11: \"Audit & Compliance\"\n};\nasync function GET(request) {\n    try {\n        // Get all federations\n        const federationKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys('federation:*'));\n        const federations = await Promise.all(federationKeys.map(async (key)=>{\n            const data = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key));\n            if (typeof data === 'string') {\n                try {\n                    return JSON.parse(data);\n                } catch  {\n                    return null;\n                }\n            }\n            return data;\n        }));\n        const validFederations = federations.filter((f)=>f && f.id);\n        // Get all assessments\n        const assessmentKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys('assessment:*'));\n        const assessments = await Promise.all(assessmentKeys.map(async (key)=>{\n            const data = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key));\n            if (typeof data === 'string') {\n                try {\n                    return JSON.parse(data);\n                } catch  {\n                    return null;\n                }\n            }\n            return data;\n        }));\n        const validAssessments = assessments.filter((a)=>a && a.federation_id && a.section_number);\n        // Process Q&A statistics for sections 3-11\n        const qaStats = [];\n        for (const sectionNum of [\n            3,\n            4,\n            5,\n            6,\n            7,\n            8,\n            9,\n            10,\n            11\n        ]){\n            const sectionAssessments = validAssessments.filter((a)=>a.section_number === sectionNum);\n            const questions = sectionQuestions[sectionNum] || {};\n            const questionStats = [];\n            for (const [field, config] of Object.entries(questions)){\n                const responses = {};\n                let totalResponses = 0;\n                // Count responses for this question\n                sectionAssessments.forEach((assessment)=>{\n                    const rawAnswer = assessment[field];\n                    if (rawAnswer !== undefined && rawAnswer !== null && rawAnswer !== '') {\n                        let decodedAnswer = rawAnswer;\n                        // Decode answer if mapping exists\n                        if (config.answerMap && config.answerMap[rawAnswer]) {\n                            decodedAnswer = config.answerMap[rawAnswer];\n                        }\n                        if (!responses[decodedAnswer]) {\n                            responses[decodedAnswer] = {\n                                count: 0,\n                                federations: []\n                            };\n                        }\n                        responses[decodedAnswer].count++;\n                        responses[decodedAnswer].federations.push(assessment.federation_id.toUpperCase());\n                        totalResponses++;\n                    }\n                });\n                // Convert to array format with percentages\n                const responseArray = Object.entries(responses).map(([answer, data])=>({\n                        answer,\n                        count: data.count,\n                        percentage: totalResponses > 0 ? data.count / totalResponses * 100 : 0,\n                        federations: data.federations.sort()\n                    })).sort((a, b)=>b.count - a.count); // Sort by count descending\n                questionStats.push({\n                    field,\n                    question: config.question,\n                    responses: responseArray,\n                    totalResponses\n                });\n            }\n            qaStats.push({\n                sectionNumber: sectionNum,\n                sectionTitle: sectionTitles[sectionNum],\n                questions: questionStats\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(qaStats);\n    } catch (error) {\n        console.error('Error in GET /api/qa-stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch Q&A statistics',\n            details: error?.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/qa-stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/upstash.ts":
/*!************************!*\
  !*** ./lib/upstash.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   safeRedisOperation: () => (/* binding */ safeRedisOperation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: \"https://ample-bear-14398.upstash.io\",\n    token: \"ATg-AAIjcDExMDY2ODQwYjIwNDI0MWQxODg4MjExMzg3YTMzYTRhNnAxMA\"\n});\n// Helper function to handle Redis operations with error checking\nasync function safeRedisOperation(operation, fallback) {\n    try {\n        const result = await operation();\n        if (result === null && fallback !== undefined) {\n            return fallback;\n        }\n        if (result === null) {\n            return {};\n        }\n        return result;\n    } catch (error) {\n        if (fallback !== undefined) {\n            console.error('Redis operation failed, using fallback:', error);\n            return fallback;\n        }\n        if (error.message?.includes('NOPERM')) {\n            console.error('Redis authentication failed:', error);\n            throw new Error('Database access denied. Please check your credentials.');\n        }\n        if (error.message?.includes('connect')) {\n            console.error('Redis connection error:', error);\n            throw new Error('Failed to connect to database. Please try again.');\n        }\n        if (error.message?.includes('JSON')) {\n            console.error('Redis JSON parsing error:', error);\n            throw new Error('Error retrieving user data. Invalid data format.');\n        }\n        console.error('Redis operation error:', error);\n        throw new Error('Error retrieving user data. Please try again.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/upstash.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_tucat_tucat_app_api_qa_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/qa-stats/route.ts */ \"(rsc)/./app/api/qa-stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/qa-stats/route\",\n        pathname: \"/api/qa-stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/qa-stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\tucat\\\\tucat\\\\app\\\\api\\\\qa-stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_tucat_tucat_app_api_qa_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();