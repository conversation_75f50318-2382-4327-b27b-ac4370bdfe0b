/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/qa-stats/route";
exports.ids = ["app/api/qa-stats/route"];
exports.modules = {

/***/ "(rsc)/./app/api/qa-stats/route.ts":
/*!***********************************!*\
  !*** ./app/api/qa-stats/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_upstash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/upstash */ \"(rsc)/./lib/upstash.ts\");\n\n\n// Question mappings for each section\nconst sectionQuestions = {\n    3: {\n        'vision_mission_status': {\n            question: 'Does the Federation have a clear vision and mission?',\n            answerMap: {\n                '1': 'No vision or mission statement',\n                '2': 'Vision/mission exists but not well communicated',\n                '3': 'Vision/mission exists and somewhat communicated',\n                '4': 'Clear vision/mission, well communicated to all'\n            }\n        },\n        'decision_making': {\n            question: 'How are decisions made in the Federation?',\n            answerMap: {\n                'a': 'Democratic process with member consultation',\n                'b': 'Executive committee decisions',\n                'c': 'Top-down leadership decisions',\n                'd': 'Ad-hoc decision making'\n            }\n        },\n        'vision_posted': {\n            question: 'Is the vision or mission statement posted where members see it regularly?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'vision_in_documents': {\n            question: 'Is the statement(s) used in any document?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'vision_for_planning': {\n            question: 'Are the vision and mission used to set priorities or for developing Annual Plan?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'emerging_issues_handling': {\n            question: 'How does the Federation handle emerging issues?',\n            answerMap: {\n                'a': 'Proactive approach with systematic response',\n                'b': 'Reactive approach with some planning',\n                'c': 'Minimal response to issues',\n                'd': 'No systematic approach'\n            }\n        },\n        'leadership_development_plan': {\n            question: 'Does the Federation have a leadership development plan?',\n            answerMap: {\n                'a': 'Comprehensive leadership development program',\n                'b': 'Basic leadership training available',\n                'c': 'Informal leadership development',\n                'd': 'No leadership development plan'\n            }\n        }\n    },\n    4: {\n        'organizational_structure': {\n            question: 'What is the organizational structure of the Federation?',\n            answerMap: {\n                'a': 'Well-defined hierarchical structure',\n                'b': 'Basic organizational structure',\n                'c': 'Informal structure',\n                'd': 'No clear structure'\n            }\n        },\n        'roles_defined': {\n            question: 'Are roles and responsibilities clearly defined?',\n            answerMap: {\n                'a': 'All roles clearly documented and communicated',\n                'b': 'Most roles defined',\n                'c': 'Some roles defined',\n                'd': 'Roles not clearly defined'\n            }\n        }\n    },\n    5: {\n        'management_approach': {\n            question: 'How is the Federation managed?',\n            answerMap: {\n                'a': 'Professional management with clear processes',\n                'b': 'Structured management approach',\n                'c': 'Basic management practices',\n                'd': 'Informal management'\n            }\n        },\n        'authority_delegation': {\n            question: 'How is authority delegated in the Federation?',\n            answerMap: {\n                'a': 'Clear delegation with proper oversight',\n                'b': 'Some delegation with monitoring',\n                'c': 'Limited delegation',\n                'd': 'Centralized authority, no delegation'\n            }\n        },\n        'decision_making_process': {\n            question: 'What is the decision-making process?',\n            answerMap: {\n                '1': 'No formal process',\n                '2': 'Basic process exists',\n                '3': 'Structured process with documentation',\n                '4': 'Comprehensive process with stakeholder involvement'\n            }\n        }\n    },\n    6: {\n        'workers_involvement_level': {\n            question: 'What is the level of workers\\' involvement in the Federation?',\n            answerMap: {\n                '1': 'Minimal involvement',\n                '2': 'Basic involvement in some activities',\n                '3': 'Good involvement in most activities',\n                '4': 'High level of involvement in all activities'\n            }\n        }\n    },\n    7: {\n        'cultural_gender_consideration': {\n            question: 'How does the Federation consider cultural and gender aspects?',\n            answerMap: {\n                '1': 'No consideration',\n                '2': 'Basic awareness',\n                '3': 'Good consideration with some policies',\n                '4': 'Comprehensive cultural and gender policies'\n            }\n        },\n        'consider_local_culture': {\n            question: 'Does your Federation consider local culture and gender in programming?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'documented_guidelines': {\n            question: 'Does your Federation have clearly documented guidelines for culturally relevant and/or gender-based approaches and programming?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'provide_training': {\n            question: 'Does your Federation provide training in gender and/or cultural issues and survey tools?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'use_assessment_findings': {\n            question: 'Are findings from culture and/or gender assessments used in program development and implementation?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        }\n    },\n    8: {\n        'representation_effectiveness': {\n            question: 'How effective is the Federation\\'s representation?',\n            answerMap: {\n                '1': 'Poor representation',\n                '2': 'Basic representation',\n                '3': 'Good representation',\n                '4': 'Excellent representation'\n            }\n        },\n        'member_involvement': {\n            question: 'What is the level of member involvement in collective bargaining?',\n            answerMap: {\n                '1': 'No member involvement',\n                '2': 'Limited involvement',\n                '3': 'Good member involvement',\n                '4': 'High member involvement'\n            }\n        }\n    },\n    9: {\n        'communication_effectiveness': {\n            question: 'How effectively does the federation communicate with its members?',\n            answerMap: {\n                '1': 'Poor communication',\n                '2': 'Basic communication',\n                '3': 'Good communication',\n                '4': 'Very effective communication'\n            }\n        },\n        'member_engagement': {\n            question: 'How engaged are members in federation activities and decision-making?',\n            answerMap: {\n                '1': 'Low engagement',\n                '2': 'Basic engagement',\n                '3': 'Good engagement',\n                '4': 'Highly engaged'\n            }\n        },\n        'participation_opportunities': {\n            question: 'How well does the federation provide opportunities for members to express concerns and contribute to decision-making?',\n            answerMap: {\n                '1': 'Poor opportunities',\n                '2': 'Basic opportunities',\n                '3': 'Good opportunities',\n                '4': 'Excellent opportunities'\n            }\n        },\n        'feedback_collection': {\n            question: 'Does the federation collect and act on feedback from members?',\n            answerMap: {\n                'a': 'Yes, feedback is regularly collected and acted upon',\n                'b': 'Somewhat – Feedback is collected but not always acted upon',\n                'c': 'No, feedback is rarely collected or acted upon'\n            }\n        }\n    },\n    10: {\n        'fee_collection': {\n            question: 'How does the Federation collect fees?',\n            answerMap: {\n                '1': 'No systematic collection',\n                '2': 'Basic collection system',\n                '3': 'Good collection system',\n                '4': 'Excellent systematic collection'\n            }\n        },\n        'financial_management': {\n            question: 'How is financial management handled?',\n            answerMap: {\n                '1': 'Poor financial management',\n                '2': 'Basic financial management',\n                '3': 'Good financial management',\n                '4': 'Excellent financial management'\n            }\n        }\n    },\n    10: {\n        'fee_collection': {\n            question: 'How does the Federation collect fees from members?',\n            answerMap: {\n                '1': 'No systematic fee collection',\n                '2': 'Irregular fee collection',\n                '3': 'Regular fee collection with some challenges',\n                '4': 'Systematic and efficient fee collection'\n            }\n        },\n        'financial_management': {\n            question: 'How would you rate the Federation\\'s financial management?',\n            answerMap: {\n                '1': 'Poor financial management',\n                '2': 'Basic financial management',\n                '3': 'Good financial management',\n                '4': 'Excellent financial management'\n            }\n        },\n        'financial_planning': {\n            question: 'How effective is the Federation\\'s financial planning?',\n            answerMap: {\n                '1': 'No financial planning',\n                '2': 'Basic financial planning',\n                '3': 'Good financial planning',\n                '4': 'Comprehensive financial planning'\n            }\n        },\n        'financial_system_quality': {\n            question: 'What is the quality of the Federation\\'s financial system?',\n            answerMap: {\n                '1': 'No financial system',\n                '2': 'Basic financial system',\n                '3': 'Good financial system',\n                '4': 'Complete and appropriate financial system'\n            }\n        },\n        'has_cash_system': {\n            question: 'Does Federation have a cash, accrual or modified system? Are cash payments made?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'uses_accounting_software': {\n            question: 'Is your Federation using accounting software?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'has_chart_accounts': {\n            question: 'Is there a chart of accounts (income and expenses, assets and liabilities)? Does it address donor specific requirements?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'reconciles_monthly': {\n            question: 'Are the bank accounts reconciled monthly against the bank journals/cash books?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        }\n    },\n    11: {\n        'audit_system_quality': {\n            question: 'What is the quality of the audit system?',\n            answerMap: {\n                '1': 'No audit system',\n                '2': 'Basic audit system',\n                '3': 'Good audit system',\n                '4': 'Excellent audit system'\n            }\n        },\n        'requires_annual_audit': {\n            question: 'Does the Federation require annual audits?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'regularly_audited': {\n            question: 'Is your Federation regularly audited?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'auditor_selection': {\n            question: 'Does your Federation select the auditor through a competitive process?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'audit_manager': {\n            question: 'Does your Federation have a designated person to manage the audit process?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'implements_recommendations': {\n            question: 'Does your Federation implement audit recommendations?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'shares_reports': {\n            question: 'Does your Federation share audit reports with stakeholders?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        },\n        'report_provides_info': {\n            question: 'Does the audit report provide useful information for decision making?',\n            answerMap: {\n                'yes': 'Yes',\n                'no': 'No',\n                'na': 'Not Applicable'\n            }\n        }\n    }\n};\nconst sectionTitles = {\n    3: \"Leadership\",\n    4: \"Organizational Structure\",\n    5: \"Management\",\n    6: \"Worker Participation\",\n    7: \"Culture and Gender\",\n    8: \"Collective Bargaining\",\n    9: \"Member Engagement\",\n    10: \"Financial Stability\",\n    11: \"Audit & Compliance\"\n};\nasync function GET(request) {\n    try {\n        // Get all federations\n        const federationKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys('federation:*'));\n        const federations = await Promise.all(federationKeys.map(async (key)=>{\n            const data = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key));\n            if (typeof data === 'string') {\n                try {\n                    return JSON.parse(data);\n                } catch  {\n                    return null;\n                }\n            }\n            return data;\n        }));\n        const validFederations = federations.filter((f)=>f && f.id);\n        // Get all assessments\n        const assessmentKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys('assessment:*'));\n        const assessments = await Promise.all(assessmentKeys.map(async (key)=>{\n            const data = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key));\n            if (typeof data === 'string') {\n                try {\n                    return JSON.parse(data);\n                } catch  {\n                    return null;\n                }\n            }\n            return data;\n        }));\n        const validAssessments = assessments.filter((a)=>a && a.federation_id && a.section_number);\n        // Process Q&A statistics for sections 3-11\n        const qaStats = [];\n        for (const sectionNum of [\n            3,\n            4,\n            5,\n            6,\n            7,\n            8,\n            9,\n            10,\n            11\n        ]){\n            const sectionAssessments = validAssessments.filter((a)=>a.section_number === sectionNum);\n            const questions = sectionQuestions[sectionNum] || {};\n            const questionStats = [];\n            for (const [field, config] of Object.entries(questions)){\n                const responses = {};\n                let totalResponses = 0;\n                // Count responses for this question\n                sectionAssessments.forEach((assessment)=>{\n                    const rawAnswer = assessment[field];\n                    if (rawAnswer !== undefined && rawAnswer !== null && rawAnswer !== '') {\n                        let decodedAnswer = rawAnswer;\n                        // Decode answer if mapping exists\n                        if (config.answerMap && config.answerMap[rawAnswer]) {\n                            decodedAnswer = config.answerMap[rawAnswer];\n                        }\n                        if (!responses[decodedAnswer]) {\n                            responses[decodedAnswer] = {\n                                count: 0,\n                                federations: []\n                            };\n                        }\n                        responses[decodedAnswer].count++;\n                        responses[decodedAnswer].federations.push(assessment.federation_id.toUpperCase());\n                        totalResponses++;\n                    }\n                });\n                // Convert to array format with percentages\n                const responseArray = Object.entries(responses).map(([answer, data])=>({\n                        answer,\n                        count: data.count,\n                        percentage: totalResponses > 0 ? data.count / totalResponses * 100 : 0,\n                        federations: data.federations.sort()\n                    })).sort((a, b)=>b.count - a.count); // Sort by count descending\n                questionStats.push({\n                    field,\n                    question: config.question,\n                    responses: responseArray,\n                    totalResponses\n                });\n            }\n            qaStats.push({\n                sectionNumber: sectionNum,\n                sectionTitle: sectionTitles[sectionNum],\n                questions: questionStats\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(qaStats);\n    } catch (error) {\n        console.error('Error in GET /api/qa-stats:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch Q&A statistics',\n            details: error?.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/qa-stats/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/upstash.ts":
/*!************************!*\
  !*** ./lib/upstash.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   safeRedisOperation: () => (/* binding */ safeRedisOperation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: \"https://ample-bear-14398.upstash.io\",\n    token: \"ATg-AAIjcDExMDY2ODQwYjIwNDI0MWQxODg4MjExMzg3YTMzYTRhNnAxMA\"\n});\n// Helper function to handle Redis operations with error checking\nasync function safeRedisOperation(operation, fallback) {\n    try {\n        const result = await operation();\n        if (result === null && fallback !== undefined) {\n            return fallback;\n        }\n        if (result === null) {\n            return {};\n        }\n        return result;\n    } catch (error) {\n        if (fallback !== undefined) {\n            console.error('Redis operation failed, using fallback:', error);\n            return fallback;\n        }\n        if (error.message?.includes('NOPERM')) {\n            console.error('Redis authentication failed:', error);\n            throw new Error('Database access denied. Please check your credentials.');\n        }\n        if (error.message?.includes('connect')) {\n            console.error('Redis connection error:', error);\n            throw new Error('Failed to connect to database. Please try again.');\n        }\n        if (error.message?.includes('JSON')) {\n            console.error('Redis JSON parsing error:', error);\n            throw new Error('Error retrieving user data. Invalid data format.');\n        }\n        console.error('Redis operation error:', error);\n        throw new Error('Error retrieving user data. Please try again.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/upstash.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_tucat_tucat_app_api_qa_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/qa-stats/route.ts */ \"(rsc)/./app/api/qa-stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/qa-stats/route\",\n        pathname: \"/api/qa-stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/qa-stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\tucat\\\\tucat\\\\app\\\\api\\\\qa-stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_tucat_tucat_app_api_qa_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fqa-stats%2Froute&page=%2Fapi%2Fqa-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqa-stats%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();