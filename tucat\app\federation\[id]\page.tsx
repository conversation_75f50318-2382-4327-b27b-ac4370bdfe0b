"use client";

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowLeft, Building2, Award, Target,
  BarChart3, FileText, AlertCircle, TrendingUp, Download
} from "lucide-react";
import { Federation, Assessment } from "@/lib/types";
import { UserNav } from "@/components/UserNav";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { toast } from "sonner";

interface FederationDetail extends Federation {
  assessments: Assessment[];
  sectionDetails: {
    [sectionNumber: number]: {
      title: string;
      questions: {
        question: string;
        answer: string;
        score?: number;
        maxScore?: number;
      }[];
      sectionScore: number;
      maxSectionScore: number;
      percentage: number;
    };
  };
}

export default function FederationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const federationId = params.id as string;

  const [federation, setFederation] = useState<FederationDetail | null>(null);
  const [allFederations, setAllFederations] = useState<Federation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingFederations, setIsLoadingFederations] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // Section titles mapping
  const sectionTitles: { [key: number]: string } = {
    3: "Leadership",
    4: "Organizational Structure", 
    5: "Management",
    6: "Worker Participation",
    7: "Culture and Gender",
    8: "Collective Bargaining",
    9: "Member Engagement",
    10: "Financial Stability",
    11: "Audit & Compliance"
  };

  useEffect(() => {
    fetchFederationDetail();
    fetchAllFederations();
  }, [federationId]);

  const fetchFederationDetail = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/federation/${federationId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch federation details');
      }

      const data = await response.json();
      setFederation(data);
    } catch (error) {
      console.error('Error fetching federation details:', error);
      toast.error("Failed to load federation details");
      router.push('/all-federation');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAllFederations = async () => {
    try {
      setIsLoadingFederations(true);
      const response = await fetch('/api/federations');

      if (!response.ok) {
        throw new Error('Failed to fetch federations list');
      }

      const data = await response.json();
      setAllFederations(data.federations || []);
    } catch (error) {
      console.error('Error fetching federations list:', error);
      // Don't show error toast for this as it's not critical
    } finally {
      setIsLoadingFederations(false);
    }
  };

  const handleFederationChange = (newFederationId: string) => {
    if (newFederationId !== federationId) {
      router.push(`/federation/${newFederationId}`);
    }
  };

  const exportFederationData = () => {
    if (!federation) return;
    
    const exportData = {
      federation: {
        name: federation.name,
        type: federation.federation_type,
        establishmentYear: federation.establishment_year,
        president: federation.president_name,
        secretary: federation.secretary_name,
        totalScore: federation.totalScore,
        percentage: federation.percentage
      },
      sections: Object.entries(federation.sectionDetails).map(([sectionNum, details]) => ({
        section: sectionTitles[parseInt(sectionNum)],
        score: details.sectionScore,
        maxScore: details.maxSectionScore,
        percentage: details.percentage,
        questions: details.questions
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${federation.name.replace(/\s+/g, '_')}_assessment_details.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Federation data exported successfully!");
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col bg-background/50">
        <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="w-full px-4 md:px-6 py-4">
            {/* Mobile Loading Layout */}
            <div className="flex md:hidden items-center justify-between">
              <div className="flex items-center gap-2">
                <Link href="/all-federation">
                  <Button variant="ghost" size="icon" className="rounded-full h-8 w-8">
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                </Link>
                <div className="flex flex-col min-w-0">
                  <h1 className="text-lg font-bold flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-primary shrink-0" />
                    <span>Loading...</span>
                  </h1>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <ThemeToggle />
                <UserNav />
              </div>
            </div>

            {/* Mobile Loading Selector */}
            <div className="flex md:hidden mt-3">
              <div className="w-full h-9 bg-muted rounded-md animate-pulse"></div>
            </div>

            {/* Desktop Loading Layout */}
            <div className="hidden md:flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Link href="/all-federation">
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                </Link>
                <div className="flex flex-col">
                  <h1 className="text-xl font-bold flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-primary" />
                    Loading Federation Details...
                  </h1>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span>Switch to:</span>
                  <div className="w-64 h-9 bg-muted rounded-md animate-pulse"></div>
                </div>
              </div>

              <div className="flex items-center gap-2 sm:gap-4">
                <div className="w-24 h-8 bg-muted rounded-md animate-pulse"></div>
                <ThemeToggle />
                <UserNav />
              </div>
            </div>
          </div>
        </header>
        
        <main className="flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto">
          <div className="space-y-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-muted rounded w-1/3"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-20 bg-muted rounded"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </main>
      </div>
    );
  }

  if (!federation) {
    return (
      <div className="flex min-h-screen flex-col bg-background/50">
        <main className="flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto">
          <div className="text-center py-12">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Federation not found</h3>
            <p className="text-muted-foreground mb-4">
              The federation you&apos;re looking for doesn&apos;t exist or has been removed.
            </p>
            <Link href="/all-federation">
              <Button>Back to All Federations</Button>
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-background/50">
      <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="w-full px-4 md:px-6 py-4">
          {/* Mobile Layout */}
          <div className="flex md:hidden items-center justify-between">
            <div className="flex items-center gap-2">
              <Link href="/all-federation">
                <Button variant="ghost" size="icon" className="rounded-full h-8 w-8">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex flex-col min-w-0">
                <h1 className="text-lg font-bold flex items-center gap-2 truncate">
                  <Building2 className="h-4 w-4 text-primary shrink-0" />
                  <span className="truncate">{federation.name}</span>
                </h1>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <ThemeToggle />
              <UserNav />
            </div>
          </div>

          {/* Mobile Federation Selector */}
          <div className="flex md:hidden mt-3">
            <div className="w-full">
              <Select
                value={federationId}
                onValueChange={handleFederationChange}
                disabled={isLoadingFederations}
              >
                <SelectTrigger className="w-full h-9 text-sm">
                  <div className="flex items-center gap-2 min-w-0">
                    <Building2 className="h-3 w-3 text-muted-foreground shrink-0" />
                    <span className="text-xs text-muted-foreground shrink-0">Switch to:</span>
                    <SelectValue placeholder={isLoadingFederations ? "Loading..." : "Select Federation"} />
                  </div>
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {isLoadingFederations ? (
                    <SelectItem value="loading" disabled>
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        <span className="text-sm">Loading...</span>
                      </div>
                    </SelectItem>
                  ) : allFederations.length === 0 ? (
                    <SelectItem value="no-data" disabled>
                      <span className="text-sm text-muted-foreground">No federations available</span>
                    </SelectItem>
                  ) : (
                    allFederations.map((fed) => (
                      <SelectItem key={fed.id} value={fed.id}>
                        <div className="flex items-center justify-between w-full min-w-0">
                          <span className="truncate flex-1 text-sm font-medium">{fed.id.toUpperCase()}</span>
                        </div>
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Link href="/all-federation">
                <Button variant="ghost" size="icon" className="rounded-full">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex flex-col">
                <h1 className="text-xl font-bold flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-primary" />
                  {federation.name}
                </h1>
                <p className="text-sm text-muted-foreground">Federation Assessment Details</p>
              </div>
            </div>

            {/* Desktop Federation Selector */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Switch to:</span>
                <Select
                  value={federationId}
                  onValueChange={handleFederationChange}
                  disabled={isLoadingFederations}
                >
                  <SelectTrigger className="w-64 h-9 bg-muted/30 border-muted-foreground/20 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-2 min-w-0">
                      <Building2 className="h-3 w-3 text-muted-foreground shrink-0" />
                      <SelectValue placeholder={isLoadingFederations ? "Loading federations..." : "Select Federation"} />
                    </div>
                  </SelectTrigger>
                  <SelectContent className="max-h-80 w-80">
                    {isLoadingFederations ? (
                      <SelectItem value="loading" disabled>
                        <div className="flex items-center gap-2">
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                          <span>Loading federations...</span>
                        </div>
                      </SelectItem>
                    ) : allFederations.length === 0 ? (
                      <SelectItem value="no-data" disabled>
                        <span className="text-muted-foreground">No federations available</span>
                      </SelectItem>
                    ) : (
                      allFederations.map((fed) => (
                        <SelectItem key={fed.id} value={fed.id} className="cursor-pointer">
                          <div className="flex items-center justify-between w-full min-w-0 py-1">
                            <div className="flex flex-col min-w-0 flex-1">
                              <span className="truncate font-semibold text-base">{fed.id.toUpperCase()}</span>
                            </div>
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2 sm:gap-4">
              <Button onClick={exportFederationData} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Export Data</span>
                <span className="sm:hidden">Export</span>
              </Button>
              <ThemeToggle />
              <UserNav />
            </div>
          </div>
        </div>
      </header>

      <main className="flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <TabsList className="grid w-full grid-cols-3 md:w-auto">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="sections">Sections</TabsTrigger>
              <TabsTrigger value="details">Q&A Details</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="mt-0">
            <div className="space-y-6 animate-in fade-in duration-300">
              {/* Federation Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Federation Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Federation Type</h4>
                      <p className="text-lg">{federation.federation_type}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Established</h4>
                      <p className="text-lg">{federation.establishment_year}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Youth Representation</h4>
                      <p className="text-lg">{federation.youth_percentage}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">President</h4>
                      <p className="text-lg">{federation.president_name}</p>
                      <p className="text-sm text-muted-foreground">Age: {federation.president_age}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Secretary</h4>
                      <p className="text-lg">{federation.secretary_name}</p>
                      <p className="text-sm text-muted-foreground">Age: {federation.secretary_age}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Performance Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Award className="h-5 w-5 text-yellow-500" />
                      Overall Score
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-primary">{federation.percentage}%</div>
                    <p className="text-sm text-muted-foreground">
                      {federation.totalScore}/{federation.maxScore} points
                    </p>
                    <div className="mt-2">
                      <Progress value={federation.percentage} className="h-2" />
                    </div>
                    <div className="mt-2">
                      <Badge variant={
                        (federation.percentage || 0) >= 75 ? "default" :
                        (federation.percentage || 0) >= 50 ? "secondary" : "destructive"
                      }>
                        {(federation.percentage || 0) >= 75 ? "Excellent" :
                         (federation.percentage || 0) >= 50 ? "Good" : "Needs Improvement"}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Target className="h-5 w-5 text-blue-500" />
                      Best Performing Section
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const bestSection = Object.entries(federation.sectionDetails)
                        .reduce((best, [num, details]) =>
                          details.percentage > best.percentage ? { num, ...details } : best,
                          { num: '', percentage: 0, title: '', sectionScore: 0, maxSectionScore: 0 }
                        );
                      return (
                        <div>
                          <div className="text-2xl font-bold text-blue-600">{bestSection.percentage}%</div>
                          <p className="text-sm font-medium">{bestSection.title}</p>
                          <p className="text-xs text-muted-foreground">
                            {bestSection.sectionScore}/{bestSection.maxSectionScore} points
                          </p>
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-green-500" />
                      Sections Completed
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const completedSections = Object.values(federation.sectionDetails)
                        .filter(section => section.questions.length > 0).length;
                      const totalSections = Object.keys(federation.sectionDetails).length;
                      return (
                        <div>
                          <div className="text-2xl font-bold text-green-600">
                            {completedSections}/{totalSections}
                          </div>
                          <p className="text-sm text-muted-foreground">Assessment sections</p>
                          <div className="mt-2">
                            <Progress
                              value={(completedSections / totalSections) * 100}
                              className="h-2"
                            />
                          </div>
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sections" className="mt-0">
            <div className="space-y-6 animate-in fade-in duration-300">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Section Performance Overview
                  </CardTitle>
                  <CardDescription>
                    Performance breakdown by assessment section
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(federation.sectionDetails).map(([sectionNum, details]) => (
                      <div key={sectionNum} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-semibold">{details.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              Section {sectionNum}
                            </p>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-primary">
                              {details.percentage}%
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {details.sectionScore}/{details.maxSectionScore} points
                            </div>
                          </div>
                        </div>
                        <Progress value={details.percentage} className="h-2" />
                        <div className="mt-2 flex justify-between text-xs text-muted-foreground">
                          <span>{details.questions.length} questions</span>
                          <Badge variant={
                            details.percentage >= 75 ? "default" :
                            details.percentage >= 50 ? "secondary" : "destructive"
                          } className="text-xs">
                            {details.percentage >= 75 ? "Excellent" :
                             details.percentage >= 50 ? "Good" : "Needs Improvement"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="details" className="mt-0">
            <div className="space-y-6 animate-in fade-in duration-300">
              {Object.entries(federation.sectionDetails).map(([sectionNum, details]) => (
                <Card key={sectionNum}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Section {sectionNum}: {details.title}
                    </CardTitle>
                    <CardDescription>
                      Score: {details.sectionScore}/{details.maxSectionScore} ({details.percentage}%)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {details.questions.length > 0 ? (
                        details.questions.map((qa, index) => (
                          <div key={index} className="border-l-4 border-primary/20 pl-4">
                            <div className="mb-2">
                              <h5 className="font-medium text-sm text-muted-foreground">
                                Question {index + 1}:
                              </h5>
                              <p className="font-medium">{qa.question}</p>
                            </div>
                            <div>
                              <h5 className="font-medium text-sm text-muted-foreground mb-1">
                                Answer:
                              </h5>
                              <p className="text-sm bg-muted/30 p-3 rounded-md">
                                {qa.answer || "No answer provided"}
                              </p>
                            </div>
                            {qa.score !== undefined && qa.maxScore !== undefined && (
                              <div className="mt-2 flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  Score: {qa.score}/{qa.maxScore}
                                </Badge>
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                          <p>No questions and answers available for this section</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
