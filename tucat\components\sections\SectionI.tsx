"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Trash2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { useAuth } from "@/components/AuthContext";
import { Federation } from "@/lib/types";
import { redis, safeRedisOperation } from "@/lib/upstash";

const FEDERATIONS = [
  { id: "bjsj", name: "<PERSON><PERSON><PERSON><PERSON>", bengali: "বিজেএসজে" },
  { id: "bftuc", name: "B<PERSON><PERSON>", bengali: "বিএফটিইউসি" },
  { id: "bjsd", name: "<PERSON><PERSON><PERSON>", bengali: "বিজেএসডি" },
  { id: "bjsf", name: "BJSF", bengali: "বিজেএসএফ" },
  { id: "blf", name: "BLF", bengali: "বিএলএফ" },
  { id: "bmsf", name: "BMSF", bengali: "বিএমএসএফ" },
  { id: "bsf", name: "BSF", bengali: "বিএসএফ" },
  { id: "bssf", name: "BSSF", bengali: "বিএসএসএফ" },
  { id: "btuc", name: "BTUC", bengali: "বিটিইউসি" },
  { id: "jsf", name: "JSF", bengali: "জেএসএফ" },
  { id: "jsj", name: "JSJ", bengali: "জেএসজে" },
  { id: "jsl", name: "JSL", bengali: "জেএসএল" },
  { id: "slf", name: "SLF", bengali: "এসএলএফ" },
  { id: "jsjb", name: "JSJB", bengali: "জেএসজেবি" },
];

export function SectionI() {
  const [committees, setCommittees] = useState([
    { id: 1, name: "", total: "", male: "", female: "" },
  ]);
  const { getCurrentFederationId, updateUserFederation, user } = useAuth();
  const [selectedFederation, setSelectedFederation] = useState("");
  const [availableFederations, setAvailableFederations] = useState(FEDERATIONS);
  const [formData, setFormData] = useState<Partial<Federation>>({});
  const { saveFederation, saveCommittees, isLoading, federation, committees: savedCommittees } = useAssessmentContext();
  const [youthPercentage, setYouthPercentage] = useState("0-25%");
  const [dataLoaded, setDataLoaded] = useState(false);

  // Debug logging to help identify issues
  useEffect(() => {
    console.log("SectionI Debug - Current user:", user);
    console.log("SectionI Debug - Federation data:", federation);
    console.log("SectionI Debug - Saved committees:", savedCommittees);
    console.log("SectionI Debug - Is loading:", isLoading);
  }, [user, federation, savedCommittees, isLoading]);

  // Load saved federation data when available
  useEffect(() => {
    console.log("Loading federation data:", federation);
    if (federation && Object.keys(federation).length > 0) {
      setFormData(federation);
      if (federation.youth_percentage) {
        setYouthPercentage(federation.youth_percentage);
      }
      if (federation.id) {
        setSelectedFederation(federation.id);
      }
      setDataLoaded(true);
      console.log("Federation data loaded successfully");
    }
  }, [federation]);

  // Load saved committees data when available
  useEffect(() => {
    console.log("Loading committees data:", savedCommittees);
    if (savedCommittees && savedCommittees.length > 0) {
      const formattedCommittees = savedCommittees.map((committee, index) => ({
        id: index + 1,
        name: committee.name || "",
        total: committee.total_members?.toString() || "",
        male: committee.male_members?.toString() || "",
        female: committee.female_members?.toString() || ""
      }));
      setCommittees(formattedCommittees);
      setDataLoaded(true);
      console.log("Committees data loaded successfully:", formattedCommittees);
    } else if (federation && federation.id && !isLoading) {
      // If we have a federation but no committees data, initialize with an empty committee
      // This ensures the committee form is ready for input
      setCommittees([{ id: 1, name: "", total: "", male: "", female: "" }]);
      console.log("Initialized empty committee for federation:", federation.id);
    }
  }, [savedCommittees, federation, isLoading]);

  // Load available federations and set the federation based on the logged-in user's federation_id
  useEffect(() => {
    const loadAvailableFederations = async () => {
      try {
        const users = await safeRedisOperation(() => redis.get('users'), []);
        let userList = [];
        
        if (users && typeof users === 'string') {
          try {
            userList = JSON.parse(users);
          } catch (parseError) {
            console.error('Error parsing users data:', parseError);
            userList = [];
          }
        } else if (users && typeof users === 'object') {
          userList = Array.isArray(users) ? users : [];
        }

        // Filter out federations that are already selected by other users
        const takenFederations = new Set(
          userList
            .filter((u: any) => u.id !== user?.id && u.federation_id)
            .map((u: any) => u.federation_id)
        );

        const available = FEDERATIONS.filter(fed => !takenFederations.has(fed.id));
        setAvailableFederations(available);
        
        console.log("Available federations loaded:", available);
      } catch (error) {
        console.error('Error loading available federations:', error);
        // Set all federations as available if we can't check taken ones
        setAvailableFederations(FEDERATIONS);
        toast.error('Could not verify federation availability. All federations shown.');
      }
    };

    loadAvailableFederations();
    const userFederationId = getCurrentFederationId();
    console.log("User federation ID:", userFederationId);
    
    if (userFederationId) {
      // Try to find a matching federation in our predefined list
      const matchingFederation = FEDERATIONS.find(f => f.id === userFederationId);
      if (matchingFederation) {
        setSelectedFederation(userFederationId);

        // Pre-populate the federation name in the form data only if not already loaded
        setFormData(prev => ({
          ...prev,
          name: prev.name || matchingFederation.name
        }));
        console.log("Set federation from user credentials:", userFederationId);
      }
    }
  }, [getCurrentFederationId, user]);

  // Determine if federation selection should be enabled
  const isFederationSelectionEnabled = !getCurrentFederationId();

  const validateForm = () => {
    const requiredFields = [
      'name',
      'federation_type',
      'establishment_year',
      'president_name',
      'president_age',
      'secretary_name',
      'secretary_age'
    ];

    const missingFields = requiredFields.filter(field => !formData[field as keyof Federation]);

    if (missingFields.length > 0) {
      toast.error(`Please fill in all required fields`);
      return false;
    }

    // Check if at least one committee has complete information
    const hasValidCommittee = committees.some(committee =>
      committee.name && committee.total && committee.male && committee.female
    );

    if (!hasValidCommittee) {
      toast.error("Please add at least one committee with complete information");
      return false;
    }

    // Check if youth percentage is selected
    if (!youthPercentage) {
      toast.error("Please select the youth percentage");
      return false;
    }

    return true;
  };

  const addCommittee = () => {
    setCommittees([
      ...committees,
      { id: committees.length + 1, name: "", total: "", male: "", female: "" },
    ]);
  };

  const deleteCommittee = (id: number) => {
    setCommittees(committees.filter(committee => committee.id !== id));
  };

  const checkFederationAvailability = async (federationId: string) => {
    try {
      const users = await safeRedisOperation(() => redis.get('users'), []);
      let userList = [];
      
      if (users && typeof users === 'string') {
        try {
          userList = JSON.parse(users);
        } catch (parseError) {
          console.error('Error parsing users data:', parseError);
          // If we can't parse user data, assume federation is available
          return true;
        }
      } else if (users && typeof users === 'object') {
        userList = Array.isArray(users) ? users : [];
      }

      // Check if any other user has already selected this federation
      const federationInUse = userList.some(
        (u: any) => u.federation_id === federationId && u.id !== user?.id
      );

      return !federationInUse;
    } catch (error) {
      console.error('Error checking federation availability:', error);
      // If we can't check availability, assume it's available to prevent blocking the user
      return true;
    }
  };

  const handleSave = async () => {
    try {
      if (!validateForm()) return;

      // Check if a federation is selected
      if (!selectedFederation) {
        toast.error("Please select a federation from the dropdown.");
        return;
      }

      // Get the federation ID from auth context or use the selected one
      let federationId = getCurrentFederationId() || selectedFederation;

      // If the user is selecting a federation for the first time, check its availability
      if (isFederationSelectionEnabled && selectedFederation) {
        const isAvailable = await checkFederationAvailability(selectedFederation);
        if (!isAvailable) {
          toast.error("This federation has already been selected by another user. Please choose a different federation.");
          return;
        }

        try {
          const updated = await updateUserFederation(selectedFederation);
          if (!updated) {
            toast.error("Failed to update your federation. Please try again.");
            return;
          }
          federationId = selectedFederation;
        } catch (error) {
          console.error('Error updating user federation:', error);
          toast.error("Failed to update your federation. Please try again.");
          return;
        }
      }

      // Find the selected federation details
      const selectedFed = FEDERATIONS.find(f => f.id === federationId);

      // Save federation data first
      try {
        const savedFederation = await saveFederation({
          ...formData,
          id: federationId, // Explicitly set the federation ID
          name: formData.name || selectedFed?.name || "",
          federation_type: formData.federation_type || "",
          establishment_year: Number(formData.establishment_year) || 0,
          president_name: formData.president_name || "",
          president_age: Number(formData.president_age) || 0,
          secretary_name: formData.secretary_name || "",
          secretary_age: Number(formData.secretary_age) || 0,
          youth_percentage: youthPercentage,
        });

        // Only proceed with saving committees if we have a valid federation ID
        if (!savedFederation?.id) {
          toast.error("Failed to save federation data");
          return;
        }

        // Save committees with the confirmed federation ID
        try {
          await saveCommittees(
            committees.map(committee => ({
              name: committee.name,
              total_members: Number(committee.total) || 0,
              male_members: Number(committee.male) || 0,
              female_members: Number(committee.female) || 0,
            }))
          );
          toast.success("Federation and committee data saved successfully");
        } catch (error) {
          console.error("Failed to save committees:", error);
          toast.error("Failed to save committee data. Federation data was saved successfully.");
        }
      } catch (error) {
        console.error("Failed to save federation:", error);
        toast.error("Failed to save federation data");
      }
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("An unexpected error occurred while saving");
    }
  };

  return (
    <div className="space-y-8">
      <Card className="p-6">
        <h2 className="text-2xl font-bold text-center mb-2">
          INTRODUCTION ABOUT THE FEDERATION
        </h2>
        <h3 className="text-xl text-center mb-6">ফেডারেশন পরিচিতি</h3>

        {/* Add loading indicator */}
        {isLoading && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-800">Loading your saved data...</p>
          </div>
        )}

        {/* Add data status indicator */}
        {!isLoading && dataLoaded && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-green-800">Your previously saved data has been loaded.</p>
          </div>
        )}

        <div className="space-y-6">
          <div className="space-y-2">
            <Label>
              Federation
              <span className="block text-sm text-muted-foreground">
                ফেডারেশন
              </span>
            </Label>
            <Select
              value={selectedFederation}
              onValueChange={setSelectedFederation}
              disabled={!isFederationSelectionEnabled}
            >
              <SelectTrigger className="w-full bg-gray-50">
                <SelectValue placeholder="Your federation" />
              </SelectTrigger>
              <SelectContent>
                {availableFederations.map((federation) => (
                  <SelectItem key={federation.id} value={federation.id}>
                    {federation.name} - {federation.bengali}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground mt-1">
              <span className="font-medium">Note:</span> {isFederationSelectionEnabled ? "Please select your federation from the dropdown above." : "Federation is automatically selected based on your login credentials and cannot be changed"}
            </p>
          </div>

          <div className="grid gap-4">
            <div className="space-y-2">
              <Label>
                Name of the Federation
                <span className="block text-sm text-muted-foreground">
                ফেডারেশনের নাম
                </span>
              </Label>
              <Input
                value={formData.name || ""}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter federation name"
              />
            </div>

            <div className="space-y-2">
              <Label>
                Type of the Federation
                <span className="block text-sm text-muted-foreground">
                ফেডারেশনের প্রকার
                </span>
              </Label>
              <Input
                value={formData.federation_type || ""}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, federation_type: e.target.value }))
                }
                placeholder="Enter federation type"
              />
            </div>

            <div className="space-y-2">
              <Label>
                Establishment Year
                <span className="block text-sm text-muted-foreground">
                প্রতিষ্ঠা সাল
                </span>
              </Label>
              <Input
                type="number"
                value={formData.establishment_year || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    establishment_year: parseInt(e.target.value),
                  }))
                }
                placeholder="Enter year"
              />
            </div>

            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label>
                  Name of the President
                  <span className="block text-sm text-muted-foreground">
                    সভাপতির নাম
                  </span>
                </Label>
                <Input
                  value={formData.president_name || ""}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      president_name: e.target.value,
                    }))
                  }
                  placeholder="Enter president name"
                />
              </div>
              <div className="space-y-2">
                <Label>
                  Age of the President
                  <span className="block text-sm text-muted-foreground">
                    সভাপতির বয়স
                  </span>
                </Label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    value={formData.president_age || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        president_age: parseInt(e.target.value),
                      }))
                    }
                    placeholder="Age"
                  />
                  <span>years</span>
                </div>
              </div>
            </div>

            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label>
                  Name of the Secretary
                  <span className="block text-sm text-muted-foreground">
                    সাধারণ সম্পাদকের নাম
                  </span>
                </Label>
                <Input
                  value={formData.secretary_name || ""}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      secretary_name: e.target.value,
                    }))
                  }
                  placeholder="Enter secretary name"
                />
              </div>
              <div className="space-y-2">
                <Label>
                  Age of the Secretary
                  <span className="block text-sm text-muted-foreground">
                    সাধারণ সম্পাদকের বয়স
                  </span>
                </Label>
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    value={formData.secretary_age || ""}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        secretary_age: parseInt(e.target.value),
                      }))
                    }
                    placeholder="Age"
                  />
                  <span>years</span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <Label className="block text-base font-medium mb-1">
              Name of the different Committees, and total number of Members in the committees
              <span className="block text-sm font-normal text-muted-foreground mt-1">
                বিভিন্ন কমিটির নাম এবং কমিটিগুলিতে মোট সদস্য সংখ্যা
              </span>
              <span className="block text-sm font-normal text-muted-foreground mt-1">
                (Including Federation Committee, Sub-committees like Youth Committee, Women Committee, or anything else like this)
              </span>
              <span className="block text-sm font-normal text-muted-foreground mt-1">
                (ফেডারেশন কমিটি, উপ কমিটি যেমন- যুব কমিটি, মহিলা কমিটি বা এর মতো অন্য যেকোনো কমিটি সহ)
              </span>
            </Label>
            <p className="text-sm text-muted-foreground mb-4">
              Please write Name of the committee and then Number of Members, and again Male and Female disaggregated Number in the committees
              <span className="block mt-1">
                অনুগ্রহ করে কমিটির নাম, তারপর সদস্য সংখ্যার পাশাপাশি পুরুষ এবর মহিলা সদস্য সংখ্যা আলাদাভাবে লিখুন কমিটিগুলিতে।
              </span>
            </p>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">
                    Name of the Committee
                    <span className="block text-xs font-normal">কমিটির নাম</span>
                  </TableHead>
                  <TableHead>
                    Total # of Members
                    <span className="block text-xs font-normal">মোট সদস্য সংখ্যা</span>
                  </TableHead>
                  <TableHead>
                    # of Male Members
                    <span className="block text-xs font-normal">পুরুষ সদস্য সংখ্যা</span>
                  </TableHead>
                  <TableHead>
                    # of Female Members
                    <span className="block text-xs font-normal">নারী সদস্য সংখ্যা</span>
                  </TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {committees.map((committee) => (
                  <TableRow key={committee.id}>
                    <TableCell>
                      <Input
                        placeholder="Committee name"
                        value={committee.name}
                        onChange={(e) =>
                          setCommittees(
                            committees.map((c) =>
                              c.id === committee.id
                                ? { ...c, name: e.target.value }
                                : c
                            )
                          )
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={committee.total}
                        onChange={(e) =>
                          setCommittees(
                            committees.map((c) =>
                              c.id === committee.id
                                ? { ...c, total: e.target.value }
                                : c
                            )
                          )
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={committee.male}
                        onChange={(e) =>
                          setCommittees(
                            committees.map((c) =>
                              c.id === committee.id
                                ? { ...c, male: e.target.value }
                                : c
                            )
                          )
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={committee.female}
                        onChange={(e) =>
                          setCommittees(
                            committees.map((c) =>
                              c.id === committee.id
                                ? { ...c, female: e.target.value }
                                : c
                            )
                          )
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-destructive hover:text-destructive/90"
                        onClick={() => deleteCommittee(committee.id)}
                        disabled={committees.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={addCommittee}
            >
              Add Committee
              <span className="ml-2 text-sm">কমিটি যোগ করুন</span>
            </Button>
          </div>

          <div className="space-y-4">
            <Label>
              What percentage of the committee members are youth?
              <span className="block text-sm text-muted-foreground">
                কমিটির কত শতাংশ সদস্য যুবক?
              </span>
            </Label>
            <RadioGroup
              value={youthPercentage}
              onValueChange={setYouthPercentage}
              className="grid grid-cols-2 sm:grid-cols-4 gap-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="0-25%" id="0-25" />
                <Label htmlFor="0-25">0-25%</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="26-50%" id="26-50" />
                <Label htmlFor="26-50">26-50%</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="51-75%" id="51-75" />
                <Label htmlFor="51-75">51-75%</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="above-75%" id="above-75" />
                <Label htmlFor="above-75">Above 75%</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="pt-6 space-x-4 flex justify-end">
            <Button
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? "Saving..." : "Save Federation Data"}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}