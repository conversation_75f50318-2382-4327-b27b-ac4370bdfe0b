/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/federation/[id]/route";
exports.ids = ["app/api/federation/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/federation/[id]/route.ts":
/*!******************************************!*\
  !*** ./app/api/federation/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_upstash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/upstash */ \"(rsc)/./lib/upstash.ts\");\n\n\n// Function to calculate section score using the same logic as useAssessment.ts\nfunction calculateSectionScore(assessment, sectionNumber) {\n    if (!assessment) return 0;\n    let sectionScore = 0;\n    // Section III scoring\n    if (sectionNumber === 3) {\n        if (assessment.vision_mission_status === '4') sectionScore += 3;\n        else if (assessment.vision_mission_status === '3') sectionScore += 2;\n        else if (assessment.vision_mission_status === '2') sectionScore += 1;\n        // Yes/No/NA questions scoring\n        if (assessment.vision_posted === 'yes') sectionScore += 1;\n        if (assessment.vision_in_documents === 'yes') sectionScore += 1;\n        if (assessment.vision_for_planning === 'yes') sectionScore += 1;\n        if (assessment.decision_making === 'a') sectionScore += 3;\n        else if (assessment.decision_making === 'b') sectionScore += 2;\n        else if (assessment.decision_making === 'c') sectionScore += 1;\n        if (assessment.emerging_issues_handling === 'a') sectionScore += 3;\n        else if (assessment.emerging_issues_handling === 'b') sectionScore += 2;\n        else if (assessment.emerging_issues_handling === 'c') sectionScore += 1;\n        if (assessment.leadership_development_plan === 'a') sectionScore += 3;\n        else if (assessment.leadership_development_plan === 'b') sectionScore += 2;\n        else if (assessment.leadership_development_plan === 'c') sectionScore += 1;\n    } else if (sectionNumber === 4) {\n        if (assessment.organizational_structure === 'a') sectionScore += 2;\n        else if (assessment.organizational_structure === 'b') sectionScore += 1;\n        if (assessment.roles_defined === 'a') sectionScore += 2;\n        else if (assessment.roles_defined === 'b') sectionScore += 1;\n    } else if (sectionNumber === 5) {\n        if (assessment.management_approach === 'a') sectionScore += 2;\n        else if (assessment.management_approach === 'c') sectionScore += 1;\n        if (assessment.authority_delegation === 'a') sectionScore += 2;\n        else if (assessment.authority_delegation === 'c') sectionScore += 1;\n        if (assessment.decision_making_process === '4') sectionScore += 3;\n        else if (assessment.decision_making_process === '3') sectionScore += 2;\n        else if (assessment.decision_making_process === '2') sectionScore += 1;\n        if (assessment.deputy_availability === 'a') sectionScore += 2;\n        else if (assessment.deputy_availability === 'b') sectionScore += 1;\n        if (assessment.transition_plan === 'a') sectionScore += 2;\n        else if (assessment.transition_plan === 'b') sectionScore += 1;\n    } else if (sectionNumber === 6) {\n        if (assessment.workers_involvement_level === '4') sectionScore += 4;\n        else if (assessment.workers_involvement_level === '3') sectionScore += 3;\n        else if (assessment.workers_involvement_level === '2') sectionScore += 2;\n        else if (assessment.workers_involvement_level === '1') sectionScore += 1;\n        if (assessment.involve_program_activities === 'yes') sectionScore += 1;\n        if (assessment.involve_leaders_orientation === 'yes') sectionScore += 1;\n        if (assessment.solicit_feedback === 'yes') sectionScore += 1;\n        if (assessment.regular_interaction === 'yes') sectionScore += 1;\n        if (assessment.share_results === 'yes') sectionScore += 1;\n    } else if (sectionNumber === 7) {\n        if (assessment.cultural_gender_consideration === '4') sectionScore += 3;\n        else if (assessment.cultural_gender_consideration === '3') sectionScore += 2;\n        else if (assessment.cultural_gender_consideration === '2') sectionScore += 1;\n        if (assessment.consider_local_culture === 'yes') sectionScore += 1;\n        if (assessment.documented_guidelines === 'yes') sectionScore += 1;\n        if (assessment.provide_training === 'yes') sectionScore += 1;\n        if (assessment.use_assessment_findings === 'yes') sectionScore += 1;\n    } else if (sectionNumber === 8) {\n        if (assessment.representation_effectiveness === '4') sectionScore += 3;\n        else if (assessment.representation_effectiveness === '3') sectionScore += 2;\n        else if (assessment.representation_effectiveness === '2') sectionScore += 1;\n        if (assessment.member_involvement === '4') sectionScore += 3;\n        else if (assessment.member_involvement === '3') sectionScore += 2;\n        else if (assessment.member_involvement === '2') sectionScore += 1;\n        if (assessment.bargaining_strategy === 'a') sectionScore += 2;\n        else if (assessment.bargaining_strategy === 'b') sectionScore += 1;\n    } else if (sectionNumber === 9) {\n        if (assessment.communication_effectiveness === '4') sectionScore += 3;\n        else if (assessment.communication_effectiveness === '3') sectionScore += 2;\n        else if (assessment.communication_effectiveness === '2') sectionScore += 1;\n        if (assessment.member_engagement === '4') sectionScore += 3;\n        else if (assessment.member_engagement === '3') sectionScore += 2;\n        else if (assessment.member_engagement === '2') sectionScore += 1;\n        if (assessment.participation_opportunities === '4') sectionScore += 3;\n        else if (assessment.participation_opportunities === '3') sectionScore += 2;\n        else if (assessment.participation_opportunities === '2') sectionScore += 1;\n        if (assessment.feedback_collection === 'a') sectionScore += 2;\n        else if (assessment.feedback_collection === 'b') sectionScore += 1;\n    } else if (sectionNumber === 10) {\n        if (assessment.fee_collection === '4') sectionScore += 3;\n        else if (assessment.fee_collection === '3') sectionScore += 2;\n        else if (assessment.fee_collection === '2') sectionScore += 1;\n        if (assessment.financial_management === '4') sectionScore += 3;\n        else if (assessment.financial_management === '3') sectionScore += 2;\n        else if (assessment.financial_management === '2') sectionScore += 1;\n        if (assessment.financial_planning === '4') sectionScore += 3;\n        else if (assessment.financial_planning === '3') sectionScore += 2;\n        else if (assessment.financial_planning === '2') sectionScore += 1;\n        if (assessment.financial_system_quality === '4') sectionScore += 3;\n        else if (assessment.financial_system_quality === '3') sectionScore += 2;\n        else if (assessment.financial_system_quality === '2') sectionScore += 1;\n        if (assessment.has_cash_system === 'yes') sectionScore += 1;\n        if (assessment.uses_accounting_software === 'yes') sectionScore += 1;\n        if (assessment.has_chart_accounts === 'yes') sectionScore += 1;\n        if (assessment.reconciles_monthly === 'yes') sectionScore += 1;\n    } else if (sectionNumber === 11) {\n        if (assessment.audit_system_quality === '4') sectionScore += 3;\n        else if (assessment.audit_system_quality === '3') sectionScore += 2;\n        else if (assessment.audit_system_quality === '2') sectionScore += 1;\n        if (assessment.requires_annual_audit === 'yes') sectionScore += 1;\n        if (assessment.regularly_audited === 'yes') sectionScore += 1;\n        if (assessment.auditor_selection === 'yes') sectionScore += 1;\n        if (assessment.audit_manager === 'yes') sectionScore += 1;\n        if (assessment.implements_recommendations === 'yes') sectionScore += 1;\n        if (assessment.shares_reports === 'yes') sectionScore += 1;\n        if (assessment.report_provides_info === 'yes') sectionScore += 1;\n    }\n    return sectionScore;\n}\nasync function GET(request, { params }) {\n    try {\n        const { id: federationId } = await params;\n        // Fetch federation basic data\n        const federationData = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(`federation:${federationId}`));\n        if (!federationData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Federation not found'\n            }, {\n                status: 404\n            });\n        }\n        // Parse federation data\n        let federation;\n        if (typeof federationData === 'string') {\n            try {\n                federation = JSON.parse(federationData);\n            } catch  {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid federation data'\n                }, {\n                    status: 500\n                });\n            }\n        } else {\n            federation = federationData;\n        }\n        // Fetch all assessments for this federation\n        const assessmentKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys(`assessment:${federationId}:*`));\n        const assessmentsRaw = await Promise.all(assessmentKeys.map((key)=>(0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key))));\n        // Parse assessments\n        const assessments = assessmentsRaw.map((a)=>{\n            if (!a) return null;\n            if (typeof a === 'string') {\n                try {\n                    return JSON.parse(a);\n                } catch  {\n                    return null;\n                }\n            }\n            return a;\n        }).filter((assessment)=>assessment !== null && typeof assessment === 'object' && 'section_number' in assessment);\n        // Section max scores (raw scores from useAssessment.ts)\n        const sectionMaxScores = {\n            2: 0,\n            3: 14,\n            4: 4,\n            5: 11,\n            6: 9,\n            7: 7,\n            8: 8,\n            9: 11,\n            10: 16,\n            11: 10\n        };\n        // Section titles\n        const sectionTitles = {\n            2: \"Legal Status and Compliance\",\n            3: \"Leadership\",\n            4: \"Organizational Structure\",\n            5: \"Management\",\n            6: \"Worker Participation\",\n            7: \"Culture and Gender\",\n            8: \"Collective Bargaining\",\n            9: \"Member Engagement\",\n            10: \"Financial Stability\",\n            11: \"Audit & Compliance\"\n        };\n        // Calculate section scores and build detailed response\n        const sectionScores = {};\n        const sectionDetails = {};\n        let totalScore = 0;\n        let maxPossible = 0;\n        // Process each section that has assessments\n        const allSectionNumbers = new Set([\n            ...Object.keys(sectionMaxScores).map(Number),\n            ...assessments.map((a)=>a.section_number)\n        ]);\n        for (const sectionNum of Array.from(allSectionNumbers).sort()){\n            const sectionAssessment = assessments.find((a)=>a.section_number === sectionNum);\n            // Calculate raw score using the same logic as useAssessment.ts\n            const rawScore = calculateSectionScore(sectionAssessment, sectionNum);\n            const maxSectionScore = sectionMaxScores[sectionNum] || 0;\n            sectionScores[sectionNum] = rawScore;\n            // Only include sections with max scores > 0 in total calculation\n            if (maxSectionScore > 0) {\n                totalScore += rawScore;\n                maxPossible += maxSectionScore;\n            }\n            // Build section details with questions and answers\n            const questions = extractQuestionsFromAssessment(sectionAssessment, sectionNum);\n            sectionDetails[sectionNum] = {\n                title: sectionTitles[sectionNum] || `Section ${sectionNum}`,\n                questions,\n                sectionScore: rawScore,\n                maxSectionScore,\n                percentage: maxSectionScore > 0 ? Math.round(rawScore / maxSectionScore * 100) : 0\n            };\n        }\n        const percentage = maxPossible > 0 ? Math.round(totalScore / maxPossible * 100) : 0;\n        const response = {\n            ...federation,\n            assessments,\n            totalScore,\n            maxScore: maxPossible,\n            percentage,\n            sectionScores,\n            sectionDetails\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Error in GET /api/federation/[id]:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch federation details',\n            details: error?.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to extract questions and answers from assessment data\nfunction extractQuestionsFromAssessment(assessment, sectionNumber) {\n    if (!assessment) return [];\n    const questions = [];\n    // Define questions and answer mappings for each section\n    const sectionData = {\n        2: {\n            'legal_registration_status': {\n                question: 'What is the legal registration status of the Federation?',\n                answerMap: {\n                    'a': 'Registered with proper documentation',\n                    'b': 'Registered but documentation incomplete',\n                    'c': 'Registration in progress',\n                    'd': 'Not registered'\n                }\n            },\n            'registration_authority': {\n                question: 'Which authority is the Federation registered with?'\n            }\n        },\n        3: {\n            'vision_mission_status': {\n                question: 'Does the Federation have a clear vision and mission?',\n                answerMap: {\n                    '1': 'No vision or mission statement',\n                    '2': 'Vision/mission exists but not well communicated',\n                    '3': 'Vision/mission exists and somewhat communicated',\n                    '4': 'Clear vision/mission, well communicated to all'\n                }\n            },\n            'vision_posted': {\n                question: 'Is the vision/mission posted in visible locations?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'vision_in_documents': {\n                question: 'Is the vision/mission included in official documents?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'vision_for_planning': {\n                question: 'Is the vision/mission used for strategic planning?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'decision_making': {\n                question: 'How are decisions made in the Federation?',\n                answerMap: {\n                    'a': 'Democratic process with member consultation',\n                    'b': 'Executive committee decisions',\n                    'c': 'Top-down leadership decisions',\n                    'd': 'Ad-hoc decision making'\n                }\n            },\n            'emerging_issues_handling': {\n                question: 'How does the Federation handle emerging issues?',\n                answerMap: {\n                    'a': 'Proactive approach with systematic response',\n                    'b': 'Reactive approach with some planning',\n                    'c': 'Minimal response to issues',\n                    'd': 'No systematic approach'\n                }\n            },\n            'leadership_development_plan': {\n                question: 'Does the Federation have a leadership development plan?',\n                answerMap: {\n                    'a': 'Comprehensive leadership development program',\n                    'b': 'Basic leadership training available',\n                    'c': 'Informal leadership development',\n                    'd': 'No leadership development plan'\n                }\n            }\n        },\n        4: {\n            'organizational_structure': {\n                question: 'What is the organizational structure of the Federation?',\n                answerMap: {\n                    'a': 'Well-defined hierarchical structure',\n                    'b': 'Basic organizational structure',\n                    'c': 'Informal structure',\n                    'd': 'No clear structure'\n                }\n            },\n            'roles_defined': {\n                question: 'Are roles and responsibilities clearly defined?',\n                answerMap: {\n                    'a': 'All roles clearly documented and communicated',\n                    'b': 'Most roles defined',\n                    'c': 'Some roles defined',\n                    'd': 'Roles not clearly defined'\n                }\n            }\n        },\n        5: {\n            'management_approach': {\n                question: 'How is the Federation managed?',\n                answerMap: {\n                    'a': 'Professional management with clear processes',\n                    'b': 'Structured management approach',\n                    'c': 'Basic management practices',\n                    'd': 'Informal management'\n                }\n            },\n            'authority_delegation': {\n                question: 'How is authority delegated in the Federation?',\n                answerMap: {\n                    'a': 'Clear delegation with proper oversight',\n                    'b': 'Some delegation with monitoring',\n                    'c': 'Limited delegation',\n                    'd': 'Centralized authority, no delegation'\n                }\n            },\n            'decision_making_process': {\n                question: 'What is the decision-making process?',\n                answerMap: {\n                    '1': 'No formal process',\n                    '2': 'Basic process exists',\n                    '3': 'Structured process with documentation',\n                    '4': 'Comprehensive process with stakeholder involvement'\n                }\n            },\n            'deputy_availability': {\n                question: 'Is there a deputy available for key positions?',\n                answerMap: {\n                    'a': 'All key positions have designated deputies',\n                    'b': 'Most positions have deputies',\n                    'c': 'Some positions have deputies',\n                    'd': 'No deputy system in place'\n                }\n            },\n            'transition_plan': {\n                question: 'Is there a succession/transition plan?',\n                answerMap: {\n                    'a': 'Comprehensive succession planning',\n                    'b': 'Basic transition planning',\n                    'c': 'Informal succession arrangements',\n                    'd': 'No succession planning'\n                }\n            }\n        },\n        6: {\n            'workers_involvement_level': {\n                question: 'What is the level of workers\\' involvement in the Federation?',\n                answerMap: {\n                    '1': 'Minimal involvement',\n                    '2': 'Basic involvement in some activities',\n                    '3': 'Good involvement in most activities',\n                    '4': 'High level of involvement in all activities'\n                }\n            },\n            'involve_program_activities': {\n                question: 'Are workers involved in program activities?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'involve_leaders_orientation': {\n                question: 'Are workers involved in leadership orientation?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'solicit_feedback': {\n                question: 'Does the Federation solicit feedback from workers?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'regular_interaction': {\n                question: 'Is there regular interaction with workers?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'share_results': {\n                question: 'Are results shared with workers?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            }\n        }\n    };\n    // Continue with remaining sections...\n    const remainingSections = {\n        7: {\n            'cultural_gender_consideration': {\n                question: 'How does the Federation consider cultural and gender aspects?',\n                answerMap: {\n                    '1': 'No consideration',\n                    '2': 'Basic awareness',\n                    '3': 'Good consideration with some policies',\n                    '4': 'Comprehensive cultural and gender policies'\n                }\n            },\n            'consider_local_culture': {\n                question: 'Does the Federation consider local culture?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'documented_guidelines': {\n                question: 'Are there documented cultural guidelines?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'provide_training': {\n                question: 'Does the Federation provide cultural sensitivity training?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'use_assessment_findings': {\n                question: 'Are assessment findings used for improvement?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            }\n        },\n        8: {\n            'representation_effectiveness': {\n                question: 'How effective is the Federation\\'s representation?',\n                answerMap: {\n                    '1': 'Poor representation',\n                    '2': 'Basic representation',\n                    '3': 'Good representation',\n                    '4': 'Excellent representation'\n                }\n            },\n            'member_involvement': {\n                question: 'What is the level of member involvement in collective bargaining?',\n                answerMap: {\n                    '1': 'No member involvement',\n                    '2': 'Limited involvement',\n                    '3': 'Good member involvement',\n                    '4': 'High member involvement'\n                }\n            },\n            'bargaining_strategy': {\n                question: 'What is the Federation\\'s bargaining strategy?',\n                answerMap: {\n                    'a': 'Comprehensive strategic approach',\n                    'b': 'Structured bargaining approach',\n                    'c': 'Basic bargaining tactics',\n                    'd': 'Ad-hoc bargaining'\n                }\n            }\n        },\n        9: {\n            'communication_effectiveness': {\n                question: 'How effective is the Federation\\'s communication?',\n                answerMap: {\n                    '1': 'Poor communication',\n                    '2': 'Basic communication',\n                    '3': 'Good communication',\n                    '4': 'Excellent communication'\n                }\n            },\n            'member_engagement': {\n                question: 'What is the level of member engagement?',\n                answerMap: {\n                    '1': 'Low engagement',\n                    '2': 'Basic engagement',\n                    '3': 'Good engagement',\n                    '4': 'High engagement'\n                }\n            },\n            'participation_opportunities': {\n                question: 'What participation opportunities are available?',\n                answerMap: {\n                    '1': 'Limited opportunities',\n                    '2': 'Basic opportunities',\n                    '3': 'Good opportunities',\n                    '4': 'Extensive opportunities'\n                }\n            },\n            'feedback_collection': {\n                question: 'How does the Federation collect member feedback?',\n                answerMap: {\n                    'a': 'Systematic feedback collection',\n                    'b': 'Regular feedback sessions',\n                    'c': 'Occasional feedback collection',\n                    'd': 'No formal feedback system'\n                }\n            }\n        },\n        10: {\n            'fee_collection': {\n                question: 'How does the Federation collect fees?',\n                answerMap: {\n                    '1': 'No systematic collection',\n                    '2': 'Basic collection system',\n                    '3': 'Good collection system',\n                    '4': 'Excellent systematic collection'\n                }\n            },\n            'financial_management': {\n                question: 'How is financial management handled?',\n                answerMap: {\n                    '1': 'Poor financial management',\n                    '2': 'Basic financial management',\n                    '3': 'Good financial management',\n                    '4': 'Excellent financial management'\n                }\n            },\n            'financial_planning': {\n                question: 'What is the approach to financial planning?',\n                answerMap: {\n                    '1': 'No financial planning',\n                    '2': 'Basic financial planning',\n                    '3': 'Good financial planning',\n                    '4': 'Comprehensive financial planning'\n                }\n            },\n            'financial_system_quality': {\n                question: 'What is the quality of the financial system?',\n                answerMap: {\n                    '1': 'Poor financial systems',\n                    '2': 'Basic financial systems',\n                    '3': 'Good financial systems',\n                    '4': 'Excellent financial systems'\n                }\n            },\n            'has_cash_system': {\n                question: 'Does the Federation have a cash management system?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'uses_accounting_software': {\n                question: 'Does the Federation use accounting software?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'has_chart_accounts': {\n                question: 'Does the Federation have a chart of accounts?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'reconciles_monthly': {\n                question: 'Does the Federation reconcile accounts monthly?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            }\n        },\n        11: {\n            'audit_system_quality': {\n                question: 'What is the quality of the audit system?',\n                answerMap: {\n                    '1': 'No audit system',\n                    '2': 'Basic audit system',\n                    '3': 'Good audit system',\n                    '4': 'Excellent audit system'\n                }\n            },\n            'requires_annual_audit': {\n                question: 'Does the Federation require annual audits?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'regularly_audited': {\n                question: 'Is the Federation regularly audited?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'auditor_selection': {\n                question: 'How are auditors selected?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'audit_manager': {\n                question: 'Who manages the audit process?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'implements_recommendations': {\n                question: 'Does the Federation implement audit recommendations?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'shares_reports': {\n                question: 'Are audit reports shared with stakeholders?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'report_provides_info': {\n                question: 'Do audit reports provide useful information?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            }\n        }\n    };\n    // Merge all sections\n    const allSections = {\n        ...sectionData,\n        ...remainingSections\n    };\n    const sectionQuestionMap = allSections[sectionNumber] || {};\n    // Extract questions and answers\n    Object.entries(sectionQuestionMap).forEach(([field, config])=>{\n        const rawAnswer = assessment[field];\n        if (rawAnswer !== undefined && rawAnswer !== null && rawAnswer !== '') {\n            let decodedAnswer = rawAnswer;\n            // Decode answer if mapping exists\n            if (config.answerMap && config.answerMap[rawAnswer]) {\n                decodedAnswer = config.answerMap[rawAnswer];\n            }\n            questions.push({\n                question: config.question,\n                answer: decodedAnswer\n            });\n        }\n    });\n    return questions;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/federation/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/upstash.ts":
/*!************************!*\
  !*** ./lib/upstash.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   safeRedisOperation: () => (/* binding */ safeRedisOperation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: \"https://ample-bear-14398.upstash.io\",\n    token: \"ATg-AAIjcDExMDY2ODQwYjIwNDI0MWQxODg4MjExMzg3YTMzYTRhNnAxMA\"\n});\n// Helper function to handle Redis operations with error checking\nasync function safeRedisOperation(operation, fallback) {\n    try {\n        const result = await operation();\n        if (result === null && fallback !== undefined) {\n            return fallback;\n        }\n        if (result === null) {\n            return {};\n        }\n        return result;\n    } catch (error) {\n        if (fallback !== undefined) {\n            console.error('Redis operation failed, using fallback:', error);\n            return fallback;\n        }\n        if (error.message?.includes('NOPERM')) {\n            console.error('Redis authentication failed:', error);\n            throw new Error('Database access denied. Please check your credentials.');\n        }\n        if (error.message?.includes('connect')) {\n            console.error('Redis connection error:', error);\n            throw new Error('Failed to connect to database. Please try again.');\n        }\n        if (error.message?.includes('JSON')) {\n            console.error('Redis JSON parsing error:', error);\n            throw new Error('Error retrieving user data. Invalid data format.');\n        }\n        console.error('Redis operation error:', error);\n        throw new Error('Error retrieving user data. Please try again.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/upstash.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_tucat_tucat_app_api_federation_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/federation/[id]/route.ts */ \"(rsc)/./app/api/federation/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/federation/[id]/route\",\n        pathname: \"/api/federation/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/federation/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\tucat\\\\tucat\\\\app\\\\api\\\\federation\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_tucat_tucat_app_api_federation_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();