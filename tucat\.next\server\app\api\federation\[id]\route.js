/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/federation/[id]/route";
exports.ids = ["app/api/federation/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/federation/[id]/route.ts":
/*!******************************************!*\
  !*** ./app/api/federation/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_upstash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/upstash */ \"(rsc)/./lib/upstash.ts\");\n\n\n// Function to calculate section score using the same logic as useAssessment.ts\nfunction calculateSectionScore(assessment, sectionNumber) {\n    if (!assessment) return 0;\n    let sectionScore = 0;\n    // Section III scoring\n    if (sectionNumber === 3) {\n        if (assessment.vision_mission_status === '4') sectionScore += 3;\n        else if (assessment.vision_mission_status === '3') sectionScore += 2;\n        else if (assessment.vision_mission_status === '2') sectionScore += 1;\n        // Yes/No/NA questions scoring\n        if (assessment.vision_posted === 'yes') sectionScore += 1;\n        if (assessment.vision_in_documents === 'yes') sectionScore += 1;\n        if (assessment.vision_for_planning === 'yes') sectionScore += 1;\n        if (assessment.decision_making === 'a') sectionScore += 3;\n        else if (assessment.decision_making === 'b') sectionScore += 2;\n        else if (assessment.decision_making === 'c') sectionScore += 1;\n        if (assessment.emerging_issues_handling === 'a') sectionScore += 3;\n        else if (assessment.emerging_issues_handling === 'b') sectionScore += 2;\n        else if (assessment.emerging_issues_handling === 'c') sectionScore += 1;\n        if (assessment.leadership_development_plan === 'a') sectionScore += 3;\n        else if (assessment.leadership_development_plan === 'b') sectionScore += 2;\n        else if (assessment.leadership_development_plan === 'c') sectionScore += 1;\n    } else if (sectionNumber === 4) {\n        if (assessment.organizational_structure === 'a') sectionScore += 2;\n        else if (assessment.organizational_structure === 'b') sectionScore += 1;\n        if (assessment.roles_defined === 'a') sectionScore += 2;\n        else if (assessment.roles_defined === 'b') sectionScore += 1;\n    } else if (sectionNumber === 5) {\n        if (assessment.management_approach === 'a') sectionScore += 2;\n        else if (assessment.management_approach === 'c') sectionScore += 1;\n        if (assessment.authority_delegation === 'a') sectionScore += 2;\n        else if (assessment.authority_delegation === 'c') sectionScore += 1;\n        if (assessment.decision_making_process === '4') sectionScore += 3;\n        else if (assessment.decision_making_process === '3') sectionScore += 2;\n        else if (assessment.decision_making_process === '2') sectionScore += 1;\n        if (assessment.deputy_availability === 'a') sectionScore += 2;\n        else if (assessment.deputy_availability === 'b') sectionScore += 1;\n        if (assessment.transition_plan === 'a') sectionScore += 2;\n        else if (assessment.transition_plan === 'b') sectionScore += 1;\n    } else if (sectionNumber === 6) {\n        if (assessment.workers_involvement_level === '4') sectionScore += 4;\n        else if (assessment.workers_involvement_level === '3') sectionScore += 3;\n        else if (assessment.workers_involvement_level === '2') sectionScore += 2;\n        else if (assessment.workers_involvement_level === '1') sectionScore += 1;\n        if (assessment.involve_program_activities === 'yes') sectionScore += 1;\n        if (assessment.involve_leaders_orientation === 'yes') sectionScore += 1;\n        if (assessment.solicit_feedback === 'yes') sectionScore += 1;\n        if (assessment.regular_interaction === 'yes') sectionScore += 1;\n        if (assessment.share_results === 'yes') sectionScore += 1;\n    } else if (sectionNumber === 7) {\n        if (assessment.cultural_gender_consideration === '4') sectionScore += 3;\n        else if (assessment.cultural_gender_consideration === '3') sectionScore += 2;\n        else if (assessment.cultural_gender_consideration === '2') sectionScore += 1;\n        if (assessment.consider_local_culture === 'yes') sectionScore += 1;\n        if (assessment.documented_guidelines === 'yes') sectionScore += 1;\n        if (assessment.provide_training === 'yes') sectionScore += 1;\n        if (assessment.use_assessment_findings === 'yes') sectionScore += 1;\n    } else if (sectionNumber === 8) {\n        if (assessment.representation_effectiveness === '4') sectionScore += 3;\n        else if (assessment.representation_effectiveness === '3') sectionScore += 2;\n        else if (assessment.representation_effectiveness === '2') sectionScore += 1;\n        if (assessment.member_involvement === '4') sectionScore += 3;\n        else if (assessment.member_involvement === '3') sectionScore += 2;\n        else if (assessment.member_involvement === '2') sectionScore += 1;\n        if (assessment.bargaining_strategy === 'a') sectionScore += 2;\n        else if (assessment.bargaining_strategy === 'b') sectionScore += 1;\n    } else if (sectionNumber === 9) {\n        if (assessment.communication_effectiveness === '4') sectionScore += 3;\n        else if (assessment.communication_effectiveness === '3') sectionScore += 2;\n        else if (assessment.communication_effectiveness === '2') sectionScore += 1;\n        if (assessment.member_engagement === '4') sectionScore += 3;\n        else if (assessment.member_engagement === '3') sectionScore += 2;\n        else if (assessment.member_engagement === '2') sectionScore += 1;\n        if (assessment.participation_opportunities === '4') sectionScore += 3;\n        else if (assessment.participation_opportunities === '3') sectionScore += 2;\n        else if (assessment.participation_opportunities === '2') sectionScore += 1;\n        if (assessment.feedback_collection === 'a') sectionScore += 2;\n        else if (assessment.feedback_collection === 'b') sectionScore += 1;\n    } else if (sectionNumber === 10) {\n        if (assessment.fee_collection === '4') sectionScore += 3;\n        else if (assessment.fee_collection === '3') sectionScore += 2;\n        else if (assessment.fee_collection === '2') sectionScore += 1;\n        if (assessment.financial_management === '4') sectionScore += 3;\n        else if (assessment.financial_management === '3') sectionScore += 2;\n        else if (assessment.financial_management === '2') sectionScore += 1;\n        if (assessment.financial_planning === '4') sectionScore += 3;\n        else if (assessment.financial_planning === '3') sectionScore += 2;\n        else if (assessment.financial_planning === '2') sectionScore += 1;\n        if (assessment.financial_system_quality === '4') sectionScore += 3;\n        else if (assessment.financial_system_quality === '3') sectionScore += 2;\n        else if (assessment.financial_system_quality === '2') sectionScore += 1;\n        if (assessment.has_cash_system === 'yes') sectionScore += 1;\n        if (assessment.uses_accounting_software === 'yes') sectionScore += 1;\n        if (assessment.has_chart_accounts === 'yes') sectionScore += 1;\n        if (assessment.reconciles_monthly === 'yes') sectionScore += 1;\n    } else if (sectionNumber === 11) {\n        if (assessment.audit_system_quality === '4') sectionScore += 3;\n        else if (assessment.audit_system_quality === '3') sectionScore += 2;\n        else if (assessment.audit_system_quality === '2') sectionScore += 1;\n        if (assessment.requires_annual_audit === 'yes') sectionScore += 1;\n        if (assessment.regularly_audited === 'yes') sectionScore += 1;\n        if (assessment.auditor_selection === 'yes') sectionScore += 1;\n        if (assessment.audit_manager === 'yes') sectionScore += 1;\n        if (assessment.implements_recommendations === 'yes') sectionScore += 1;\n        if (assessment.shares_reports === 'yes') sectionScore += 1;\n        if (assessment.report_provides_info === 'yes') sectionScore += 1;\n    }\n    return sectionScore;\n}\nasync function GET(request, { params }) {\n    try {\n        const { id: federationId } = params;\n        // Fetch federation basic data\n        const federationData = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(`federation:${federationId}`));\n        if (!federationData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Federation not found'\n            }, {\n                status: 404\n            });\n        }\n        // Parse federation data\n        let federation;\n        if (typeof federationData === 'string') {\n            try {\n                federation = JSON.parse(federationData);\n            } catch  {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid federation data'\n                }, {\n                    status: 500\n                });\n            }\n        } else {\n            federation = federationData;\n        }\n        // Fetch all assessments for this federation\n        const assessmentKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys(`assessment:${federationId}:*`));\n        const assessmentsRaw = await Promise.all(assessmentKeys.map((key)=>(0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key))));\n        // Parse assessments\n        const assessments = assessmentsRaw.map((a)=>{\n            if (!a) return null;\n            if (typeof a === 'string') {\n                try {\n                    return JSON.parse(a);\n                } catch  {\n                    return null;\n                }\n            }\n            return a;\n        }).filter((assessment)=>assessment !== null && typeof assessment === 'object' && 'section_number' in assessment);\n        // Section max scores (raw scores from useAssessment.ts)\n        const sectionMaxScores = {\n            2: 0,\n            3: 14,\n            4: 4,\n            5: 11,\n            6: 9,\n            7: 7,\n            8: 8,\n            9: 11,\n            10: 16,\n            11: 10\n        };\n        // Section titles\n        const sectionTitles = {\n            2: \"Legal Status and Compliance\",\n            3: \"Leadership\",\n            4: \"Organizational Structure\",\n            5: \"Management\",\n            6: \"Worker Participation\",\n            7: \"Culture and Gender\",\n            8: \"Collective Bargaining\",\n            9: \"Member Engagement\",\n            10: \"Financial Stability\",\n            11: \"Audit & Compliance\"\n        };\n        // Calculate section scores and build detailed response\n        const sectionScores = {};\n        const sectionDetails = {};\n        let totalScore = 0;\n        let maxPossible = 0;\n        // Process each section that has assessments\n        const allSectionNumbers = new Set([\n            ...Object.keys(sectionMaxScores).map(Number),\n            ...assessments.map((a)=>a.section_number)\n        ]);\n        for (const sectionNum of Array.from(allSectionNumbers).sort()){\n            const sectionAssessment = assessments.find((a)=>a.section_number === sectionNum);\n            // Calculate raw score using the same logic as useAssessment.ts\n            const rawScore = calculateSectionScore(sectionAssessment, sectionNum);\n            const maxSectionScore = sectionMaxScores[sectionNum] || 0;\n            sectionScores[sectionNum] = rawScore;\n            // Only include sections with max scores > 0 in total calculation\n            if (maxSectionScore > 0) {\n                totalScore += rawScore;\n                maxPossible += maxSectionScore;\n            }\n            // Build section details with questions and answers\n            const questions = extractQuestionsFromAssessment(sectionAssessment, sectionNum);\n            sectionDetails[sectionNum] = {\n                title: sectionTitles[sectionNum] || `Section ${sectionNum}`,\n                questions,\n                sectionScore: rawScore,\n                maxSectionScore,\n                percentage: maxSectionScore > 0 ? Math.round(rawScore / maxSectionScore * 100) : 0\n            };\n        }\n        const percentage = maxPossible > 0 ? Math.round(totalScore / maxPossible * 100) : 0;\n        const response = {\n            ...federation,\n            assessments,\n            totalScore,\n            maxScore: maxPossible,\n            percentage,\n            sectionScores,\n            sectionDetails\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('Error in GET /api/federation/[id]:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch federation details',\n            details: error?.message\n        }, {\n            status: 500\n        });\n    }\n}\n// Helper function to extract questions and answers from assessment data\nfunction extractQuestionsFromAssessment(assessment, sectionNumber) {\n    if (!assessment) return [];\n    const questions = [];\n    // Define questions and answer mappings for each section\n    const sectionData = {\n        2: {\n            'legal_registration_status': {\n                question: 'What is the legal registration status of the Federation?',\n                answerMap: {\n                    'a': 'Registered with proper documentation',\n                    'b': 'Registered but documentation incomplete',\n                    'c': 'Registration in progress',\n                    'd': 'Not registered'\n                }\n            },\n            'registration_authority': {\n                question: 'Which authority is the Federation registered with?'\n            }\n        },\n        3: {\n            'vision_mission_status': {\n                question: 'Does the Federation have a clear vision and mission?',\n                answerMap: {\n                    '1': 'No vision or mission statement',\n                    '2': 'Vision/mission exists but not well communicated',\n                    '3': 'Vision/mission exists and somewhat communicated',\n                    '4': 'Clear vision/mission, well communicated to all'\n                }\n            },\n            'vision_posted': {\n                question: 'Is the vision/mission posted in visible locations?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'vision_in_documents': {\n                question: 'Is the vision/mission included in official documents?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'vision_for_planning': {\n                question: 'Is the vision/mission used for strategic planning?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'decision_making': {\n                question: 'How are decisions made in the Federation?',\n                answerMap: {\n                    'a': 'Democratic process with member consultation',\n                    'b': 'Executive committee decisions',\n                    'c': 'Top-down leadership decisions',\n                    'd': 'Ad-hoc decision making'\n                }\n            },\n            'emerging_issues_handling': {\n                question: 'How does the Federation handle emerging issues?',\n                answerMap: {\n                    'a': 'Proactive approach with systematic response',\n                    'b': 'Reactive approach with some planning',\n                    'c': 'Minimal response to issues',\n                    'd': 'No systematic approach'\n                }\n            },\n            'leadership_development_plan': {\n                question: 'Does the Federation have a leadership development plan?',\n                answerMap: {\n                    'a': 'Comprehensive leadership development program',\n                    'b': 'Basic leadership training available',\n                    'c': 'Informal leadership development',\n                    'd': 'No leadership development plan'\n                }\n            }\n        },\n        4: {\n            'organizational_structure': {\n                question: 'What is the organizational structure of the Federation?',\n                answerMap: {\n                    'a': 'Well-defined hierarchical structure',\n                    'b': 'Basic organizational structure',\n                    'c': 'Informal structure',\n                    'd': 'No clear structure'\n                }\n            },\n            'roles_defined': {\n                question: 'Are roles and responsibilities clearly defined?',\n                answerMap: {\n                    'a': 'All roles clearly documented and communicated',\n                    'b': 'Most roles defined',\n                    'c': 'Some roles defined',\n                    'd': 'Roles not clearly defined'\n                }\n            }\n        },\n        5: {\n            'management_approach': {\n                question: 'How is the Federation managed?',\n                answerMap: {\n                    'a': 'Professional management with clear processes',\n                    'b': 'Structured management approach',\n                    'c': 'Basic management practices',\n                    'd': 'Informal management'\n                }\n            },\n            'authority_delegation': {\n                question: 'How is authority delegated in the Federation?',\n                answerMap: {\n                    'a': 'Clear delegation with proper oversight',\n                    'b': 'Some delegation with monitoring',\n                    'c': 'Limited delegation',\n                    'd': 'Centralized authority, no delegation'\n                }\n            },\n            'decision_making_process': {\n                question: 'What is the decision-making process?',\n                answerMap: {\n                    '1': 'No formal process',\n                    '2': 'Basic process exists',\n                    '3': 'Structured process with documentation',\n                    '4': 'Comprehensive process with stakeholder involvement'\n                }\n            },\n            'deputy_availability': {\n                question: 'Is there a deputy available for key positions?',\n                answerMap: {\n                    'a': 'All key positions have designated deputies',\n                    'b': 'Most positions have deputies',\n                    'c': 'Some positions have deputies',\n                    'd': 'No deputy system in place'\n                }\n            },\n            'transition_plan': {\n                question: 'Is there a succession/transition plan?',\n                answerMap: {\n                    'a': 'Comprehensive succession planning',\n                    'b': 'Basic transition planning',\n                    'c': 'Informal succession arrangements',\n                    'd': 'No succession planning'\n                }\n            }\n        },\n        6: {\n            'workers_involvement_level': {\n                question: 'What is the level of workers\\' involvement in the Federation?',\n                answerMap: {\n                    '1': 'Minimal involvement',\n                    '2': 'Basic involvement in some activities',\n                    '3': 'Good involvement in most activities',\n                    '4': 'High level of involvement in all activities'\n                }\n            },\n            'involve_program_activities': {\n                question: 'Are workers involved in program activities?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'involve_leaders_orientation': {\n                question: 'Are workers involved in leadership orientation?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'solicit_feedback': {\n                question: 'Does the Federation solicit feedback from workers?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'regular_interaction': {\n                question: 'Is there regular interaction with workers?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'share_results': {\n                question: 'Are results shared with workers?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            }\n        }\n    };\n    // Continue with remaining sections...\n    const remainingSections = {\n        7: {\n            'cultural_gender_consideration': {\n                question: 'How does the Federation consider cultural and gender aspects?',\n                answerMap: {\n                    '1': 'No consideration',\n                    '2': 'Basic awareness',\n                    '3': 'Good consideration with some policies',\n                    '4': 'Comprehensive cultural and gender policies'\n                }\n            },\n            'consider_local_culture': {\n                question: 'Does the Federation consider local culture?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'documented_guidelines': {\n                question: 'Are there documented cultural guidelines?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'provide_training': {\n                question: 'Does the Federation provide cultural sensitivity training?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'use_assessment_findings': {\n                question: 'Are assessment findings used for improvement?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            }\n        },\n        8: {\n            'representation_effectiveness': {\n                question: 'How effective is the Federation\\'s representation?',\n                answerMap: {\n                    '1': 'Poor representation',\n                    '2': 'Basic representation',\n                    '3': 'Good representation',\n                    '4': 'Excellent representation'\n                }\n            },\n            'member_involvement': {\n                question: 'What is the level of member involvement in collective bargaining?',\n                answerMap: {\n                    '1': 'No member involvement',\n                    '2': 'Limited involvement',\n                    '3': 'Good member involvement',\n                    '4': 'High member involvement'\n                }\n            },\n            'bargaining_strategy': {\n                question: 'What is the Federation\\'s bargaining strategy?',\n                answerMap: {\n                    'a': 'Comprehensive strategic approach',\n                    'b': 'Structured bargaining approach',\n                    'c': 'Basic bargaining tactics',\n                    'd': 'Ad-hoc bargaining'\n                }\n            }\n        },\n        9: {\n            'communication_effectiveness': {\n                question: 'How effective is the Federation\\'s communication?',\n                answerMap: {\n                    '1': 'Poor communication',\n                    '2': 'Basic communication',\n                    '3': 'Good communication',\n                    '4': 'Excellent communication'\n                }\n            },\n            'member_engagement': {\n                question: 'What is the level of member engagement?',\n                answerMap: {\n                    '1': 'Low engagement',\n                    '2': 'Basic engagement',\n                    '3': 'Good engagement',\n                    '4': 'High engagement'\n                }\n            },\n            'participation_opportunities': {\n                question: 'What participation opportunities are available?',\n                answerMap: {\n                    '1': 'Limited opportunities',\n                    '2': 'Basic opportunities',\n                    '3': 'Good opportunities',\n                    '4': 'Extensive opportunities'\n                }\n            },\n            'feedback_collection': {\n                question: 'How does the Federation collect member feedback?',\n                answerMap: {\n                    'a': 'Systematic feedback collection',\n                    'b': 'Regular feedback sessions',\n                    'c': 'Occasional feedback collection',\n                    'd': 'No formal feedback system'\n                }\n            }\n        },\n        10: {\n            'fee_collection': {\n                question: 'How does the Federation collect fees?',\n                answerMap: {\n                    '1': 'No systematic collection',\n                    '2': 'Basic collection system',\n                    '3': 'Good collection system',\n                    '4': 'Excellent systematic collection'\n                }\n            },\n            'financial_management': {\n                question: 'How is financial management handled?',\n                answerMap: {\n                    '1': 'Poor financial management',\n                    '2': 'Basic financial management',\n                    '3': 'Good financial management',\n                    '4': 'Excellent financial management'\n                }\n            },\n            'financial_planning': {\n                question: 'What is the approach to financial planning?',\n                answerMap: {\n                    '1': 'No financial planning',\n                    '2': 'Basic financial planning',\n                    '3': 'Good financial planning',\n                    '4': 'Comprehensive financial planning'\n                }\n            },\n            'financial_system_quality': {\n                question: 'What is the quality of the financial system?',\n                answerMap: {\n                    '1': 'Poor financial systems',\n                    '2': 'Basic financial systems',\n                    '3': 'Good financial systems',\n                    '4': 'Excellent financial systems'\n                }\n            },\n            'has_cash_system': {\n                question: 'Does the Federation have a cash management system?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'uses_accounting_software': {\n                question: 'Does the Federation use accounting software?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'has_chart_accounts': {\n                question: 'Does the Federation have a chart of accounts?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            },\n            'reconciles_monthly': {\n                question: 'Does the Federation reconcile accounts monthly?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No'\n                }\n            }\n        },\n        11: {\n            'audit_system_quality': {\n                question: 'What is the quality of the audit system?',\n                answerMap: {\n                    '1': 'No audit system',\n                    '2': 'Basic audit system',\n                    '3': 'Good audit system',\n                    '4': 'Excellent audit system'\n                }\n            },\n            'requires_annual_audit': {\n                question: 'Does the Federation require annual audits?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'regularly_audited': {\n                question: 'Is the Federation regularly audited?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'auditor_selection': {\n                question: 'How are auditors selected?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'audit_manager': {\n                question: 'Who manages the audit process?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'implements_recommendations': {\n                question: 'Does the Federation implement audit recommendations?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'shares_reports': {\n                question: 'Are audit reports shared with stakeholders?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            },\n            'report_provides_info': {\n                question: 'Do audit reports provide useful information?',\n                answerMap: {\n                    'yes': 'Yes',\n                    'no': 'No',\n                    'na': 'Not Applicable'\n                }\n            }\n        }\n    };\n    // Merge all sections\n    const allSections = {\n        ...sectionData,\n        ...remainingSections\n    };\n    const sectionQuestionMap = allSections[sectionNumber] || {};\n    // Extract questions and answers\n    Object.entries(sectionQuestionMap).forEach(([field, config])=>{\n        const rawAnswer = assessment[field];\n        if (rawAnswer !== undefined && rawAnswer !== null && rawAnswer !== '') {\n            let decodedAnswer = rawAnswer;\n            // Decode answer if mapping exists\n            if (config.answerMap && config.answerMap[rawAnswer]) {\n                decodedAnswer = config.answerMap[rawAnswer];\n            }\n            questions.push({\n                question: config.question,\n                answer: decodedAnswer\n            });\n        }\n    });\n    return questions;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2ZlZGVyYXRpb24vW2lkXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFDRTtBQUUxRCwrRUFBK0U7QUFDL0UsU0FBU0csc0JBQXNCQyxVQUFlLEVBQUVDLGFBQXFCO0lBQ25FLElBQUksQ0FBQ0QsWUFBWSxPQUFPO0lBRXhCLElBQUlFLGVBQWU7SUFFbkIsc0JBQXNCO0lBQ3RCLElBQUlELGtCQUFrQixHQUFHO1FBQ3ZCLElBQUlELFdBQVdHLHFCQUFxQixLQUFLLEtBQUtELGdCQUFnQjthQUN6RCxJQUFJRixXQUFXRyxxQkFBcUIsS0FBSyxLQUFLRCxnQkFBZ0I7YUFDOUQsSUFBSUYsV0FBV0cscUJBQXFCLEtBQUssS0FBS0QsZ0JBQWdCO1FBRW5FLDhCQUE4QjtRQUM5QixJQUFJRixXQUFXSSxhQUFhLEtBQUssT0FBT0YsZ0JBQWdCO1FBQ3hELElBQUlGLFdBQVdLLG1CQUFtQixLQUFLLE9BQU9ILGdCQUFnQjtRQUM5RCxJQUFJRixXQUFXTSxtQkFBbUIsS0FBSyxPQUFPSixnQkFBZ0I7UUFFOUQsSUFBSUYsV0FBV08sZUFBZSxLQUFLLEtBQUtMLGdCQUFnQjthQUNuRCxJQUFJRixXQUFXTyxlQUFlLEtBQUssS0FBS0wsZ0JBQWdCO2FBQ3hELElBQUlGLFdBQVdPLGVBQWUsS0FBSyxLQUFLTCxnQkFBZ0I7UUFFN0QsSUFBSUYsV0FBV1Esd0JBQXdCLEtBQUssS0FBS04sZ0JBQWdCO2FBQzVELElBQUlGLFdBQVdRLHdCQUF3QixLQUFLLEtBQUtOLGdCQUFnQjthQUNqRSxJQUFJRixXQUFXUSx3QkFBd0IsS0FBSyxLQUFLTixnQkFBZ0I7UUFFdEUsSUFBSUYsV0FBV1MsMkJBQTJCLEtBQUssS0FBS1AsZ0JBQWdCO2FBQy9ELElBQUlGLFdBQVdTLDJCQUEyQixLQUFLLEtBQUtQLGdCQUFnQjthQUNwRSxJQUFJRixXQUFXUywyQkFBMkIsS0FBSyxLQUFLUCxnQkFBZ0I7SUFDM0UsT0FHSyxJQUFJRCxrQkFBa0IsR0FBRztRQUM1QixJQUFJRCxXQUFXVSx3QkFBd0IsS0FBSyxLQUFLUixnQkFBZ0I7YUFDNUQsSUFBSUYsV0FBV1Usd0JBQXdCLEtBQUssS0FBS1IsZ0JBQWdCO1FBRXRFLElBQUlGLFdBQVdXLGFBQWEsS0FBSyxLQUFLVCxnQkFBZ0I7YUFDakQsSUFBSUYsV0FBV1csYUFBYSxLQUFLLEtBQUtULGdCQUFnQjtJQUM3RCxPQUdLLElBQUlELGtCQUFrQixHQUFHO1FBQzVCLElBQUlELFdBQVdZLG1CQUFtQixLQUFLLEtBQUtWLGdCQUFnQjthQUN2RCxJQUFJRixXQUFXWSxtQkFBbUIsS0FBSyxLQUFLVixnQkFBZ0I7UUFFakUsSUFBSUYsV0FBV2Esb0JBQW9CLEtBQUssS0FBS1gsZ0JBQWdCO2FBQ3hELElBQUlGLFdBQVdhLG9CQUFvQixLQUFLLEtBQUtYLGdCQUFnQjtRQUVsRSxJQUFJRixXQUFXYyx1QkFBdUIsS0FBSyxLQUFLWixnQkFBZ0I7YUFDM0QsSUFBSUYsV0FBV2MsdUJBQXVCLEtBQUssS0FBS1osZ0JBQWdCO2FBQ2hFLElBQUlGLFdBQVdjLHVCQUF1QixLQUFLLEtBQUtaLGdCQUFnQjtRQUVyRSxJQUFJRixXQUFXZSxtQkFBbUIsS0FBSyxLQUFLYixnQkFBZ0I7YUFDdkQsSUFBSUYsV0FBV2UsbUJBQW1CLEtBQUssS0FBS2IsZ0JBQWdCO1FBRWpFLElBQUlGLFdBQVdnQixlQUFlLEtBQUssS0FBS2QsZ0JBQWdCO2FBQ25ELElBQUlGLFdBQVdnQixlQUFlLEtBQUssS0FBS2QsZ0JBQWdCO0lBQy9ELE9BR0ssSUFBSUQsa0JBQWtCLEdBQUc7UUFDNUIsSUFBSUQsV0FBV2lCLHlCQUF5QixLQUFLLEtBQUtmLGdCQUFnQjthQUM3RCxJQUFJRixXQUFXaUIseUJBQXlCLEtBQUssS0FBS2YsZ0JBQWdCO2FBQ2xFLElBQUlGLFdBQVdpQix5QkFBeUIsS0FBSyxLQUFLZixnQkFBZ0I7YUFDbEUsSUFBSUYsV0FBV2lCLHlCQUF5QixLQUFLLEtBQUtmLGdCQUFnQjtRQUV2RSxJQUFJRixXQUFXa0IsMEJBQTBCLEtBQUssT0FBT2hCLGdCQUFnQjtRQUNyRSxJQUFJRixXQUFXbUIsMkJBQTJCLEtBQUssT0FBT2pCLGdCQUFnQjtRQUN0RSxJQUFJRixXQUFXb0IsZ0JBQWdCLEtBQUssT0FBT2xCLGdCQUFnQjtRQUMzRCxJQUFJRixXQUFXcUIsbUJBQW1CLEtBQUssT0FBT25CLGdCQUFnQjtRQUM5RCxJQUFJRixXQUFXc0IsYUFBYSxLQUFLLE9BQU9wQixnQkFBZ0I7SUFDMUQsT0FHSyxJQUFJRCxrQkFBa0IsR0FBRztRQUM1QixJQUFJRCxXQUFXdUIsNkJBQTZCLEtBQUssS0FBS3JCLGdCQUFnQjthQUNqRSxJQUFJRixXQUFXdUIsNkJBQTZCLEtBQUssS0FBS3JCLGdCQUFnQjthQUN0RSxJQUFJRixXQUFXdUIsNkJBQTZCLEtBQUssS0FBS3JCLGdCQUFnQjtRQUUzRSxJQUFJRixXQUFXd0Isc0JBQXNCLEtBQUssT0FBT3RCLGdCQUFnQjtRQUNqRSxJQUFJRixXQUFXeUIscUJBQXFCLEtBQUssT0FBT3ZCLGdCQUFnQjtRQUNoRSxJQUFJRixXQUFXMEIsZ0JBQWdCLEtBQUssT0FBT3hCLGdCQUFnQjtRQUMzRCxJQUFJRixXQUFXMkIsdUJBQXVCLEtBQUssT0FBT3pCLGdCQUFnQjtJQUNwRSxPQUdLLElBQUlELGtCQUFrQixHQUFHO1FBQzVCLElBQUlELFdBQVc0Qiw0QkFBNEIsS0FBSyxLQUFLMUIsZ0JBQWdCO2FBQ2hFLElBQUlGLFdBQVc0Qiw0QkFBNEIsS0FBSyxLQUFLMUIsZ0JBQWdCO2FBQ3JFLElBQUlGLFdBQVc0Qiw0QkFBNEIsS0FBSyxLQUFLMUIsZ0JBQWdCO1FBRTFFLElBQUlGLFdBQVc2QixrQkFBa0IsS0FBSyxLQUFLM0IsZ0JBQWdCO2FBQ3RELElBQUlGLFdBQVc2QixrQkFBa0IsS0FBSyxLQUFLM0IsZ0JBQWdCO2FBQzNELElBQUlGLFdBQVc2QixrQkFBa0IsS0FBSyxLQUFLM0IsZ0JBQWdCO1FBRWhFLElBQUlGLFdBQVc4QixtQkFBbUIsS0FBSyxLQUFLNUIsZ0JBQWdCO2FBQ3ZELElBQUlGLFdBQVc4QixtQkFBbUIsS0FBSyxLQUFLNUIsZ0JBQWdCO0lBQ25FLE9BR0ssSUFBSUQsa0JBQWtCLEdBQUc7UUFDNUIsSUFBSUQsV0FBVytCLDJCQUEyQixLQUFLLEtBQUs3QixnQkFBZ0I7YUFDL0QsSUFBSUYsV0FBVytCLDJCQUEyQixLQUFLLEtBQUs3QixnQkFBZ0I7YUFDcEUsSUFBSUYsV0FBVytCLDJCQUEyQixLQUFLLEtBQUs3QixnQkFBZ0I7UUFFekUsSUFBSUYsV0FBV2dDLGlCQUFpQixLQUFLLEtBQUs5QixnQkFBZ0I7YUFDckQsSUFBSUYsV0FBV2dDLGlCQUFpQixLQUFLLEtBQUs5QixnQkFBZ0I7YUFDMUQsSUFBSUYsV0FBV2dDLGlCQUFpQixLQUFLLEtBQUs5QixnQkFBZ0I7UUFFL0QsSUFBSUYsV0FBV2lDLDJCQUEyQixLQUFLLEtBQUsvQixnQkFBZ0I7YUFDL0QsSUFBSUYsV0FBV2lDLDJCQUEyQixLQUFLLEtBQUsvQixnQkFBZ0I7YUFDcEUsSUFBSUYsV0FBV2lDLDJCQUEyQixLQUFLLEtBQUsvQixnQkFBZ0I7UUFFekUsSUFBSUYsV0FBV2tDLG1CQUFtQixLQUFLLEtBQUtoQyxnQkFBZ0I7YUFDdkQsSUFBSUYsV0FBV2tDLG1CQUFtQixLQUFLLEtBQUtoQyxnQkFBZ0I7SUFDbkUsT0FHSyxJQUFJRCxrQkFBa0IsSUFBSTtRQUM3QixJQUFJRCxXQUFXbUMsY0FBYyxLQUFLLEtBQUtqQyxnQkFBZ0I7YUFDbEQsSUFBSUYsV0FBV21DLGNBQWMsS0FBSyxLQUFLakMsZ0JBQWdCO2FBQ3ZELElBQUlGLFdBQVdtQyxjQUFjLEtBQUssS0FBS2pDLGdCQUFnQjtRQUU1RCxJQUFJRixXQUFXb0Msb0JBQW9CLEtBQUssS0FBS2xDLGdCQUFnQjthQUN4RCxJQUFJRixXQUFXb0Msb0JBQW9CLEtBQUssS0FBS2xDLGdCQUFnQjthQUM3RCxJQUFJRixXQUFXb0Msb0JBQW9CLEtBQUssS0FBS2xDLGdCQUFnQjtRQUVsRSxJQUFJRixXQUFXcUMsa0JBQWtCLEtBQUssS0FBS25DLGdCQUFnQjthQUN0RCxJQUFJRixXQUFXcUMsa0JBQWtCLEtBQUssS0FBS25DLGdCQUFnQjthQUMzRCxJQUFJRixXQUFXcUMsa0JBQWtCLEtBQUssS0FBS25DLGdCQUFnQjtRQUVoRSxJQUFJRixXQUFXc0Msd0JBQXdCLEtBQUssS0FBS3BDLGdCQUFnQjthQUM1RCxJQUFJRixXQUFXc0Msd0JBQXdCLEtBQUssS0FBS3BDLGdCQUFnQjthQUNqRSxJQUFJRixXQUFXc0Msd0JBQXdCLEtBQUssS0FBS3BDLGdCQUFnQjtRQUV0RSxJQUFJRixXQUFXdUMsZUFBZSxLQUFLLE9BQU9yQyxnQkFBZ0I7UUFDMUQsSUFBSUYsV0FBV3dDLHdCQUF3QixLQUFLLE9BQU90QyxnQkFBZ0I7UUFDbkUsSUFBSUYsV0FBV3lDLGtCQUFrQixLQUFLLE9BQU92QyxnQkFBZ0I7UUFDN0QsSUFBSUYsV0FBVzBDLGtCQUFrQixLQUFLLE9BQU94QyxnQkFBZ0I7SUFDL0QsT0FHSyxJQUFJRCxrQkFBa0IsSUFBSTtRQUM3QixJQUFJRCxXQUFXMkMsb0JBQW9CLEtBQUssS0FBS3pDLGdCQUFnQjthQUN4RCxJQUFJRixXQUFXMkMsb0JBQW9CLEtBQUssS0FBS3pDLGdCQUFnQjthQUM3RCxJQUFJRixXQUFXMkMsb0JBQW9CLEtBQUssS0FBS3pDLGdCQUFnQjtRQUVsRSxJQUFJRixXQUFXNEMscUJBQXFCLEtBQUssT0FBTzFDLGdCQUFnQjtRQUNoRSxJQUFJRixXQUFXNkMsaUJBQWlCLEtBQUssT0FBTzNDLGdCQUFnQjtRQUM1RCxJQUFJRixXQUFXOEMsaUJBQWlCLEtBQUssT0FBTzVDLGdCQUFnQjtRQUM1RCxJQUFJRixXQUFXK0MsYUFBYSxLQUFLLE9BQU83QyxnQkFBZ0I7UUFDeEQsSUFBSUYsV0FBV2dELDBCQUEwQixLQUFLLE9BQU85QyxnQkFBZ0I7UUFDckUsSUFBSUYsV0FBV2lELGNBQWMsS0FBSyxPQUFPL0MsZ0JBQWdCO1FBQ3pELElBQUlGLFdBQVdrRCxvQkFBb0IsS0FBSyxPQUFPaEQsZ0JBQWdCO0lBQ2pFO0lBRUEsT0FBT0E7QUFDVDtBQUVPLGVBQWVpRCxJQUNwQkMsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUE4QjtJQUV0QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxJQUFJQyxZQUFZLEVBQUUsR0FBR0Y7UUFFN0IsOEJBQThCO1FBQzlCLE1BQU1HLGlCQUFpQixNQUFNMUQsZ0VBQWtCQSxDQUFDLElBQzlDRCwrQ0FBS0EsQ0FBQzRELEdBQUcsQ0FBQyxDQUFDLFdBQVcsRUFBRUYsY0FBYztRQUd4QyxJQUFJLENBQUNDLGdCQUFnQjtZQUNuQixPQUFPNUQscURBQVlBLENBQUM4RCxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXVCLEdBQ2hDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSx3QkFBd0I7UUFDeEIsSUFBSUM7UUFDSixJQUFJLE9BQU9MLG1CQUFtQixVQUFVO1lBQ3RDLElBQUk7Z0JBQ0ZLLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ1A7WUFDMUIsRUFBRSxPQUFNO2dCQUNOLE9BQU81RCxxREFBWUEsQ0FBQzhELElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQTBCLEdBQ25DO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1FBQ0YsT0FBTztZQUNMQyxhQUFhTDtRQUNmO1FBRUEsNENBQTRDO1FBQzVDLE1BQU1RLGlCQUFpQixNQUFNbEUsZ0VBQWtCQSxDQUFDLElBQzlDRCwrQ0FBS0EsQ0FBQ29FLElBQUksQ0FBQyxDQUFDLFdBQVcsRUFBRVYsYUFBYSxFQUFFLENBQUM7UUFHM0MsTUFBTVcsaUJBQWlCLE1BQU1DLFFBQVFDLEdBQUcsQ0FDdENKLGVBQWVLLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT3hFLGdFQUFrQkEsQ0FBQyxJQUFNRCwrQ0FBS0EsQ0FBQzRELEdBQUcsQ0FBQ2E7UUFHL0Qsb0JBQW9CO1FBQ3BCLE1BQU1DLGNBQWNMLGVBQ2pCRyxHQUFHLENBQUMsQ0FBQ0c7WUFDSixJQUFJLENBQUNBLEdBQUcsT0FBTztZQUNmLElBQUksT0FBT0EsTUFBTSxVQUFVO2dCQUN6QixJQUFJO29CQUNGLE9BQU9WLEtBQUtDLEtBQUssQ0FBQ1M7Z0JBQ3BCLEVBQUUsT0FBTTtvQkFDTixPQUFPO2dCQUNUO1lBQ0Y7WUFDQSxPQUFPQTtRQUNULEdBQ0NDLE1BQU0sQ0FBQyxDQUFDekUsYUFDUEEsZUFBZSxRQUNmLE9BQU9BLGVBQWUsWUFDdEIsb0JBQW9CQTtRQUd4Qix3REFBd0Q7UUFDeEQsTUFBTTBFLG1CQUFrRDtZQUN0RCxHQUFHO1lBQ0gsR0FBRztZQUNILEdBQUc7WUFDSCxHQUFHO1lBQ0gsR0FBRztZQUNILEdBQUc7WUFDSCxHQUFHO1lBQ0gsR0FBRztZQUNILElBQUk7WUFDSixJQUFJO1FBQ047UUFFQSxpQkFBaUI7UUFDakIsTUFBTUMsZ0JBQTJDO1lBQy9DLEdBQUc7WUFDSCxHQUFHO1lBQ0gsR0FBRztZQUNILEdBQUc7WUFDSCxHQUFHO1lBQ0gsR0FBRztZQUNILEdBQUc7WUFDSCxHQUFHO1lBQ0gsSUFBSTtZQUNKLElBQUk7UUFDTjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNQyxnQkFBK0MsQ0FBQztRQUN0RCxNQUFNQyxpQkFBbUQsQ0FBQztRQUMxRCxJQUFJQyxhQUFhO1FBQ2pCLElBQUlDLGNBQWM7UUFFbEIsNENBQTRDO1FBQzVDLE1BQU1DLG9CQUFvQixJQUFJQyxJQUFJO2VBQzdCQyxPQUFPakIsSUFBSSxDQUFDUyxrQkFBa0JMLEdBQUcsQ0FBQ2M7ZUFDbENaLFlBQVlGLEdBQUcsQ0FBQ0csQ0FBQUEsSUFBS0EsRUFBRVksY0FBYztTQUN6QztRQUVELEtBQUssTUFBTUMsY0FBY0MsTUFBTUMsSUFBSSxDQUFDUCxtQkFBbUJRLElBQUksR0FBSTtZQUM3RCxNQUFNQyxvQkFBb0JsQixZQUFZbUIsSUFBSSxDQUFDbEIsQ0FBQUEsSUFBS0EsRUFBRVksY0FBYyxLQUFLQztZQUVyRSwrREFBK0Q7WUFDL0QsTUFBTU0sV0FBVzVGLHNCQUFzQjBGLG1CQUFtQko7WUFDMUQsTUFBTU8sa0JBQWtCbEIsZ0JBQWdCLENBQUNXLFdBQVcsSUFBSTtZQUV4RFQsYUFBYSxDQUFDUyxXQUFXLEdBQUdNO1lBRTVCLGlFQUFpRTtZQUNqRSxJQUFJQyxrQkFBa0IsR0FBRztnQkFDdkJkLGNBQWNhO2dCQUNkWixlQUFlYTtZQUNqQjtZQUVBLG1EQUFtRDtZQUNuRCxNQUFNQyxZQUFZQywrQkFBK0JMLG1CQUFtQko7WUFFcEVSLGNBQWMsQ0FBQ1EsV0FBVyxHQUFHO2dCQUMzQlUsT0FBT3BCLGFBQWEsQ0FBQ1UsV0FBVyxJQUFJLENBQUMsUUFBUSxFQUFFQSxZQUFZO2dCQUMzRFE7Z0JBQ0EzRixjQUFjeUY7Z0JBQ2RDO2dCQUNBSSxZQUFZSixrQkFBa0IsSUFBSUssS0FBS0MsS0FBSyxDQUFDLFdBQVlOLGtCQUFtQixPQUFPO1lBQ3JGO1FBQ0Y7UUFFQSxNQUFNSSxhQUFhakIsY0FBYyxJQUFJa0IsS0FBS0MsS0FBSyxDQUFDLGFBQWNuQixjQUFlLE9BQU87UUFFcEYsTUFBTW9CLFdBQVc7WUFDZixHQUFHdEMsVUFBVTtZQUNiVTtZQUNBTztZQUNBc0IsVUFBVXJCO1lBQ1ZpQjtZQUNBcEI7WUFDQUM7UUFDRjtRQUVBLE9BQU9qRixxREFBWUEsQ0FBQzhELElBQUksQ0FBQ3lDO0lBRTNCLEVBQUUsT0FBT3hDLE9BQU87UUFDZDBDLFFBQVExQyxLQUFLLENBQUMsc0NBQXNDQTtRQUNwRCxPQUFPL0QscURBQVlBLENBQUM4RCxJQUFJLENBQ3RCO1lBQUVDLE9BQU87WUFBc0MyQyxTQUFTM0MsT0FBTzRDO1FBQVEsR0FDdkU7WUFBRTNDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsd0VBQXdFO0FBQ3hFLFNBQVNrQywrQkFBK0I5RixVQUFlLEVBQUVDLGFBQXFCO0lBQzVFLElBQUksQ0FBQ0QsWUFBWSxPQUFPLEVBQUU7SUFFMUIsTUFBTTZGLFlBQXVGLEVBQUU7SUFFL0Ysd0RBQXdEO0lBQ3hELE1BQU1XLGNBQW1IO1FBQ3ZILEdBQUc7WUFDRCw2QkFBNkI7Z0JBQzNCQyxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLDBCQUEwQjtnQkFDeEJELFVBQVU7WUFDWjtRQUNGO1FBQ0EsR0FBRztZQUNELHlCQUF5QjtnQkFDdkJBLFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0EsaUJBQWlCO2dCQUNmRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtvQkFBTSxNQUFNO2dCQUFpQjtZQUNoRTtZQUNBLHVCQUF1QjtnQkFDckJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQUUsT0FBTztvQkFBTyxNQUFNO29CQUFNLE1BQU07Z0JBQWlCO1lBQ2hFO1lBQ0EsdUJBQXVCO2dCQUNyQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07b0JBQU0sTUFBTTtnQkFBaUI7WUFDaEU7WUFDQSxtQkFBbUI7Z0JBQ2pCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLDRCQUE0QjtnQkFDMUJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0EsK0JBQStCO2dCQUM3QkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7UUFDRjtRQUNBLEdBQUc7WUFDRCw0QkFBNEI7Z0JBQzFCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLGlCQUFpQjtnQkFDZkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7UUFDRjtRQUNBLEdBQUc7WUFDRCx1QkFBdUI7Z0JBQ3JCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLHdCQUF3QjtnQkFDdEJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0EsMkJBQTJCO2dCQUN6QkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSx1QkFBdUI7Z0JBQ3JCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLG1CQUFtQjtnQkFDakJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1FBQ0Y7UUFDQSxHQUFHO1lBQ0QsNkJBQTZCO2dCQUMzQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSw4QkFBOEI7Z0JBQzVCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtnQkFBSztZQUN4QztZQUNBLCtCQUErQjtnQkFDN0JELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQUUsT0FBTztvQkFBTyxNQUFNO2dCQUFLO1lBQ3hDO1lBQ0Esb0JBQW9CO2dCQUNsQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07Z0JBQUs7WUFDeEM7WUFDQSx1QkFBdUI7Z0JBQ3JCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtnQkFBSztZQUN4QztZQUNBLGlCQUFpQjtnQkFDZkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07Z0JBQUs7WUFDeEM7UUFDRjtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1DLG9CQUFvQjtRQUN4QixHQUFHO1lBQ0QsaUNBQWlDO2dCQUMvQkYsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSwwQkFBMEI7Z0JBQ3hCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtnQkFBSztZQUN4QztZQUNBLHlCQUF5QjtnQkFDdkJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQUUsT0FBTztvQkFBTyxNQUFNO2dCQUFLO1lBQ3hDO1lBQ0Esb0JBQW9CO2dCQUNsQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07Z0JBQUs7WUFDeEM7WUFDQSwyQkFBMkI7Z0JBQ3pCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtnQkFBSztZQUN4QztRQUNGO1FBQ0EsR0FBRztZQUNELGdDQUFnQztnQkFDOUJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0Esc0JBQXNCO2dCQUNwQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSx1QkFBdUI7Z0JBQ3JCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtRQUNGO1FBQ0EsR0FBRztZQUNELCtCQUErQjtnQkFDN0JELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0EscUJBQXFCO2dCQUNuQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSwrQkFBK0I7Z0JBQzdCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLHVCQUF1QjtnQkFDckJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1FBQ0Y7UUFDQSxJQUFJO1lBQ0Ysa0JBQWtCO2dCQUNoQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSx3QkFBd0I7Z0JBQ3RCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUNULEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7Z0JBQ1A7WUFDRjtZQUNBLHNCQUFzQjtnQkFDcEJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0EsNEJBQTRCO2dCQUMxQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFDVCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztvQkFDTCxLQUFLO2dCQUNQO1lBQ0Y7WUFDQSxtQkFBbUI7Z0JBQ2pCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtnQkFBSztZQUN4QztZQUNBLDRCQUE0QjtnQkFDMUJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQUUsT0FBTztvQkFBTyxNQUFNO2dCQUFLO1lBQ3hDO1lBQ0Esc0JBQXNCO2dCQUNwQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07Z0JBQUs7WUFDeEM7WUFDQSxzQkFBc0I7Z0JBQ3BCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtnQkFBSztZQUN4QztRQUNGO1FBQ0EsSUFBSTtZQUNGLHdCQUF3QjtnQkFDdEJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1QsS0FBSztvQkFDTCxLQUFLO29CQUNMLEtBQUs7b0JBQ0wsS0FBSztnQkFDUDtZQUNGO1lBQ0EseUJBQXlCO2dCQUN2QkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07b0JBQU0sTUFBTTtnQkFBaUI7WUFDaEU7WUFDQSxxQkFBcUI7Z0JBQ25CRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtvQkFBTSxNQUFNO2dCQUFpQjtZQUNoRTtZQUNBLHFCQUFxQjtnQkFDbkJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQUUsT0FBTztvQkFBTyxNQUFNO29CQUFNLE1BQU07Z0JBQWlCO1lBQ2hFO1lBQ0EsaUJBQWlCO2dCQUNmRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtvQkFBTSxNQUFNO2dCQUFpQjtZQUNoRTtZQUNBLDhCQUE4QjtnQkFDNUJELFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQUUsT0FBTztvQkFBTyxNQUFNO29CQUFNLE1BQU07Z0JBQWlCO1lBQ2hFO1lBQ0Esa0JBQWtCO2dCQUNoQkQsVUFBVTtnQkFDVkMsV0FBVztvQkFBRSxPQUFPO29CQUFPLE1BQU07b0JBQU0sTUFBTTtnQkFBaUI7WUFDaEU7WUFDQSx3QkFBd0I7Z0JBQ3RCRCxVQUFVO2dCQUNWQyxXQUFXO29CQUFFLE9BQU87b0JBQU8sTUFBTTtvQkFBTSxNQUFNO2dCQUFpQjtZQUNoRTtRQUNGO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckIsTUFBTUUsY0FBYztRQUFFLEdBQUdKLFdBQVc7UUFBRSxHQUFHRyxpQkFBaUI7SUFBQztJQUMzRCxNQUFNRSxxQkFBcUJELFdBQVcsQ0FBQzNHLGNBQWMsSUFBSSxDQUFDO0lBRTFELGdDQUFnQztJQUNoQ2lGLE9BQU80QixPQUFPLENBQUNELG9CQUFvQkUsT0FBTyxDQUFDLENBQUMsQ0FBQ0MsT0FBT0MsT0FBTztRQUN6RCxNQUFNQyxZQUFZbEgsVUFBVSxDQUFDZ0gsTUFBTTtRQUNuQyxJQUFJRSxjQUFjQyxhQUFhRCxjQUFjLFFBQVFBLGNBQWMsSUFBSTtZQUNyRSxJQUFJRSxnQkFBZ0JGO1lBRXBCLGtDQUFrQztZQUNsQyxJQUFJRCxPQUFPUCxTQUFTLElBQUlPLE9BQU9QLFNBQVMsQ0FBQ1EsVUFBVSxFQUFFO2dCQUNuREUsZ0JBQWdCSCxPQUFPUCxTQUFTLENBQUNRLFVBQVU7WUFDN0M7WUFFQXJCLFVBQVV3QixJQUFJLENBQUM7Z0JBQ2JaLFVBQVVRLE9BQU9SLFFBQVE7Z0JBQ3pCYSxRQUFRRjtZQUdWO1FBQ0Y7SUFDRjtJQUVBLE9BQU92QjtBQUNUIiwic291cmNlcyI6WyJEOlxcdHVjYXRcXHR1Y2F0XFxhcHBcXGFwaVxcZmVkZXJhdGlvblxcW2lkXVxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IHJlZGlzLCBzYWZlUmVkaXNPcGVyYXRpb24gfSBmcm9tICdAL2xpYi91cHN0YXNoJztcblxuLy8gRnVuY3Rpb24gdG8gY2FsY3VsYXRlIHNlY3Rpb24gc2NvcmUgdXNpbmcgdGhlIHNhbWUgbG9naWMgYXMgdXNlQXNzZXNzbWVudC50c1xuZnVuY3Rpb24gY2FsY3VsYXRlU2VjdGlvblNjb3JlKGFzc2Vzc21lbnQ6IGFueSwgc2VjdGlvbk51bWJlcjogbnVtYmVyKTogbnVtYmVyIHtcbiAgaWYgKCFhc3Nlc3NtZW50KSByZXR1cm4gMDtcblxuICBsZXQgc2VjdGlvblNjb3JlID0gMDtcblxuICAvLyBTZWN0aW9uIElJSSBzY29yaW5nXG4gIGlmIChzZWN0aW9uTnVtYmVyID09PSAzKSB7XG4gICAgaWYgKGFzc2Vzc21lbnQudmlzaW9uX21pc3Npb25fc3RhdHVzID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQudmlzaW9uX21pc3Npb25fc3RhdHVzID09PSAnMycpIHNlY3Rpb25TY29yZSArPSAyO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQudmlzaW9uX21pc3Npb25fc3RhdHVzID09PSAnMicpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgLy8gWWVzL05vL05BIHF1ZXN0aW9ucyBzY29yaW5nXG4gICAgaWYgKGFzc2Vzc21lbnQudmlzaW9uX3Bvc3RlZCA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50LnZpc2lvbl9pbl9kb2N1bWVudHMgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC52aXNpb25fZm9yX3BsYW5uaW5nID09PSAneWVzJykgc2VjdGlvblNjb3JlICs9IDE7XG5cbiAgICBpZiAoYXNzZXNzbWVudC5kZWNpc2lvbl9tYWtpbmcgPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDM7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5kZWNpc2lvbl9tYWtpbmcgPT09ICdiJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5kZWNpc2lvbl9tYWtpbmcgPT09ICdjJykgc2VjdGlvblNjb3JlICs9IDE7XG5cbiAgICBpZiAoYXNzZXNzbWVudC5lbWVyZ2luZ19pc3N1ZXNfaGFuZGxpbmcgPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDM7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5lbWVyZ2luZ19pc3N1ZXNfaGFuZGxpbmcgPT09ICdiJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5lbWVyZ2luZ19pc3N1ZXNfaGFuZGxpbmcgPT09ICdjJykgc2VjdGlvblNjb3JlICs9IDE7XG5cbiAgICBpZiAoYXNzZXNzbWVudC5sZWFkZXJzaGlwX2RldmVsb3BtZW50X3BsYW4gPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDM7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5sZWFkZXJzaGlwX2RldmVsb3BtZW50X3BsYW4gPT09ICdiJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5sZWFkZXJzaGlwX2RldmVsb3BtZW50X3BsYW4gPT09ICdjJykgc2VjdGlvblNjb3JlICs9IDE7XG4gIH1cblxuICAvLyBTZWN0aW9uIElWIHNjb3JpbmdcbiAgZWxzZSBpZiAoc2VjdGlvbk51bWJlciA9PT0gNCkge1xuICAgIGlmIChhc3Nlc3NtZW50Lm9yZ2FuaXphdGlvbmFsX3N0cnVjdHVyZSA9PT0gJ2EnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50Lm9yZ2FuaXphdGlvbmFsX3N0cnVjdHVyZSA9PT0gJ2InKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LnJvbGVzX2RlZmluZWQgPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5yb2xlc19kZWZpbmVkID09PSAnYicpIHNlY3Rpb25TY29yZSArPSAxO1xuICB9XG5cbiAgLy8gU2VjdGlvbiBWIHNjb3JpbmdcbiAgZWxzZSBpZiAoc2VjdGlvbk51bWJlciA9PT0gNSkge1xuICAgIGlmIChhc3Nlc3NtZW50Lm1hbmFnZW1lbnRfYXBwcm9hY2ggPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5tYW5hZ2VtZW50X2FwcHJvYWNoID09PSAnYycpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQuYXV0aG9yaXR5X2RlbGVnYXRpb24gPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5hdXRob3JpdHlfZGVsZWdhdGlvbiA9PT0gJ2MnKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LmRlY2lzaW9uX21ha2luZ19wcm9jZXNzID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuZGVjaXNpb25fbWFraW5nX3Byb2Nlc3MgPT09ICczJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5kZWNpc2lvbl9tYWtpbmdfcHJvY2VzcyA9PT0gJzInKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LmRlcHV0eV9hdmFpbGFiaWxpdHkgPT09ICdhJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5kZXB1dHlfYXZhaWxhYmlsaXR5ID09PSAnYicpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQudHJhbnNpdGlvbl9wbGFuID09PSAnYScpIHNlY3Rpb25TY29yZSArPSAyO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQudHJhbnNpdGlvbl9wbGFuID09PSAnYicpIHNlY3Rpb25TY29yZSArPSAxO1xuICB9XG5cbiAgLy8gU2VjdGlvbiBWSSBzY29yaW5nXG4gIGVsc2UgaWYgKHNlY3Rpb25OdW1iZXIgPT09IDYpIHtcbiAgICBpZiAoYXNzZXNzbWVudC53b3JrZXJzX2ludm9sdmVtZW50X2xldmVsID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSA0O1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQud29ya2Vyc19pbnZvbHZlbWVudF9sZXZlbCA9PT0gJzMnKSBzZWN0aW9uU2NvcmUgKz0gMztcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LndvcmtlcnNfaW52b2x2ZW1lbnRfbGV2ZWwgPT09ICcyJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC53b3JrZXJzX2ludm9sdmVtZW50X2xldmVsID09PSAnMScpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQuaW52b2x2ZV9wcm9ncmFtX2FjdGl2aXRpZXMgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC5pbnZvbHZlX2xlYWRlcnNfb3JpZW50YXRpb24gPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC5zb2xpY2l0X2ZlZWRiYWNrID09PSAneWVzJykgc2VjdGlvblNjb3JlICs9IDE7XG4gICAgaWYgKGFzc2Vzc21lbnQucmVndWxhcl9pbnRlcmFjdGlvbiA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50LnNoYXJlX3Jlc3VsdHMgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgfVxuXG4gIC8vIFNlY3Rpb24gVklJIHNjb3JpbmdcbiAgZWxzZSBpZiAoc2VjdGlvbk51bWJlciA9PT0gNykge1xuICAgIGlmIChhc3Nlc3NtZW50LmN1bHR1cmFsX2dlbmRlcl9jb25zaWRlcmF0aW9uID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuY3VsdHVyYWxfZ2VuZGVyX2NvbnNpZGVyYXRpb24gPT09ICczJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5jdWx0dXJhbF9nZW5kZXJfY29uc2lkZXJhdGlvbiA9PT0gJzInKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LmNvbnNpZGVyX2xvY2FsX2N1bHR1cmUgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC5kb2N1bWVudGVkX2d1aWRlbGluZXMgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC5wcm92aWRlX3RyYWluaW5nID09PSAneWVzJykgc2VjdGlvblNjb3JlICs9IDE7XG4gICAgaWYgKGFzc2Vzc21lbnQudXNlX2Fzc2Vzc21lbnRfZmluZGluZ3MgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgfVxuXG4gIC8vIFNlY3Rpb24gVklJSSBzY29yaW5nXG4gIGVsc2UgaWYgKHNlY3Rpb25OdW1iZXIgPT09IDgpIHtcbiAgICBpZiAoYXNzZXNzbWVudC5yZXByZXNlbnRhdGlvbl9lZmZlY3RpdmVuZXNzID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQucmVwcmVzZW50YXRpb25fZWZmZWN0aXZlbmVzcyA9PT0gJzMnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LnJlcHJlc2VudGF0aW9uX2VmZmVjdGl2ZW5lc3MgPT09ICcyJykgc2VjdGlvblNjb3JlICs9IDE7XG5cbiAgICBpZiAoYXNzZXNzbWVudC5tZW1iZXJfaW52b2x2ZW1lbnQgPT09ICc0Jykgc2VjdGlvblNjb3JlICs9IDM7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5tZW1iZXJfaW52b2x2ZW1lbnQgPT09ICczJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5tZW1iZXJfaW52b2x2ZW1lbnQgPT09ICcyJykgc2VjdGlvblNjb3JlICs9IDE7XG5cbiAgICBpZiAoYXNzZXNzbWVudC5iYXJnYWluaW5nX3N0cmF0ZWd5ID09PSAnYScpIHNlY3Rpb25TY29yZSArPSAyO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuYmFyZ2FpbmluZ19zdHJhdGVneSA9PT0gJ2InKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgfVxuXG4gIC8vIFNlY3Rpb24gSVggc2NvcmluZ1xuICBlbHNlIGlmIChzZWN0aW9uTnVtYmVyID09PSA5KSB7XG4gICAgaWYgKGFzc2Vzc21lbnQuY29tbXVuaWNhdGlvbl9lZmZlY3RpdmVuZXNzID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuY29tbXVuaWNhdGlvbl9lZmZlY3RpdmVuZXNzID09PSAnMycpIHNlY3Rpb25TY29yZSArPSAyO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuY29tbXVuaWNhdGlvbl9lZmZlY3RpdmVuZXNzID09PSAnMicpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQubWVtYmVyX2VuZ2FnZW1lbnQgPT09ICc0Jykgc2VjdGlvblNjb3JlICs9IDM7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5tZW1iZXJfZW5nYWdlbWVudCA9PT0gJzMnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50Lm1lbWJlcl9lbmdhZ2VtZW50ID09PSAnMicpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQucGFydGljaXBhdGlvbl9vcHBvcnR1bml0aWVzID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQucGFydGljaXBhdGlvbl9vcHBvcnR1bml0aWVzID09PSAnMycpIHNlY3Rpb25TY29yZSArPSAyO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQucGFydGljaXBhdGlvbl9vcHBvcnR1bml0aWVzID09PSAnMicpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQuZmVlZGJhY2tfY29sbGVjdGlvbiA9PT0gJ2EnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LmZlZWRiYWNrX2NvbGxlY3Rpb24gPT09ICdiJykgc2VjdGlvblNjb3JlICs9IDE7XG4gIH1cblxuICAvLyBTZWN0aW9uIFggc2NvcmluZ1xuICBlbHNlIGlmIChzZWN0aW9uTnVtYmVyID09PSAxMCkge1xuICAgIGlmIChhc3Nlc3NtZW50LmZlZV9jb2xsZWN0aW9uID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuZmVlX2NvbGxlY3Rpb24gPT09ICczJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5mZWVfY29sbGVjdGlvbiA9PT0gJzInKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9tYW5hZ2VtZW50ID09PSAnNCcpIHNlY3Rpb25TY29yZSArPSAzO1xuICAgIGVsc2UgaWYgKGFzc2Vzc21lbnQuZmluYW5jaWFsX21hbmFnZW1lbnQgPT09ICczJykgc2VjdGlvblNjb3JlICs9IDI7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5maW5hbmNpYWxfbWFuYWdlbWVudCA9PT0gJzInKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9wbGFubmluZyA9PT0gJzQnKSBzZWN0aW9uU2NvcmUgKz0gMztcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9wbGFubmluZyA9PT0gJzMnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9wbGFubmluZyA9PT0gJzInKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9zeXN0ZW1fcXVhbGl0eSA9PT0gJzQnKSBzZWN0aW9uU2NvcmUgKz0gMztcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9zeXN0ZW1fcXVhbGl0eSA9PT0gJzMnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LmZpbmFuY2lhbF9zeXN0ZW1fcXVhbGl0eSA9PT0gJzInKSBzZWN0aW9uU2NvcmUgKz0gMTtcblxuICAgIGlmIChhc3Nlc3NtZW50Lmhhc19jYXNoX3N5c3RlbSA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50LnVzZXNfYWNjb3VudGluZ19zb2Z0d2FyZSA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50Lmhhc19jaGFydF9hY2NvdW50cyA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50LnJlY29uY2lsZXNfbW9udGhseSA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICB9XG5cbiAgLy8gU2VjdGlvbiBYSSBzY29yaW5nXG4gIGVsc2UgaWYgKHNlY3Rpb25OdW1iZXIgPT09IDExKSB7XG4gICAgaWYgKGFzc2Vzc21lbnQuYXVkaXRfc3lzdGVtX3F1YWxpdHkgPT09ICc0Jykgc2VjdGlvblNjb3JlICs9IDM7XG4gICAgZWxzZSBpZiAoYXNzZXNzbWVudC5hdWRpdF9zeXN0ZW1fcXVhbGl0eSA9PT0gJzMnKSBzZWN0aW9uU2NvcmUgKz0gMjtcbiAgICBlbHNlIGlmIChhc3Nlc3NtZW50LmF1ZGl0X3N5c3RlbV9xdWFsaXR5ID09PSAnMicpIHNlY3Rpb25TY29yZSArPSAxO1xuXG4gICAgaWYgKGFzc2Vzc21lbnQucmVxdWlyZXNfYW5udWFsX2F1ZGl0ID09PSAneWVzJykgc2VjdGlvblNjb3JlICs9IDE7XG4gICAgaWYgKGFzc2Vzc21lbnQucmVndWxhcmx5X2F1ZGl0ZWQgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC5hdWRpdG9yX3NlbGVjdGlvbiA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50LmF1ZGl0X21hbmFnZXIgPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgICBpZiAoYXNzZXNzbWVudC5pbXBsZW1lbnRzX3JlY29tbWVuZGF0aW9ucyA9PT0gJ3llcycpIHNlY3Rpb25TY29yZSArPSAxO1xuICAgIGlmIChhc3Nlc3NtZW50LnNoYXJlc19yZXBvcnRzID09PSAneWVzJykgc2VjdGlvblNjb3JlICs9IDE7XG4gICAgaWYgKGFzc2Vzc21lbnQucmVwb3J0X3Byb3ZpZGVzX2luZm8gPT09ICd5ZXMnKSBzZWN0aW9uU2NvcmUgKz0gMTtcbiAgfVxuXG4gIHJldHVybiBzZWN0aW9uU2NvcmU7XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogeyBpZDogc3RyaW5nIH0gfVxuKSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBpZDogZmVkZXJhdGlvbklkIH0gPSBwYXJhbXM7XG5cbiAgICAvLyBGZXRjaCBmZWRlcmF0aW9uIGJhc2ljIGRhdGFcbiAgICBjb25zdCBmZWRlcmF0aW9uRGF0YSA9IGF3YWl0IHNhZmVSZWRpc09wZXJhdGlvbigoKSA9PiBcbiAgICAgIHJlZGlzLmdldChgZmVkZXJhdGlvbjoke2ZlZGVyYXRpb25JZH1gKVxuICAgICk7XG5cbiAgICBpZiAoIWZlZGVyYXRpb25EYXRhKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdGZWRlcmF0aW9uIG5vdCBmb3VuZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwNCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFBhcnNlIGZlZGVyYXRpb24gZGF0YVxuICAgIGxldCBmZWRlcmF0aW9uO1xuICAgIGlmICh0eXBlb2YgZmVkZXJhdGlvbkRhdGEgPT09ICdzdHJpbmcnKSB7XG4gICAgICB0cnkge1xuICAgICAgICBmZWRlcmF0aW9uID0gSlNPTi5wYXJzZShmZWRlcmF0aW9uRGF0YSk7XG4gICAgICB9IGNhdGNoIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdJbnZhbGlkIGZlZGVyYXRpb24gZGF0YScgfSxcbiAgICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgZmVkZXJhdGlvbiA9IGZlZGVyYXRpb25EYXRhO1xuICAgIH1cblxuICAgIC8vIEZldGNoIGFsbCBhc3Nlc3NtZW50cyBmb3IgdGhpcyBmZWRlcmF0aW9uXG4gICAgY29uc3QgYXNzZXNzbWVudEtleXMgPSBhd2FpdCBzYWZlUmVkaXNPcGVyYXRpb24oKCkgPT4gXG4gICAgICByZWRpcy5rZXlzKGBhc3Nlc3NtZW50OiR7ZmVkZXJhdGlvbklkfToqYClcbiAgICApO1xuXG4gICAgY29uc3QgYXNzZXNzbWVudHNSYXcgPSBhd2FpdCBQcm9taXNlLmFsbChcbiAgICAgIGFzc2Vzc21lbnRLZXlzLm1hcChrZXkgPT4gc2FmZVJlZGlzT3BlcmF0aW9uKCgpID0+IHJlZGlzLmdldChrZXkpKSlcbiAgICApO1xuXG4gICAgLy8gUGFyc2UgYXNzZXNzbWVudHNcbiAgICBjb25zdCBhc3Nlc3NtZW50cyA9IGFzc2Vzc21lbnRzUmF3XG4gICAgICAubWFwKChhKSA9PiB7XG4gICAgICAgIGlmICghYSkgcmV0dXJuIG51bGw7XG4gICAgICAgIGlmICh0eXBlb2YgYSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIEpTT04ucGFyc2UoYSk7XG4gICAgICAgICAgfSBjYXRjaCB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGE7XG4gICAgICB9KVxuICAgICAgLmZpbHRlcigoYXNzZXNzbWVudCk6IGFzc2Vzc21lbnQgaXMgYW55ID0+XG4gICAgICAgIGFzc2Vzc21lbnQgIT09IG51bGwgJiZcbiAgICAgICAgdHlwZW9mIGFzc2Vzc21lbnQgPT09ICdvYmplY3QnICYmXG4gICAgICAgICdzZWN0aW9uX251bWJlcicgaW4gYXNzZXNzbWVudFxuICAgICAgKTtcblxuICAgIC8vIFNlY3Rpb24gbWF4IHNjb3JlcyAocmF3IHNjb3JlcyBmcm9tIHVzZUFzc2Vzc21lbnQudHMpXG4gICAgY29uc3Qgc2VjdGlvbk1heFNjb3JlczogeyBbc2VjdGlvbjogbnVtYmVyXTogbnVtYmVyIH0gPSB7XG4gICAgICAyOiAwLCAvLyBMZWdhbCBTdGF0dXMgYW5kIENvbXBsaWFuY2UgKG5vIHNjb3JpbmcgaW4gdXNlQXNzZXNzbWVudClcbiAgICAgIDM6IDE0LCAvLyBMZWFkZXJzaGlwXG4gICAgICA0OiA0LCAvLyBPcmdhbml6YXRpb25hbCBTdHJ1Y3R1cmVcbiAgICAgIDU6IDExLCAvLyBNYW5hZ2VtZW50XG4gICAgICA2OiA5LCAvLyBXb3JrZXIgUGFydGljaXBhdGlvblxuICAgICAgNzogNywgLy8gQ3VsdHVyZSBhbmQgR2VuZGVyXG4gICAgICA4OiA4LCAvLyBDb2xsZWN0aXZlIEJhcmdhaW5pbmdcbiAgICAgIDk6IDExLCAvLyBNZW1iZXIgRW5nYWdlbWVudFxuICAgICAgMTA6IDE2LCAvLyBGaW5hbmNpYWwgU3RhYmlsaXR5XG4gICAgICAxMTogMTAsIC8vIEF1ZGl0ICYgQ29tcGxpYW5jZVxuICAgIH07XG5cbiAgICAvLyBTZWN0aW9uIHRpdGxlc1xuICAgIGNvbnN0IHNlY3Rpb25UaXRsZXM6IHsgW2tleTogbnVtYmVyXTogc3RyaW5nIH0gPSB7XG4gICAgICAyOiBcIkxlZ2FsIFN0YXR1cyBhbmQgQ29tcGxpYW5jZVwiLFxuICAgICAgMzogXCJMZWFkZXJzaGlwXCIsXG4gICAgICA0OiBcIk9yZ2FuaXphdGlvbmFsIFN0cnVjdHVyZVwiLFxuICAgICAgNTogXCJNYW5hZ2VtZW50XCIsXG4gICAgICA2OiBcIldvcmtlciBQYXJ0aWNpcGF0aW9uXCIsXG4gICAgICA3OiBcIkN1bHR1cmUgYW5kIEdlbmRlclwiLFxuICAgICAgODogXCJDb2xsZWN0aXZlIEJhcmdhaW5pbmdcIixcbiAgICAgIDk6IFwiTWVtYmVyIEVuZ2FnZW1lbnRcIixcbiAgICAgIDEwOiBcIkZpbmFuY2lhbCBTdGFiaWxpdHlcIixcbiAgICAgIDExOiBcIkF1ZGl0ICYgQ29tcGxpYW5jZVwiXG4gICAgfTtcblxuICAgIC8vIENhbGN1bGF0ZSBzZWN0aW9uIHNjb3JlcyBhbmQgYnVpbGQgZGV0YWlsZWQgcmVzcG9uc2VcbiAgICBjb25zdCBzZWN0aW9uU2NvcmVzOiB7IFtzZWN0aW9uOiBudW1iZXJdOiBudW1iZXIgfSA9IHt9O1xuICAgIGNvbnN0IHNlY3Rpb25EZXRhaWxzOiB7IFtzZWN0aW9uTnVtYmVyOiBudW1iZXJdOiBhbnkgfSA9IHt9O1xuICAgIGxldCB0b3RhbFNjb3JlID0gMDtcbiAgICBsZXQgbWF4UG9zc2libGUgPSAwO1xuXG4gICAgLy8gUHJvY2VzcyBlYWNoIHNlY3Rpb24gdGhhdCBoYXMgYXNzZXNzbWVudHNcbiAgICBjb25zdCBhbGxTZWN0aW9uTnVtYmVycyA9IG5ldyBTZXQoW1xuICAgICAgLi4uT2JqZWN0LmtleXMoc2VjdGlvbk1heFNjb3JlcykubWFwKE51bWJlciksXG4gICAgICAuLi5hc3Nlc3NtZW50cy5tYXAoYSA9PiBhLnNlY3Rpb25fbnVtYmVyKVxuICAgIF0pO1xuXG4gICAgZm9yIChjb25zdCBzZWN0aW9uTnVtIG9mIEFycmF5LmZyb20oYWxsU2VjdGlvbk51bWJlcnMpLnNvcnQoKSkge1xuICAgICAgY29uc3Qgc2VjdGlvbkFzc2Vzc21lbnQgPSBhc3Nlc3NtZW50cy5maW5kKGEgPT4gYS5zZWN0aW9uX251bWJlciA9PT0gc2VjdGlvbk51bSk7XG5cbiAgICAgIC8vIENhbGN1bGF0ZSByYXcgc2NvcmUgdXNpbmcgdGhlIHNhbWUgbG9naWMgYXMgdXNlQXNzZXNzbWVudC50c1xuICAgICAgY29uc3QgcmF3U2NvcmUgPSBjYWxjdWxhdGVTZWN0aW9uU2NvcmUoc2VjdGlvbkFzc2Vzc21lbnQsIHNlY3Rpb25OdW0pO1xuICAgICAgY29uc3QgbWF4U2VjdGlvblNjb3JlID0gc2VjdGlvbk1heFNjb3Jlc1tzZWN0aW9uTnVtXSB8fCAwO1xuXG4gICAgICBzZWN0aW9uU2NvcmVzW3NlY3Rpb25OdW1dID0gcmF3U2NvcmU7XG5cbiAgICAgIC8vIE9ubHkgaW5jbHVkZSBzZWN0aW9ucyB3aXRoIG1heCBzY29yZXMgPiAwIGluIHRvdGFsIGNhbGN1bGF0aW9uXG4gICAgICBpZiAobWF4U2VjdGlvblNjb3JlID4gMCkge1xuICAgICAgICB0b3RhbFNjb3JlICs9IHJhd1Njb3JlO1xuICAgICAgICBtYXhQb3NzaWJsZSArPSBtYXhTZWN0aW9uU2NvcmU7XG4gICAgICB9XG5cbiAgICAgIC8vIEJ1aWxkIHNlY3Rpb24gZGV0YWlscyB3aXRoIHF1ZXN0aW9ucyBhbmQgYW5zd2Vyc1xuICAgICAgY29uc3QgcXVlc3Rpb25zID0gZXh0cmFjdFF1ZXN0aW9uc0Zyb21Bc3Nlc3NtZW50KHNlY3Rpb25Bc3Nlc3NtZW50LCBzZWN0aW9uTnVtKTtcblxuICAgICAgc2VjdGlvbkRldGFpbHNbc2VjdGlvbk51bV0gPSB7XG4gICAgICAgIHRpdGxlOiBzZWN0aW9uVGl0bGVzW3NlY3Rpb25OdW1dIHx8IGBTZWN0aW9uICR7c2VjdGlvbk51bX1gLFxuICAgICAgICBxdWVzdGlvbnMsXG4gICAgICAgIHNlY3Rpb25TY29yZTogcmF3U2NvcmUsXG4gICAgICAgIG1heFNlY3Rpb25TY29yZSxcbiAgICAgICAgcGVyY2VudGFnZTogbWF4U2VjdGlvblNjb3JlID4gMCA/IE1hdGgucm91bmQoKHJhd1Njb3JlIC8gbWF4U2VjdGlvblNjb3JlKSAqIDEwMCkgOiAwXG4gICAgICB9O1xuICAgIH1cblxuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBtYXhQb3NzaWJsZSA+IDAgPyBNYXRoLnJvdW5kKCh0b3RhbFNjb3JlIC8gbWF4UG9zc2libGUpICogMTAwKSA6IDA7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IHtcbiAgICAgIC4uLmZlZGVyYXRpb24sXG4gICAgICBhc3Nlc3NtZW50cyxcbiAgICAgIHRvdGFsU2NvcmUsXG4gICAgICBtYXhTY29yZTogbWF4UG9zc2libGUsXG4gICAgICBwZXJjZW50YWdlLFxuICAgICAgc2VjdGlvblNjb3JlcyxcbiAgICAgIHNlY3Rpb25EZXRhaWxzXG4gICAgfTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihyZXNwb25zZSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBHRVQgL2FwaS9mZWRlcmF0aW9uL1tpZF06JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZmV0Y2ggZmVkZXJhdGlvbiBkZXRhaWxzJywgZGV0YWlsczogZXJyb3I/Lm1lc3NhZ2UgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGV4dHJhY3QgcXVlc3Rpb25zIGFuZCBhbnN3ZXJzIGZyb20gYXNzZXNzbWVudCBkYXRhXG5mdW5jdGlvbiBleHRyYWN0UXVlc3Rpb25zRnJvbUFzc2Vzc21lbnQoYXNzZXNzbWVudDogYW55LCBzZWN0aW9uTnVtYmVyOiBudW1iZXIpIHtcbiAgaWYgKCFhc3Nlc3NtZW50KSByZXR1cm4gW107XG5cbiAgY29uc3QgcXVlc3Rpb25zOiB7IHF1ZXN0aW9uOiBzdHJpbmc7IGFuc3dlcjogc3RyaW5nOyBzY29yZT86IG51bWJlcjsgbWF4U2NvcmU/OiBudW1iZXIgfVtdID0gW107XG5cbiAgLy8gRGVmaW5lIHF1ZXN0aW9ucyBhbmQgYW5zd2VyIG1hcHBpbmdzIGZvciBlYWNoIHNlY3Rpb25cbiAgY29uc3Qgc2VjdGlvbkRhdGE6IHsgW2tleTogbnVtYmVyXTogeyBbZmllbGQ6IHN0cmluZ106IHsgcXVlc3Rpb246IHN0cmluZzsgYW5zd2VyTWFwPzogeyBba2V5OiBzdHJpbmddOiBzdHJpbmcgfSB9IH0gfSA9IHtcbiAgICAyOiB7XG4gICAgICAnbGVnYWxfcmVnaXN0cmF0aW9uX3N0YXR1cyc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdXaGF0IGlzIHRoZSBsZWdhbCByZWdpc3RyYXRpb24gc3RhdHVzIG9mIHRoZSBGZWRlcmF0aW9uPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICdhJzogJ1JlZ2lzdGVyZWQgd2l0aCBwcm9wZXIgZG9jdW1lbnRhdGlvbicsXG4gICAgICAgICAgJ2InOiAnUmVnaXN0ZXJlZCBidXQgZG9jdW1lbnRhdGlvbiBpbmNvbXBsZXRlJyxcbiAgICAgICAgICAnYyc6ICdSZWdpc3RyYXRpb24gaW4gcHJvZ3Jlc3MnLFxuICAgICAgICAgICdkJzogJ05vdCByZWdpc3RlcmVkJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ3JlZ2lzdHJhdGlvbl9hdXRob3JpdHknOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnV2hpY2ggYXV0aG9yaXR5IGlzIHRoZSBGZWRlcmF0aW9uIHJlZ2lzdGVyZWQgd2l0aD8nXG4gICAgICB9XG4gICAgfSxcbiAgICAzOiB7XG4gICAgICAndmlzaW9uX21pc3Npb25fc3RhdHVzJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0RvZXMgdGhlIEZlZGVyYXRpb24gaGF2ZSBhIGNsZWFyIHZpc2lvbiBhbmQgbWlzc2lvbj8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnMSc6ICdObyB2aXNpb24gb3IgbWlzc2lvbiBzdGF0ZW1lbnQnLFxuICAgICAgICAgICcyJzogJ1Zpc2lvbi9taXNzaW9uIGV4aXN0cyBidXQgbm90IHdlbGwgY29tbXVuaWNhdGVkJyxcbiAgICAgICAgICAnMyc6ICdWaXNpb24vbWlzc2lvbiBleGlzdHMgYW5kIHNvbWV3aGF0IGNvbW11bmljYXRlZCcsXG4gICAgICAgICAgJzQnOiAnQ2xlYXIgdmlzaW9uL21pc3Npb24sIHdlbGwgY29tbXVuaWNhdGVkIHRvIGFsbCdcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgICd2aXNpb25fcG9zdGVkJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0lzIHRoZSB2aXNpb24vbWlzc2lvbiBwb3N0ZWQgaW4gdmlzaWJsZSBsb2NhdGlvbnM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJywgJ25hJzogJ05vdCBBcHBsaWNhYmxlJyB9XG4gICAgICB9LFxuICAgICAgJ3Zpc2lvbl9pbl9kb2N1bWVudHMnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSXMgdGhlIHZpc2lvbi9taXNzaW9uIGluY2x1ZGVkIGluIG9mZmljaWFsIGRvY3VtZW50cz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHsgJ3llcyc6ICdZZXMnLCAnbm8nOiAnTm8nLCAnbmEnOiAnTm90IEFwcGxpY2FibGUnIH1cbiAgICAgIH0sXG4gICAgICAndmlzaW9uX2Zvcl9wbGFubmluZyc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdJcyB0aGUgdmlzaW9uL21pc3Npb24gdXNlZCBmb3Igc3RyYXRlZ2ljIHBsYW5uaW5nPycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycsICduYSc6ICdOb3QgQXBwbGljYWJsZScgfVxuICAgICAgfSxcbiAgICAgICdkZWNpc2lvbl9tYWtpbmcnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSG93IGFyZSBkZWNpc2lvbnMgbWFkZSBpbiB0aGUgRmVkZXJhdGlvbj8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnYSc6ICdEZW1vY3JhdGljIHByb2Nlc3Mgd2l0aCBtZW1iZXIgY29uc3VsdGF0aW9uJyxcbiAgICAgICAgICAnYic6ICdFeGVjdXRpdmUgY29tbWl0dGVlIGRlY2lzaW9ucycsXG4gICAgICAgICAgJ2MnOiAnVG9wLWRvd24gbGVhZGVyc2hpcCBkZWNpc2lvbnMnLFxuICAgICAgICAgICdkJzogJ0FkLWhvYyBkZWNpc2lvbiBtYWtpbmcnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnZW1lcmdpbmdfaXNzdWVzX2hhbmRsaW5nJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBkb2VzIHRoZSBGZWRlcmF0aW9uIGhhbmRsZSBlbWVyZ2luZyBpc3N1ZXM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJ2EnOiAnUHJvYWN0aXZlIGFwcHJvYWNoIHdpdGggc3lzdGVtYXRpYyByZXNwb25zZScsXG4gICAgICAgICAgJ2InOiAnUmVhY3RpdmUgYXBwcm9hY2ggd2l0aCBzb21lIHBsYW5uaW5nJyxcbiAgICAgICAgICAnYyc6ICdNaW5pbWFsIHJlc3BvbnNlIHRvIGlzc3VlcycsXG4gICAgICAgICAgJ2QnOiAnTm8gc3lzdGVtYXRpYyBhcHByb2FjaCdcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgICdsZWFkZXJzaGlwX2RldmVsb3BtZW50X3BsYW4nOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnRG9lcyB0aGUgRmVkZXJhdGlvbiBoYXZlIGEgbGVhZGVyc2hpcCBkZXZlbG9wbWVudCBwbGFuPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICdhJzogJ0NvbXByZWhlbnNpdmUgbGVhZGVyc2hpcCBkZXZlbG9wbWVudCBwcm9ncmFtJyxcbiAgICAgICAgICAnYic6ICdCYXNpYyBsZWFkZXJzaGlwIHRyYWluaW5nIGF2YWlsYWJsZScsXG4gICAgICAgICAgJ2MnOiAnSW5mb3JtYWwgbGVhZGVyc2hpcCBkZXZlbG9wbWVudCcsXG4gICAgICAgICAgJ2QnOiAnTm8gbGVhZGVyc2hpcCBkZXZlbG9wbWVudCBwbGFuJ1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICA0OiB7XG4gICAgICAnb3JnYW5pemF0aW9uYWxfc3RydWN0dXJlJzoge1xuICAgICAgICBxdWVzdGlvbjogJ1doYXQgaXMgdGhlIG9yZ2FuaXphdGlvbmFsIHN0cnVjdHVyZSBvZiB0aGUgRmVkZXJhdGlvbj8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnYSc6ICdXZWxsLWRlZmluZWQgaGllcmFyY2hpY2FsIHN0cnVjdHVyZScsXG4gICAgICAgICAgJ2InOiAnQmFzaWMgb3JnYW5pemF0aW9uYWwgc3RydWN0dXJlJyxcbiAgICAgICAgICAnYyc6ICdJbmZvcm1hbCBzdHJ1Y3R1cmUnLFxuICAgICAgICAgICdkJzogJ05vIGNsZWFyIHN0cnVjdHVyZSdcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgICdyb2xlc19kZWZpbmVkJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0FyZSByb2xlcyBhbmQgcmVzcG9uc2liaWxpdGllcyBjbGVhcmx5IGRlZmluZWQ/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJ2EnOiAnQWxsIHJvbGVzIGNsZWFybHkgZG9jdW1lbnRlZCBhbmQgY29tbXVuaWNhdGVkJyxcbiAgICAgICAgICAnYic6ICdNb3N0IHJvbGVzIGRlZmluZWQnLFxuICAgICAgICAgICdjJzogJ1NvbWUgcm9sZXMgZGVmaW5lZCcsXG4gICAgICAgICAgJ2QnOiAnUm9sZXMgbm90IGNsZWFybHkgZGVmaW5lZCdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0sXG4gICAgNToge1xuICAgICAgJ21hbmFnZW1lbnRfYXBwcm9hY2gnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSG93IGlzIHRoZSBGZWRlcmF0aW9uIG1hbmFnZWQ/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJ2EnOiAnUHJvZmVzc2lvbmFsIG1hbmFnZW1lbnQgd2l0aCBjbGVhciBwcm9jZXNzZXMnLFxuICAgICAgICAgICdiJzogJ1N0cnVjdHVyZWQgbWFuYWdlbWVudCBhcHByb2FjaCcsXG4gICAgICAgICAgJ2MnOiAnQmFzaWMgbWFuYWdlbWVudCBwcmFjdGljZXMnLFxuICAgICAgICAgICdkJzogJ0luZm9ybWFsIG1hbmFnZW1lbnQnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnYXV0aG9yaXR5X2RlbGVnYXRpb24nOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSG93IGlzIGF1dGhvcml0eSBkZWxlZ2F0ZWQgaW4gdGhlIEZlZGVyYXRpb24/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJ2EnOiAnQ2xlYXIgZGVsZWdhdGlvbiB3aXRoIHByb3BlciBvdmVyc2lnaHQnLFxuICAgICAgICAgICdiJzogJ1NvbWUgZGVsZWdhdGlvbiB3aXRoIG1vbml0b3JpbmcnLFxuICAgICAgICAgICdjJzogJ0xpbWl0ZWQgZGVsZWdhdGlvbicsXG4gICAgICAgICAgJ2QnOiAnQ2VudHJhbGl6ZWQgYXV0aG9yaXR5LCBubyBkZWxlZ2F0aW9uJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ2RlY2lzaW9uX21ha2luZ19wcm9jZXNzJzoge1xuICAgICAgICBxdWVzdGlvbjogJ1doYXQgaXMgdGhlIGRlY2lzaW9uLW1ha2luZyBwcm9jZXNzPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICcxJzogJ05vIGZvcm1hbCBwcm9jZXNzJyxcbiAgICAgICAgICAnMic6ICdCYXNpYyBwcm9jZXNzIGV4aXN0cycsXG4gICAgICAgICAgJzMnOiAnU3RydWN0dXJlZCBwcm9jZXNzIHdpdGggZG9jdW1lbnRhdGlvbicsXG4gICAgICAgICAgJzQnOiAnQ29tcHJlaGVuc2l2ZSBwcm9jZXNzIHdpdGggc3Rha2Vob2xkZXIgaW52b2x2ZW1lbnQnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnZGVwdXR5X2F2YWlsYWJpbGl0eSc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdJcyB0aGVyZSBhIGRlcHV0eSBhdmFpbGFibGUgZm9yIGtleSBwb3NpdGlvbnM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJ2EnOiAnQWxsIGtleSBwb3NpdGlvbnMgaGF2ZSBkZXNpZ25hdGVkIGRlcHV0aWVzJyxcbiAgICAgICAgICAnYic6ICdNb3N0IHBvc2l0aW9ucyBoYXZlIGRlcHV0aWVzJyxcbiAgICAgICAgICAnYyc6ICdTb21lIHBvc2l0aW9ucyBoYXZlIGRlcHV0aWVzJyxcbiAgICAgICAgICAnZCc6ICdObyBkZXB1dHkgc3lzdGVtIGluIHBsYWNlJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ3RyYW5zaXRpb25fcGxhbic6IHtcbiAgICAgICAgcXVlc3Rpb246ICdJcyB0aGVyZSBhIHN1Y2Nlc3Npb24vdHJhbnNpdGlvbiBwbGFuPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICdhJzogJ0NvbXByZWhlbnNpdmUgc3VjY2Vzc2lvbiBwbGFubmluZycsXG4gICAgICAgICAgJ2InOiAnQmFzaWMgdHJhbnNpdGlvbiBwbGFubmluZycsXG4gICAgICAgICAgJ2MnOiAnSW5mb3JtYWwgc3VjY2Vzc2lvbiBhcnJhbmdlbWVudHMnLFxuICAgICAgICAgICdkJzogJ05vIHN1Y2Nlc3Npb24gcGxhbm5pbmcnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIDY6IHtcbiAgICAgICd3b3JrZXJzX2ludm9sdmVtZW50X2xldmVsJzoge1xuICAgICAgICBxdWVzdGlvbjogJ1doYXQgaXMgdGhlIGxldmVsIG9mIHdvcmtlcnNcXCcgaW52b2x2ZW1lbnQgaW4gdGhlIEZlZGVyYXRpb24/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJzEnOiAnTWluaW1hbCBpbnZvbHZlbWVudCcsXG4gICAgICAgICAgJzInOiAnQmFzaWMgaW52b2x2ZW1lbnQgaW4gc29tZSBhY3Rpdml0aWVzJyxcbiAgICAgICAgICAnMyc6ICdHb29kIGludm9sdmVtZW50IGluIG1vc3QgYWN0aXZpdGllcycsXG4gICAgICAgICAgJzQnOiAnSGlnaCBsZXZlbCBvZiBpbnZvbHZlbWVudCBpbiBhbGwgYWN0aXZpdGllcydcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgICdpbnZvbHZlX3Byb2dyYW1fYWN0aXZpdGllcyc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdBcmUgd29ya2VycyBpbnZvbHZlZCBpbiBwcm9ncmFtIGFjdGl2aXRpZXM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJyB9XG4gICAgICB9LFxuICAgICAgJ2ludm9sdmVfbGVhZGVyc19vcmllbnRhdGlvbic6IHtcbiAgICAgICAgcXVlc3Rpb246ICdBcmUgd29ya2VycyBpbnZvbHZlZCBpbiBsZWFkZXJzaGlwIG9yaWVudGF0aW9uPycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycgfVxuICAgICAgfSxcbiAgICAgICdzb2xpY2l0X2ZlZWRiYWNrJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0RvZXMgdGhlIEZlZGVyYXRpb24gc29saWNpdCBmZWVkYmFjayBmcm9tIHdvcmtlcnM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJyB9XG4gICAgICB9LFxuICAgICAgJ3JlZ3VsYXJfaW50ZXJhY3Rpb24nOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSXMgdGhlcmUgcmVndWxhciBpbnRlcmFjdGlvbiB3aXRoIHdvcmtlcnM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJyB9XG4gICAgICB9LFxuICAgICAgJ3NoYXJlX3Jlc3VsdHMnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnQXJlIHJlc3VsdHMgc2hhcmVkIHdpdGggd29ya2Vycz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHsgJ3llcyc6ICdZZXMnLCAnbm8nOiAnTm8nIH1cbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8gQ29udGludWUgd2l0aCByZW1haW5pbmcgc2VjdGlvbnMuLi5cbiAgY29uc3QgcmVtYWluaW5nU2VjdGlvbnMgPSB7XG4gICAgNzoge1xuICAgICAgJ2N1bHR1cmFsX2dlbmRlcl9jb25zaWRlcmF0aW9uJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBkb2VzIHRoZSBGZWRlcmF0aW9uIGNvbnNpZGVyIGN1bHR1cmFsIGFuZCBnZW5kZXIgYXNwZWN0cz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnMSc6ICdObyBjb25zaWRlcmF0aW9uJyxcbiAgICAgICAgICAnMic6ICdCYXNpYyBhd2FyZW5lc3MnLFxuICAgICAgICAgICczJzogJ0dvb2QgY29uc2lkZXJhdGlvbiB3aXRoIHNvbWUgcG9saWNpZXMnLFxuICAgICAgICAgICc0JzogJ0NvbXByZWhlbnNpdmUgY3VsdHVyYWwgYW5kIGdlbmRlciBwb2xpY2llcydcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgICdjb25zaWRlcl9sb2NhbF9jdWx0dXJlJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0RvZXMgdGhlIEZlZGVyYXRpb24gY29uc2lkZXIgbG9jYWwgY3VsdHVyZT8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHsgJ3llcyc6ICdZZXMnLCAnbm8nOiAnTm8nIH1cbiAgICAgIH0sXG4gICAgICAnZG9jdW1lbnRlZF9ndWlkZWxpbmVzJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0FyZSB0aGVyZSBkb2N1bWVudGVkIGN1bHR1cmFsIGd1aWRlbGluZXM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJyB9XG4gICAgICB9LFxuICAgICAgJ3Byb3ZpZGVfdHJhaW5pbmcnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnRG9lcyB0aGUgRmVkZXJhdGlvbiBwcm92aWRlIGN1bHR1cmFsIHNlbnNpdGl2aXR5IHRyYWluaW5nPycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycgfVxuICAgICAgfSxcbiAgICAgICd1c2VfYXNzZXNzbWVudF9maW5kaW5ncyc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdBcmUgYXNzZXNzbWVudCBmaW5kaW5ncyB1c2VkIGZvciBpbXByb3ZlbWVudD8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHsgJ3llcyc6ICdZZXMnLCAnbm8nOiAnTm8nIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIDg6IHtcbiAgICAgICdyZXByZXNlbnRhdGlvbl9lZmZlY3RpdmVuZXNzJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBlZmZlY3RpdmUgaXMgdGhlIEZlZGVyYXRpb25cXCdzIHJlcHJlc2VudGF0aW9uPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICcxJzogJ1Bvb3IgcmVwcmVzZW50YXRpb24nLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIHJlcHJlc2VudGF0aW9uJyxcbiAgICAgICAgICAnMyc6ICdHb29kIHJlcHJlc2VudGF0aW9uJyxcbiAgICAgICAgICAnNCc6ICdFeGNlbGxlbnQgcmVwcmVzZW50YXRpb24nXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnbWVtYmVyX2ludm9sdmVtZW50Jzoge1xuICAgICAgICBxdWVzdGlvbjogJ1doYXQgaXMgdGhlIGxldmVsIG9mIG1lbWJlciBpbnZvbHZlbWVudCBpbiBjb2xsZWN0aXZlIGJhcmdhaW5pbmc/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJzEnOiAnTm8gbWVtYmVyIGludm9sdmVtZW50JyxcbiAgICAgICAgICAnMic6ICdMaW1pdGVkIGludm9sdmVtZW50JyxcbiAgICAgICAgICAnMyc6ICdHb29kIG1lbWJlciBpbnZvbHZlbWVudCcsXG4gICAgICAgICAgJzQnOiAnSGlnaCBtZW1iZXIgaW52b2x2ZW1lbnQnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnYmFyZ2FpbmluZ19zdHJhdGVneSc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdXaGF0IGlzIHRoZSBGZWRlcmF0aW9uXFwncyBiYXJnYWluaW5nIHN0cmF0ZWd5PycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICdhJzogJ0NvbXByZWhlbnNpdmUgc3RyYXRlZ2ljIGFwcHJvYWNoJyxcbiAgICAgICAgICAnYic6ICdTdHJ1Y3R1cmVkIGJhcmdhaW5pbmcgYXBwcm9hY2gnLFxuICAgICAgICAgICdjJzogJ0Jhc2ljIGJhcmdhaW5pbmcgdGFjdGljcycsXG4gICAgICAgICAgJ2QnOiAnQWQtaG9jIGJhcmdhaW5pbmcnXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIDk6IHtcbiAgICAgICdjb21tdW5pY2F0aW9uX2VmZmVjdGl2ZW5lc3MnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSG93IGVmZmVjdGl2ZSBpcyB0aGUgRmVkZXJhdGlvblxcJ3MgY29tbXVuaWNhdGlvbj8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnMSc6ICdQb29yIGNvbW11bmljYXRpb24nLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIGNvbW11bmljYXRpb24nLFxuICAgICAgICAgICczJzogJ0dvb2QgY29tbXVuaWNhdGlvbicsXG4gICAgICAgICAgJzQnOiAnRXhjZWxsZW50IGNvbW11bmljYXRpb24nXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnbWVtYmVyX2VuZ2FnZW1lbnQnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnV2hhdCBpcyB0aGUgbGV2ZWwgb2YgbWVtYmVyIGVuZ2FnZW1lbnQ/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJzEnOiAnTG93IGVuZ2FnZW1lbnQnLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIGVuZ2FnZW1lbnQnLFxuICAgICAgICAgICczJzogJ0dvb2QgZW5nYWdlbWVudCcsXG4gICAgICAgICAgJzQnOiAnSGlnaCBlbmdhZ2VtZW50J1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ3BhcnRpY2lwYXRpb25fb3Bwb3J0dW5pdGllcyc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdXaGF0IHBhcnRpY2lwYXRpb24gb3Bwb3J0dW5pdGllcyBhcmUgYXZhaWxhYmxlPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICcxJzogJ0xpbWl0ZWQgb3Bwb3J0dW5pdGllcycsXG4gICAgICAgICAgJzInOiAnQmFzaWMgb3Bwb3J0dW5pdGllcycsXG4gICAgICAgICAgJzMnOiAnR29vZCBvcHBvcnR1bml0aWVzJyxcbiAgICAgICAgICAnNCc6ICdFeHRlbnNpdmUgb3Bwb3J0dW5pdGllcydcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgICdmZWVkYmFja19jb2xsZWN0aW9uJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBkb2VzIHRoZSBGZWRlcmF0aW9uIGNvbGxlY3QgbWVtYmVyIGZlZWRiYWNrPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICdhJzogJ1N5c3RlbWF0aWMgZmVlZGJhY2sgY29sbGVjdGlvbicsXG4gICAgICAgICAgJ2InOiAnUmVndWxhciBmZWVkYmFjayBzZXNzaW9ucycsXG4gICAgICAgICAgJ2MnOiAnT2NjYXNpb25hbCBmZWVkYmFjayBjb2xsZWN0aW9uJyxcbiAgICAgICAgICAnZCc6ICdObyBmb3JtYWwgZmVlZGJhY2sgc3lzdGVtJ1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICAxMDoge1xuICAgICAgJ2ZlZV9jb2xsZWN0aW9uJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBkb2VzIHRoZSBGZWRlcmF0aW9uIGNvbGxlY3QgZmVlcz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnMSc6ICdObyBzeXN0ZW1hdGljIGNvbGxlY3Rpb24nLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIGNvbGxlY3Rpb24gc3lzdGVtJyxcbiAgICAgICAgICAnMyc6ICdHb29kIGNvbGxlY3Rpb24gc3lzdGVtJyxcbiAgICAgICAgICAnNCc6ICdFeGNlbGxlbnQgc3lzdGVtYXRpYyBjb2xsZWN0aW9uJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ2ZpbmFuY2lhbF9tYW5hZ2VtZW50Jzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBpcyBmaW5hbmNpYWwgbWFuYWdlbWVudCBoYW5kbGVkPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICcxJzogJ1Bvb3IgZmluYW5jaWFsIG1hbmFnZW1lbnQnLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIGZpbmFuY2lhbCBtYW5hZ2VtZW50JyxcbiAgICAgICAgICAnMyc6ICdHb29kIGZpbmFuY2lhbCBtYW5hZ2VtZW50JyxcbiAgICAgICAgICAnNCc6ICdFeGNlbGxlbnQgZmluYW5jaWFsIG1hbmFnZW1lbnQnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnZmluYW5jaWFsX3BsYW5uaW5nJzoge1xuICAgICAgICBxdWVzdGlvbjogJ1doYXQgaXMgdGhlIGFwcHJvYWNoIHRvIGZpbmFuY2lhbCBwbGFubmluZz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHtcbiAgICAgICAgICAnMSc6ICdObyBmaW5hbmNpYWwgcGxhbm5pbmcnLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIGZpbmFuY2lhbCBwbGFubmluZycsXG4gICAgICAgICAgJzMnOiAnR29vZCBmaW5hbmNpYWwgcGxhbm5pbmcnLFxuICAgICAgICAgICc0JzogJ0NvbXByZWhlbnNpdmUgZmluYW5jaWFsIHBsYW5uaW5nJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ2ZpbmFuY2lhbF9zeXN0ZW1fcXVhbGl0eSc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdXaGF0IGlzIHRoZSBxdWFsaXR5IG9mIHRoZSBmaW5hbmNpYWwgc3lzdGVtPycsXG4gICAgICAgIGFuc3dlck1hcDoge1xuICAgICAgICAgICcxJzogJ1Bvb3IgZmluYW5jaWFsIHN5c3RlbXMnLFxuICAgICAgICAgICcyJzogJ0Jhc2ljIGZpbmFuY2lhbCBzeXN0ZW1zJyxcbiAgICAgICAgICAnMyc6ICdHb29kIGZpbmFuY2lhbCBzeXN0ZW1zJyxcbiAgICAgICAgICAnNCc6ICdFeGNlbGxlbnQgZmluYW5jaWFsIHN5c3RlbXMnXG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAnaGFzX2Nhc2hfc3lzdGVtJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0RvZXMgdGhlIEZlZGVyYXRpb24gaGF2ZSBhIGNhc2ggbWFuYWdlbWVudCBzeXN0ZW0/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJyB9XG4gICAgICB9LFxuICAgICAgJ3VzZXNfYWNjb3VudGluZ19zb2Z0d2FyZSc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdEb2VzIHRoZSBGZWRlcmF0aW9uIHVzZSBhY2NvdW50aW5nIHNvZnR3YXJlPycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycgfVxuICAgICAgfSxcbiAgICAgICdoYXNfY2hhcnRfYWNjb3VudHMnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnRG9lcyB0aGUgRmVkZXJhdGlvbiBoYXZlIGEgY2hhcnQgb2YgYWNjb3VudHM/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJyB9XG4gICAgICB9LFxuICAgICAgJ3JlY29uY2lsZXNfbW9udGhseSc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdEb2VzIHRoZSBGZWRlcmF0aW9uIHJlY29uY2lsZSBhY2NvdW50cyBtb250aGx5PycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycgfVxuICAgICAgfVxuICAgIH0sXG4gICAgMTE6IHtcbiAgICAgICdhdWRpdF9zeXN0ZW1fcXVhbGl0eSc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdXaGF0IGlzIHRoZSBxdWFsaXR5IG9mIHRoZSBhdWRpdCBzeXN0ZW0/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7XG4gICAgICAgICAgJzEnOiAnTm8gYXVkaXQgc3lzdGVtJyxcbiAgICAgICAgICAnMic6ICdCYXNpYyBhdWRpdCBzeXN0ZW0nLFxuICAgICAgICAgICczJzogJ0dvb2QgYXVkaXQgc3lzdGVtJyxcbiAgICAgICAgICAnNCc6ICdFeGNlbGxlbnQgYXVkaXQgc3lzdGVtJ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgJ3JlcXVpcmVzX2FubnVhbF9hdWRpdCc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdEb2VzIHRoZSBGZWRlcmF0aW9uIHJlcXVpcmUgYW5udWFsIGF1ZGl0cz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHsgJ3llcyc6ICdZZXMnLCAnbm8nOiAnTm8nLCAnbmEnOiAnTm90IEFwcGxpY2FibGUnIH1cbiAgICAgIH0sXG4gICAgICAncmVndWxhcmx5X2F1ZGl0ZWQnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnSXMgdGhlIEZlZGVyYXRpb24gcmVndWxhcmx5IGF1ZGl0ZWQ/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJywgJ25hJzogJ05vdCBBcHBsaWNhYmxlJyB9XG4gICAgICB9LFxuICAgICAgJ2F1ZGl0b3Jfc2VsZWN0aW9uJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0hvdyBhcmUgYXVkaXRvcnMgc2VsZWN0ZWQ/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJywgJ25hJzogJ05vdCBBcHBsaWNhYmxlJyB9XG4gICAgICB9LFxuICAgICAgJ2F1ZGl0X21hbmFnZXInOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnV2hvIG1hbmFnZXMgdGhlIGF1ZGl0IHByb2Nlc3M/JyxcbiAgICAgICAgYW5zd2VyTWFwOiB7ICd5ZXMnOiAnWWVzJywgJ25vJzogJ05vJywgJ25hJzogJ05vdCBBcHBsaWNhYmxlJyB9XG4gICAgICB9LFxuICAgICAgJ2ltcGxlbWVudHNfcmVjb21tZW5kYXRpb25zJzoge1xuICAgICAgICBxdWVzdGlvbjogJ0RvZXMgdGhlIEZlZGVyYXRpb24gaW1wbGVtZW50IGF1ZGl0IHJlY29tbWVuZGF0aW9ucz8nLFxuICAgICAgICBhbnN3ZXJNYXA6IHsgJ3llcyc6ICdZZXMnLCAnbm8nOiAnTm8nLCAnbmEnOiAnTm90IEFwcGxpY2FibGUnIH1cbiAgICAgIH0sXG4gICAgICAnc2hhcmVzX3JlcG9ydHMnOiB7XG4gICAgICAgIHF1ZXN0aW9uOiAnQXJlIGF1ZGl0IHJlcG9ydHMgc2hhcmVkIHdpdGggc3Rha2Vob2xkZXJzPycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycsICduYSc6ICdOb3QgQXBwbGljYWJsZScgfVxuICAgICAgfSxcbiAgICAgICdyZXBvcnRfcHJvdmlkZXNfaW5mbyc6IHtcbiAgICAgICAgcXVlc3Rpb246ICdEbyBhdWRpdCByZXBvcnRzIHByb3ZpZGUgdXNlZnVsIGluZm9ybWF0aW9uPycsXG4gICAgICAgIGFuc3dlck1hcDogeyAneWVzJzogJ1llcycsICdubyc6ICdObycsICduYSc6ICdOb3QgQXBwbGljYWJsZScgfVxuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyBNZXJnZSBhbGwgc2VjdGlvbnNcbiAgY29uc3QgYWxsU2VjdGlvbnMgPSB7IC4uLnNlY3Rpb25EYXRhLCAuLi5yZW1haW5pbmdTZWN0aW9ucyB9O1xuICBjb25zdCBzZWN0aW9uUXVlc3Rpb25NYXAgPSBhbGxTZWN0aW9uc1tzZWN0aW9uTnVtYmVyXSB8fCB7fTtcblxuICAvLyBFeHRyYWN0IHF1ZXN0aW9ucyBhbmQgYW5zd2Vyc1xuICBPYmplY3QuZW50cmllcyhzZWN0aW9uUXVlc3Rpb25NYXApLmZvckVhY2goKFtmaWVsZCwgY29uZmlnXSkgPT4ge1xuICAgIGNvbnN0IHJhd0Fuc3dlciA9IGFzc2Vzc21lbnRbZmllbGRdO1xuICAgIGlmIChyYXdBbnN3ZXIgIT09IHVuZGVmaW5lZCAmJiByYXdBbnN3ZXIgIT09IG51bGwgJiYgcmF3QW5zd2VyICE9PSAnJykge1xuICAgICAgbGV0IGRlY29kZWRBbnN3ZXIgPSByYXdBbnN3ZXI7XG5cbiAgICAgIC8vIERlY29kZSBhbnN3ZXIgaWYgbWFwcGluZyBleGlzdHNcbiAgICAgIGlmIChjb25maWcuYW5zd2VyTWFwICYmIGNvbmZpZy5hbnN3ZXJNYXBbcmF3QW5zd2VyXSkge1xuICAgICAgICBkZWNvZGVkQW5zd2VyID0gY29uZmlnLmFuc3dlck1hcFtyYXdBbnN3ZXJdO1xuICAgICAgfVxuXG4gICAgICBxdWVzdGlvbnMucHVzaCh7XG4gICAgICAgIHF1ZXN0aW9uOiBjb25maWcucXVlc3Rpb24sXG4gICAgICAgIGFuc3dlcjogZGVjb2RlZEFuc3dlcixcbiAgICAgICAgLy8gTm90ZTogSW5kaXZpZHVhbCBxdWVzdGlvbiBzY29yZXMgYXJlIG5vdCBzdG9yZWQgc2VwYXJhdGVseSBpbiBjdXJyZW50IHN0cnVjdHVyZVxuICAgICAgICAvLyBUaGV5IHdvdWxkIG5lZWQgdG8gYmUgYWRkZWQgdG8gdGhlIGFzc2Vzc21lbnQgZGF0YSBzdHJ1Y3R1cmUgaWYgbmVlZGVkXG4gICAgICB9KTtcbiAgICB9XG4gIH0pO1xuXG4gIHJldHVybiBxdWVzdGlvbnM7XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwicmVkaXMiLCJzYWZlUmVkaXNPcGVyYXRpb24iLCJjYWxjdWxhdGVTZWN0aW9uU2NvcmUiLCJhc3Nlc3NtZW50Iiwic2VjdGlvbk51bWJlciIsInNlY3Rpb25TY29yZSIsInZpc2lvbl9taXNzaW9uX3N0YXR1cyIsInZpc2lvbl9wb3N0ZWQiLCJ2aXNpb25faW5fZG9jdW1lbnRzIiwidmlzaW9uX2Zvcl9wbGFubmluZyIsImRlY2lzaW9uX21ha2luZyIsImVtZXJnaW5nX2lzc3Vlc19oYW5kbGluZyIsImxlYWRlcnNoaXBfZGV2ZWxvcG1lbnRfcGxhbiIsIm9yZ2FuaXphdGlvbmFsX3N0cnVjdHVyZSIsInJvbGVzX2RlZmluZWQiLCJtYW5hZ2VtZW50X2FwcHJvYWNoIiwiYXV0aG9yaXR5X2RlbGVnYXRpb24iLCJkZWNpc2lvbl9tYWtpbmdfcHJvY2VzcyIsImRlcHV0eV9hdmFpbGFiaWxpdHkiLCJ0cmFuc2l0aW9uX3BsYW4iLCJ3b3JrZXJzX2ludm9sdmVtZW50X2xldmVsIiwiaW52b2x2ZV9wcm9ncmFtX2FjdGl2aXRpZXMiLCJpbnZvbHZlX2xlYWRlcnNfb3JpZW50YXRpb24iLCJzb2xpY2l0X2ZlZWRiYWNrIiwicmVndWxhcl9pbnRlcmFjdGlvbiIsInNoYXJlX3Jlc3VsdHMiLCJjdWx0dXJhbF9nZW5kZXJfY29uc2lkZXJhdGlvbiIsImNvbnNpZGVyX2xvY2FsX2N1bHR1cmUiLCJkb2N1bWVudGVkX2d1aWRlbGluZXMiLCJwcm92aWRlX3RyYWluaW5nIiwidXNlX2Fzc2Vzc21lbnRfZmluZGluZ3MiLCJyZXByZXNlbnRhdGlvbl9lZmZlY3RpdmVuZXNzIiwibWVtYmVyX2ludm9sdmVtZW50IiwiYmFyZ2FpbmluZ19zdHJhdGVneSIsImNvbW11bmljYXRpb25fZWZmZWN0aXZlbmVzcyIsIm1lbWJlcl9lbmdhZ2VtZW50IiwicGFydGljaXBhdGlvbl9vcHBvcnR1bml0aWVzIiwiZmVlZGJhY2tfY29sbGVjdGlvbiIsImZlZV9jb2xsZWN0aW9uIiwiZmluYW5jaWFsX21hbmFnZW1lbnQiLCJmaW5hbmNpYWxfcGxhbm5pbmciLCJmaW5hbmNpYWxfc3lzdGVtX3F1YWxpdHkiLCJoYXNfY2FzaF9zeXN0ZW0iLCJ1c2VzX2FjY291bnRpbmdfc29mdHdhcmUiLCJoYXNfY2hhcnRfYWNjb3VudHMiLCJyZWNvbmNpbGVzX21vbnRobHkiLCJhdWRpdF9zeXN0ZW1fcXVhbGl0eSIsInJlcXVpcmVzX2FubnVhbF9hdWRpdCIsInJlZ3VsYXJseV9hdWRpdGVkIiwiYXVkaXRvcl9zZWxlY3Rpb24iLCJhdWRpdF9tYW5hZ2VyIiwiaW1wbGVtZW50c19yZWNvbW1lbmRhdGlvbnMiLCJzaGFyZXNfcmVwb3J0cyIsInJlcG9ydF9wcm92aWRlc19pbmZvIiwiR0VUIiwicmVxdWVzdCIsInBhcmFtcyIsImlkIiwiZmVkZXJhdGlvbklkIiwiZmVkZXJhdGlvbkRhdGEiLCJnZXQiLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJmZWRlcmF0aW9uIiwiSlNPTiIsInBhcnNlIiwiYXNzZXNzbWVudEtleXMiLCJrZXlzIiwiYXNzZXNzbWVudHNSYXciLCJQcm9taXNlIiwiYWxsIiwibWFwIiwia2V5IiwiYXNzZXNzbWVudHMiLCJhIiwiZmlsdGVyIiwic2VjdGlvbk1heFNjb3JlcyIsInNlY3Rpb25UaXRsZXMiLCJzZWN0aW9uU2NvcmVzIiwic2VjdGlvbkRldGFpbHMiLCJ0b3RhbFNjb3JlIiwibWF4UG9zc2libGUiLCJhbGxTZWN0aW9uTnVtYmVycyIsIlNldCIsIk9iamVjdCIsIk51bWJlciIsInNlY3Rpb25fbnVtYmVyIiwic2VjdGlvbk51bSIsIkFycmF5IiwiZnJvbSIsInNvcnQiLCJzZWN0aW9uQXNzZXNzbWVudCIsImZpbmQiLCJyYXdTY29yZSIsIm1heFNlY3Rpb25TY29yZSIsInF1ZXN0aW9ucyIsImV4dHJhY3RRdWVzdGlvbnNGcm9tQXNzZXNzbWVudCIsInRpdGxlIiwicGVyY2VudGFnZSIsIk1hdGgiLCJyb3VuZCIsInJlc3BvbnNlIiwibWF4U2NvcmUiLCJjb25zb2xlIiwiZGV0YWlscyIsIm1lc3NhZ2UiLCJzZWN0aW9uRGF0YSIsInF1ZXN0aW9uIiwiYW5zd2VyTWFwIiwicmVtYWluaW5nU2VjdGlvbnMiLCJhbGxTZWN0aW9ucyIsInNlY3Rpb25RdWVzdGlvbk1hcCIsImVudHJpZXMiLCJmb3JFYWNoIiwiZmllbGQiLCJjb25maWciLCJyYXdBbnN3ZXIiLCJ1bmRlZmluZWQiLCJkZWNvZGVkQW5zd2VyIiwicHVzaCIsImFuc3dlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/federation/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/upstash.ts":
/*!************************!*\
  !*** ./lib/upstash.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   safeRedisOperation: () => (/* binding */ safeRedisOperation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: \"https://ample-bear-14398.upstash.io\",\n    token: \"ATg-AAIjcDExMDY2ODQwYjIwNDI0MWQxODg4MjExMzg3YTMzYTRhNnAxMA\"\n});\n// Helper function to handle Redis operations with error checking\nasync function safeRedisOperation(operation, fallback) {\n    try {\n        const result = await operation();\n        if (result === null && fallback !== undefined) {\n            return fallback;\n        }\n        if (result === null) {\n            return {};\n        }\n        return result;\n    } catch (error) {\n        if (fallback !== undefined) {\n            console.error('Redis operation failed, using fallback:', error);\n            return fallback;\n        }\n        if (error.message?.includes('NOPERM')) {\n            console.error('Redis authentication failed:', error);\n            throw new Error('Database access denied. Please check your credentials.');\n        }\n        if (error.message?.includes('connect')) {\n            console.error('Redis connection error:', error);\n            throw new Error('Failed to connect to database. Please try again.');\n        }\n        if (error.message?.includes('JSON')) {\n            console.error('Redis JSON parsing error:', error);\n            throw new Error('Error retrieving user data. Invalid data format.');\n        }\n        console.error('Redis operation error:', error);\n        throw new Error('Error retrieving user data. Please try again.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/upstash.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_tucat_tucat_app_api_federation_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/federation/[id]/route.ts */ \"(rsc)/./app/api/federation/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/federation/[id]/route\",\n        pathname: \"/api/federation/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/federation/[id]/route\"\n    },\n    resolvedPagePath: \"D:\\\\tucat\\\\tucat\\\\app\\\\api\\\\federation\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_tucat_tucat_app_api_federation_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&page=%2Fapi%2Ffederation%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederation%2F%5Bid%5D%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();