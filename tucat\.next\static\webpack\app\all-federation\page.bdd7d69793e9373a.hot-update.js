"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/all-federation/page",{

/***/ "(app-pages-browser)/./components/dashboard/QAStatsCard.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/QAStatsCard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QAStatsCard: () => (/* binding */ QAStatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ QAStatsCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst COLORS = [\n    '#0088FE',\n    '#00C49F',\n    '#FFBB28',\n    '#FF8042',\n    '#8884D8',\n    '#82CA9D',\n    '#FFC658',\n    '#FF7C7C',\n    '#8DD1E1',\n    '#D084D0',\n    '#87D068',\n    '#FFB347'\n];\nconst sectionTitles = {\n    3: \"Leadership\",\n    4: \"Organizational Structure\",\n    5: \"Management\",\n    6: \"Worker Participation\",\n    7: \"Culture and Gender\",\n    8: \"Collective Bargaining\",\n    9: \"Member Engagement\",\n    10: \"Financial Stability\",\n    11: \"Audit & Compliance\"\n};\nfunction QAStatsCard() {\n    _s();\n    const [qaStats, setQAStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedSection, setSelectedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bar');\n    const [expandedQuestions, setExpandedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QAStatsCard.useEffect\": ()=>{\n            fetchQAStats();\n        }\n    }[\"QAStatsCard.useEffect\"], []);\n    const fetchQAStats = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch('/api/qa-stats');\n            if (!response.ok) {\n                throw new Error('Failed to fetch Q&A statistics');\n            }\n            const data = await response.json();\n            setQAStats(data);\n        } catch (error) {\n            console.error('Error fetching Q&A stats:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load Q&A statistics\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleQuestionExpansion = (questionField)=>{\n        const newExpanded = new Set(expandedQuestions);\n        if (newExpanded.has(questionField)) {\n            newExpanded.delete(questionField);\n        } else {\n            newExpanded.add(questionField);\n        }\n        setExpandedQuestions(newExpanded);\n    };\n    const expandAllQuestions = ()=>{\n        if (currentSectionData) {\n            const allFields = new Set(currentSectionData.questions.map((q)=>q.field));\n            setExpandedQuestions(allFields);\n        }\n    };\n    const collapseAllQuestions = ()=>{\n        setExpandedQuestions(new Set());\n    };\n    const renderBarChart = (questionData)=>{\n        const data = questionData.responses.map((response)=>({\n                answer: response.answer.length > 20 ? response.answer.substring(0, 20) + '...' : response.answer,\n                fullAnswer: response.answer,\n                count: response.count,\n                percentage: response.percentage\n            }));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.BarChart, {\n                data: data,\n                margin: {\n                    top: 20,\n                    right: 30,\n                    left: 20,\n                    bottom: 60\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                        strokeDasharray: \"3 3\",\n                        stroke: \"#e5e7eb\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                        dataKey: \"answer\",\n                        angle: -45,\n                        textAnchor: \"end\",\n                        height: 80,\n                        fontSize: 12,\n                        interval: 0\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                        label: {\n                            value: 'Number of Federations',\n                            angle: -90,\n                            position: 'insideLeft'\n                        },\n                        fontSize: 12\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        formatter: (value, name, props)=>[\n                                \"\".concat(value, \" federations (\").concat(props.payload.percentage.toFixed(1), \"%)\"),\n                                'Responses'\n                            ],\n                        labelFormatter: (label, payload)=>{\n                            var _payload__payload, _payload_;\n                            return (payload === null || payload === void 0 ? void 0 : (_payload_ = payload[0]) === null || _payload_ === void 0 ? void 0 : (_payload__payload = _payload_.payload) === null || _payload__payload === void 0 ? void 0 : _payload__payload.fullAnswer) || label;\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                        dataKey: \"count\",\n                        fill: \"#0088FE\",\n                        radius: [\n                            4,\n                            4,\n                            0,\n                            0\n                        ]\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPieChart = (questionData)=>{\n        const data = questionData.responses.map((response, index)=>({\n                name: response.answer.length > 15 ? response.answer.substring(0, 15) + '...' : response.answer,\n                fullName: response.answer,\n                value: response.count,\n                percentage: response.percentage\n            }));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.PieChart, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Pie, {\n                        data: data,\n                        cx: \"50%\",\n                        cy: \"50%\",\n                        labelLine: false,\n                        label: (param)=>{\n                            let { percentage } = param;\n                            return \"\".concat(percentage.toFixed(1), \"%\");\n                        },\n                        outerRadius: 80,\n                        fill: \"#8884d8\",\n                        dataKey: \"value\",\n                        children: data.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Cell, {\n                                fill: COLORS[index % COLORS.length]\n                            }, \"cell-\".concat(index), false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        formatter: (value, name, props)=>[\n                                \"\".concat(value, \" federations (\").concat(props.payload.percentage.toFixed(1), \"%)\"),\n                                props.payload.fullName\n                            ]\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Legend, {\n                        formatter: (value, entry)=>entry.payload.fullName,\n                        wrapperStyle: {\n                            fontSize: '12px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                \"Q&A Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"Loading question and answer statistics...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            1,\n                            2,\n                            3\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-20 bg-muted rounded-md animate-pulse\"\n                            }, i, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    const currentSectionData = qaStats.find((section)=>section.sectionNumber === selectedSection);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-xl flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Q&A Statistics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"Analysis of how federations answered assessment questions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                value: chartType,\n                                onValueChange: (value)=>setChartType(value),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid grid-cols-2 w-32\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"bar\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Bar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"pie\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pie\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium mb-3 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Select Assessment Section\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-2\",\n                                children: Object.entries(sectionTitles).map((param)=>{\n                                    let [sectionNum, title] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: selectedSection === parseInt(sectionNum) ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedSection(parseInt(sectionNum)),\n                                        className: \"text-xs\",\n                                        children: sectionNum\n                                    }, sectionNum, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    currentSectionData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Section \",\n                                            selectedSection,\n                                            \": \",\n                                            currentSectionData.sectionTitle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: expandAllQuestions,\n                                                        className: \"text-xs\",\n                                                        children: \"Expand All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: collapseAllQuestions,\n                                                        className: \"text-xs\",\n                                                        children: \"Collapse All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                children: [\n                                                    currentSectionData.questions.length,\n                                                    \" questions\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: currentSectionData.questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-l-4 border-l-primary/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: [\n                                                                        \"Q\",\n                                                                        index + 1,\n                                                                        \": \",\n                                                                        question.question\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                    className: \"flex items-center gap-4 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 314,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                question.totalResponses,\n                                                                                \" responses\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 318,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                question.responses.length,\n                                                                                \" unique answers\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>toggleQuestionExpansion(question.field),\n                                                            children: expandedQuestions.has(question.field) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this),\n                                            expandedQuestions.has(question.field) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: question.responses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: chartType === 'bar' ? renderBarChart(question) : renderPieChart(question)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\",\n                                                            children: question.responses.map((response, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-lg border bg-muted/20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-3 h-3 rounded-full\",\n                                                                                    style: {\n                                                                                        backgroundColor: COLORS[idx % COLORS.length]\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 354,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        response.count,\n                                                                                        \" (\",\n                                                                                        response.percentage.toFixed(1),\n                                                                                        \"%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 358,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: response.answer\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                                            children: [\n                                                                                \"Federations: \",\n                                                                                response.federations.join(', ')\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"No responses available for this question\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, question.field, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No Data Available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"No statistics available for Section \",\n                                    selectedSection\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(QAStatsCard, \"WPXcFe8wDyl3krN0KffCtb+K0ro=\");\n_c = QAStatsCard;\nvar _c;\n$RefreshReg$(_c, \"QAStatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/QAStatsCard.tsx\n"));

/***/ })

});