"use client";

import { useState, useEffect, useMemo } from 'react';
import { DashboardProvider } from "@/components/dashboard/DashboardProvider";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { UserNav } from "@/components/UserNav";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft, Building2, UserCircle, Search, Filter, Download,
  TrendingUp, TrendingDown, BarChart3, PieChart, Users,
  Calendar, Award, Target, RefreshCw, Eye, Settings,
  ChevronDown, ChevronUp, SortAsc, SortDesc, LayoutDashboard
} from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Federation } from "@/lib/types";
import { useAuth } from "@/components/AuthContext";
import FederationAnalyticsChart from "@/components/dashboard/FederationAnalyticsChart";
import SectionAverageChart from "@/components/dashboard/SectionAverageChart";
import YouthRepresentationChart from "@/components/dashboard/YouthRepresentationChart";
import { QAStatsCard } from "@/components/dashboard/QAStatsCard";
import { toast } from "sonner";

export default function AllFederationPage() {
  const { user } = useAuth();
  const [federations, setFederations] = useState<Partial<Federation>[]>([]);
  const [selectedFederation, setSelectedFederation] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "score" | "percentage" | "youth">("score");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [filterBy, setFilterBy] = useState<"all" | "high" | "medium" | "low">("all");
  const [activeTab, setActiveTab] = useState("overview");
  const [refreshing, setRefreshing] = useState(false);

  // Calculated averages
  const [averagePresidentAge, setAveragePresidentAge] = useState<number>(0);
  const [averageSecretaryAge, setAverageSecretaryAge] = useState<number>(0);
  // Score analytics
  const [averageTotalScore, setAverageTotalScore] = useState<number>(0);
  const [averagePercentage, setAveragePercentage] = useState<number>(0);
  const [highestScore, setHighestScore] = useState<number>(0);
  const [lowestScore, setLowestScore] = useState<number>(0);
  const [highestPercentage, setHighestPercentage] = useState<number>(0);
  const [lowestPercentage, setLowestPercentage] = useState<number>(0);
  // Youth and section analytics
  const [averageYouthPercentage, setAverageYouthPercentage] = useState<number>(0);
  const [avgSectionScores, setAvgSectionScores] = useState<{ [section: number]: number }>({});
  const [minSectionScores, setMinSectionScores] = useState<{ [section: number]: number }>({});
  const [maxSectionScores, setMaxSectionScores] = useState<{ [section: number]: number }>({});
  const [sectionList, setSectionList] = useState<{ id: number; title: string }[]>([]);
  // Selected federation details
  const [currentFederation, setCurrentFederation] = useState<Partial<Federation> | null>(null);
  const [currentSectionScores, setCurrentSectionScores] = useState<{ [section: number]: number }>({});

  // Filtered and sorted federations
  const filteredAndSortedFederations = useMemo(() => {
    let filtered = federations.filter(fed => {
      const matchesSearch = !searchTerm ||
        fed.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fed.president_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fed.secretary_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const percentage = fed.percentage || 0;
      const matchesFilter = filterBy === "all" ||
        (filterBy === "high" && percentage >= 75) ||
        (filterBy === "medium" && percentage >= 50 && percentage < 75) ||
        (filterBy === "low" && percentage < 50);

      return matchesSearch && matchesFilter;
    });

    // Sort the filtered results
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case "name":
          aValue = a.name || "";
          bValue = b.name || "";
          break;
        case "score":
          aValue = a.totalScore || 0;
          bValue = b.totalScore || 0;
          break;
        case "percentage":
          aValue = a.percentage || 0;
          bValue = b.percentage || 0;
          break;
        case "youth":
          aValue = getYouthPercentageValue(a.youth_percentage || "0-25%");
          bValue = getYouthPercentageValue(b.youth_percentage || "0-25%");
          break;
        default:
          return 0;
      }

      if (typeof aValue === "string") {
        return sortOrder === "asc" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      } else {
        return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
      }
    });

    return filtered;
  }, [federations, searchTerm, sortBy, sortOrder, filterBy]);

  // Helper function to convert youth percentage to numeric value
  const getYouthPercentageValue = (youthPercentage: string): number => {
    switch (youthPercentage) {
      case "above-75%": return 87.5;
      case "51-75%": return 63;
      case "26-50%": return 38;
      case "0-25%": return 12.5;
      default: return 0;
    }
  };

  // Section titles (should match backend section numbers 3-11)
  // Section max scores (from useAssessment.ts)
  const sectionMaxScores: { [section: number]: number } = {
    3: 14,
    4: 4,
    5: 11,
    6: 9,
    7: 7,
    8: 8,
    9: 11,
    10: 16,
    11: 10,
  };

  useEffect(() => {
    setSectionList([
      { id: 3, title: "Leadership" },
      { id: 4, title: "Organizational Structure" },
      { id: 5, title: "Management" },
      { id: 6, title: "Worker Participation" },
      { id: 7, title: "Culture and Gender" },
      { id: 8, title: "Collective Bargaining" },
      { id: 9, title: "Member Engagement" },
      { id: 10, title: "Financial Stability" },
      { id: 11, title: "Audit & Compliance" },
    ]);
  }, []);
  // Fetch all federations
  const fetchFederations = async (showRefreshToast = false) => {
    if (showRefreshToast) setRefreshing(true);

    try {
      // This would be replaced with your actual API endpoint
      const response = await fetch('/api/federations');
      if (!response.ok) throw new Error('Failed to fetch federations');

      const data = await response.json();
      setFederations(data.federations || []);

      // Calculate averages
      if ((data.federations || []).length > 0) {
        const presidentAges = data.federations.filter((fed: Partial<Federation>) => fed.president_age).map((fed: Partial<Federation>) => fed.president_age as number);
        const secretaryAges = data.federations.filter((fed: Partial<Federation>) => fed.secretary_age).map((fed: Partial<Federation>) => fed.secretary_age as number);
        const avgPresidentAge = presidentAges.length > 0
          ? Math.round(presidentAges.reduce((sum: number, age: number) => sum + age, 0) / presidentAges.length)
          : 0;
        const avgSecretaryAge = secretaryAges.length > 0
          ? Math.round(secretaryAges.reduce((sum: number, age: number) => sum + age, 0) / secretaryAges.length)
          : 0;
        setAveragePresidentAge(avgPresidentAge);
        setAverageSecretaryAge(avgSecretaryAge);
      }

      // Set score analytics
      setAverageTotalScore(data.avgTotalScore || 0);
      setAveragePercentage(data.avgPercentage || 0);
      setHighestScore(data.highestScore || 0);
      setLowestScore(data.lowestScore || 0);
      setHighestPercentage(data.highestPercentage || 0);
      setLowestPercentage(data.lowestPercentage || 0);
      setAverageYouthPercentage(data.avgYouthPercentage || 0);
      setAvgSectionScores(data.avgSectionScores || {});
      setMinSectionScores(data.minSectionScores || {});
      setMaxSectionScores(data.maxSectionScores || {});

      if (showRefreshToast) {
        toast.success("Data refreshed successfully!");
      }
    } catch (error) {
      console.error('Error fetching federations:', error);
      if (showRefreshToast) {
        toast.error("Failed to refresh data");
      }
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchFederations();
  }, []);

  // Handle federation selection change
  const handleFederationChange = (value: string) => {
    setSelectedFederation(value);

    if (value === 'all') {
      setCurrentFederation(null);
      setCurrentSectionScores({});
    } else {
      const selected = federations.find(fed => fed.id === value);
      setCurrentFederation(selected || null);
      setCurrentSectionScores(selected?.sectionScores || {});
    }
  };

  // Export functionality
  const exportToCSV = () => {
    const headers = ["Name", "President", "Secretary", "Total Score", "Percentage", "Youth %"];
    const csvData = filteredAndSortedFederations.map(fed => [
      fed.name || "",
      fed.president_name || "",
      fed.secretary_name || "",
      fed.totalScore || 0,
      fed.percentage || 0,
      fed.youth_percentage || ""
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `federations-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Data exported successfully!");
  };

  // Performance metrics
  const performanceMetrics = useMemo(() => {
    const total = federations.length;
    const highPerformers = federations.filter(f => (f.percentage || 0) >= 75).length;
    const mediumPerformers = federations.filter(f => (f.percentage || 0) >= 50 && (f.percentage || 0) < 75).length;
    const lowPerformers = federations.filter(f => (f.percentage || 0) < 50).length;

    return {
      total,
      highPerformers,
      mediumPerformers,
      lowPerformers,
      highPercentage: total > 0 ? Math.round((highPerformers / total) * 100) : 0,
      mediumPercentage: total > 0 ? Math.round((mediumPerformers / total) * 100) : 0,
      lowPercentage: total > 0 ? Math.round((lowPerformers / total) * 100) : 0
    };
  }, [federations]);

  return (
    <div className="flex min-h-screen flex-col bg-background/50">
      <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="w-full px-4 md:px-6 flex h-16 items-center py-4">
          <div className="flex items-center gap-3">
            <Link href="/assessment">
              <Button variant="ghost" size="icon" className="rounded-full">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="flex flex-col">
              <h1 className="text-xl font-bold flex items-center gap-2">
                <LayoutDashboard className="h-5 w-5 text-primary" />
                Admin Dashboard
              </h1>
              <p className="text-sm text-muted-foreground">All Federations Overview</p>
            </div>
          </div>
          <div className="ml-auto flex items-center gap-2 sm:gap-4">
            <Button 
              onClick={() => fetchFederations(true)} 
              disabled={refreshing}
              variant="outline"
              size="sm"
              className="mr-2"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={exportToCSV} variant="outline" size="sm" className="mr-2">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
            <ThemeToggle />
            <UserNav />
          </div>
        </div>
      </header>

      <main className="flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <TabsList className="grid w-full grid-cols-5 md:w-auto">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="qa-stats">Q&A Stats</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="federations">Federations</TabsTrigger>
            </TabsList>

            <div className="flex gap-2 flex-wrap">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search federations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-[200px]"
                />
              </div>
              
              <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
                <SelectTrigger className="w-[130px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Scores</SelectItem>
                  <SelectItem value="high">High (75%+)</SelectItem>
                  <SelectItem value="medium">Medium (50-74%)</SelectItem>
                  <SelectItem value="low">Low (&lt;50%)</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="score">Score</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="youth">Youth %</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              >
                {sortOrder === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <TabsContent value="overview" className="mt-0">
            <div className="space-y-6 animate-in fade-in duration-300">
              {/* Title and Logos Section */}
              <div className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-6">
                <h1 className="text-2xl font-bold mb-4">Support for Effective and Inclusive Trade Unions in Bangladesh</h1>
                <div className="flex justify-center gap-8 flex-wrap">
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png" alt="German Cooperation Bangladesh" className="h-16 w-auto" />
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png" alt="International Labour Organization" className="h-16 w-auto" />
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png" alt="Growing Together OPC" className="h-16 w-auto" />
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png" alt="Government of the people's republic of Bangladesh" className="h-16 w-auto" />
                </div>
              </div>

              {/* Quick Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Award className="h-5 w-5 text-yellow-500" />
                      Average Performance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-primary">{averagePercentage}%</div>
                    <p className="text-sm text-muted-foreground">Overall assessment score</p>
                    <div className="mt-2 flex items-center gap-2">
                      <Badge variant={averagePercentage >= 75 ? "default" : averagePercentage >= 50 ? "secondary" : "destructive"}>
                        {averagePercentage >= 75 ? "Excellent" : averagePercentage >= 50 ? "Good" : "Needs Improvement"}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Users className="h-5 w-5 text-green-500" />
                      Youth Engagement
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-green-600">{averageYouthPercentage}%</div>
                    <p className="text-sm text-muted-foreground">Average youth representation</p>
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(averageYouthPercentage, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Target className="h-5 w-5 text-blue-500" />
                      Score Range
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">Highest:</span>
                        <span className="font-semibold text-green-600">{highestPercentage}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Lowest:</span>
                        <span className="font-semibold text-red-600">{lowestPercentage}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Range:</span>
                        <span className="font-semibold">{highestPercentage - lowestPercentage}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="mt-0">
            <div className="space-y-8 animate-in fade-in duration-300">
              <FederationAnalyticsChart
                federations={federations}
                selectedFederation={selectedFederation}
                onSelectFederation={handleFederationChange}
              />

              <SectionAverageChart
                avgSectionScores={avgSectionScores}
                minSectionScores={minSectionScores}
                maxSectionScores={maxSectionScores}
                sectionList={sectionList}
                sectionMaxScores={sectionMaxScores}
              />
            </div>
          </TabsContent>

          <TabsContent value="qa-stats" className="mt-0">
            <div className="space-y-8 animate-in fade-in duration-300">
              <QAStatsCard />
            </div>
          </TabsContent>

          <TabsContent value="reports" className="mt-0">
            <div className="space-y-8 animate-in fade-in duration-300">
              <YouthRepresentationChart federations={federations} />
            </div>
          </TabsContent>

          <TabsContent value="federations" className="mt-0">
            <div className="animate-in fade-in duration-300">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Federation Directory
                  </CardTitle>
                  <CardDescription>
                    Interactive table showing all federations with their performance metrics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3, 4, 5].map((i) => (
                        <div key={i} className="flex items-center space-x-4 animate-pulse">
                          <div className="h-12 w-12 bg-muted rounded-full"></div>
                          <div className="space-y-2 flex-1">
                            <div className="h-4 bg-muted rounded w-1/4"></div>
                            <div className="h-3 bg-muted rounded w-1/2"></div>
                          </div>
                          <div className="h-8 w-16 bg-muted rounded"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredAndSortedFederations.map((federation, index) => (
                        <Link href={`/federation/${federation.id}`} key={federation.id}>
                          <div
                            className="border rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer animate-in fade-in slide-in-from-bottom-2"
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                                <Building2 className="h-6 w-6 text-primary" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-lg">{federation.name}</h3>
                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                  <span>President: {federation.president_name || "N/A"}</span>
                                  <span>Secretary: {federation.secretary_name || "N/A"}</span>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-4">
                              <div className="text-right">
                                <div className="text-2xl font-bold text-primary">
                                  {federation.percentage || 0}%
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {federation.totalScore || 0}/{federation.maxScore || 0}
                                </div>
                              </div>

                              <Badge
                                variant={
                                  (federation.percentage || 0) >= 75 ? "default" :
                                  (federation.percentage || 0) >= 50 ? "secondary" : "destructive"
                                }
                                className="ml-2"
                              >
                                {(federation.percentage || 0) >= 75 ? "High" :
                                 (federation.percentage || 0) >= 50 ? "Medium" : "Low"}
                              </Badge>

                              <div className="text-center">
                                <div className="text-sm font-medium text-green-600">
                                  {federation.youth_percentage || "N/A"}
                                </div>
                                <div className="text-xs text-muted-foreground">Youth</div>
                              </div>

                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Progress bar */}
                          <div className="mt-3">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-500 ${
                                  (federation.percentage || 0) >= 75 ? 'bg-green-500' :
                                  (federation.percentage || 0) >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${Math.min(federation.percentage || 0, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                          </div>
                        </Link>
                      ))}

                      {filteredAndSortedFederations.length === 0 && (
                        <div className="text-center py-12">
                          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No federations found</h3>
                          <p className="text-muted-foreground">
                            Try adjusting your search or filter criteria
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {isLoading ? (
          <div className="grid gap-6 md:grid-cols-2 mt-8">
            <Card className="shadow-md animate-pulse">
              <CardHeader>
                <CardTitle>Loading...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-20 bg-muted rounded"></div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 mt-8">
            <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
              <CardHeader className="pb-2 bg-muted/10">
                <CardTitle className="text-xl flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-primary" />
                  Leadership Overview
                </CardTitle>
                <CardDescription>
                  Average leadership demographics across all federations
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-blue-500/10 p-2.5 rounded-full">
                      <UserCircle className="h-5 w-5 text-blue-500" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-sm text-muted-foreground">Average Ages</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-1">
                        <div className="bg-muted/30 p-2 rounded-md">
                          <p className="text-xs text-muted-foreground">President</p>
                          <p className="font-medium">
                            {averagePresidentAge} years
                          </p>
                        </div>
                        <div className="bg-muted/30 p-2 rounded-md">
                          <p className="text-xs text-muted-foreground">Secretary</p>
                          <p className="font-medium">
                            {averageSecretaryAge} years
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
              <CardHeader className="pb-2 bg-muted/10">
                <CardTitle className="text-xl flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  Performance Summary
                </CardTitle>
                <CardDescription>
                  Key performance indicators across all federations
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{averageTotalScore}</div>
                      <div className="text-xs text-muted-foreground">Avg Score</div>
                    </div>
                    <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{averagePercentage}%</div>
                      <div className="text-xs text-muted-foreground">Avg Percentage</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Highest Score:</span>
                      <span className="font-semibold text-green-600">{highestScore} ({highestPercentage}%)</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Lowest Score:</span>
                      <span className="font-semibold text-red-600">{lowestScore} ({lowestPercentage}%)</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Youth Engagement:</span>
                      <span className="font-semibold text-purple-600">{averageYouthPercentage}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </main>
    </div>
  );
}