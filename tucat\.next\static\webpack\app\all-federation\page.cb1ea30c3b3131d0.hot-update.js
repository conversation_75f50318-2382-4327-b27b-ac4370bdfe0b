"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/all-federation/page",{

/***/ "(app-pages-browser)/./app/all-federation/page.tsx":
/*!*************************************!*\
  !*** ./app/all-federation/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AllFederationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_UserNav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/UserNav */ \"(app-pages-browser)/./components/UserNav.tsx\");\n/* harmony import */ var _components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/theme-toggle */ \"(app-pages-browser)/./components/ui/theme-toggle.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-down-wide-narrow.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,BarChart3,Building2,Download,Eye,Filter,LayoutDashboard,RefreshCw,Search,SortAsc,SortDesc,Target,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_AuthContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AuthContext */ \"(app-pages-browser)/./components/AuthContext.tsx\");\n/* harmony import */ var _components_dashboard_FederationAnalyticsChart__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/dashboard/FederationAnalyticsChart */ \"(app-pages-browser)/./components/dashboard/FederationAnalyticsChart.tsx\");\n/* harmony import */ var _components_dashboard_SectionAverageChart__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/dashboard/SectionAverageChart */ \"(app-pages-browser)/./components/dashboard/SectionAverageChart.tsx\");\n/* harmony import */ var _components_dashboard_YouthRepresentationChart__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/dashboard/YouthRepresentationChart */ \"(app-pages-browser)/./components/dashboard/YouthRepresentationChart.tsx\");\n/* harmony import */ var _components_dashboard_QAStatsCard__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/dashboard/QAStatsCard */ \"(app-pages-browser)/./components/dashboard/QAStatsCard.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AllFederationPage() {\n    _s();\n    const { user } = (0,_components_AuthContext__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const [federations, setFederations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedFederation, setSelectedFederation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"score\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"desc\");\n    const [filterBy, setFilterBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Calculated averages\n    const [averagePresidentAge, setAveragePresidentAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [averageSecretaryAge, setAverageSecretaryAge] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Score analytics\n    const [averageTotalScore, setAverageTotalScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [averagePercentage, setAveragePercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [highestScore, setHighestScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lowestScore, setLowestScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [highestPercentage, setHighestPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [lowestPercentage, setLowestPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Youth and section analytics\n    const [averageYouthPercentage, setAverageYouthPercentage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [avgSectionScores, setAvgSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [minSectionScores, setMinSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [maxSectionScores, setMaxSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [sectionList, setSectionList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Selected federation details\n    const [currentFederation, setCurrentFederation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentSectionScores, setCurrentSectionScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Filtered and sorted federations\n    const filteredAndSortedFederations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AllFederationPage.useMemo[filteredAndSortedFederations]\": ()=>{\n            let filtered = federations.filter({\n                \"AllFederationPage.useMemo[filteredAndSortedFederations].filtered\": (fed)=>{\n                    var _fed_name, _fed_president_name, _fed_secretary_name;\n                    const matchesSearch = !searchTerm || ((_fed_name = fed.name) === null || _fed_name === void 0 ? void 0 : _fed_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_fed_president_name = fed.president_name) === null || _fed_president_name === void 0 ? void 0 : _fed_president_name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_fed_secretary_name = fed.secretary_name) === null || _fed_secretary_name === void 0 ? void 0 : _fed_secretary_name.toLowerCase().includes(searchTerm.toLowerCase()));\n                    const percentage = fed.percentage || 0;\n                    const matchesFilter = filterBy === \"all\" || filterBy === \"high\" && percentage >= 75 || filterBy === \"medium\" && percentage >= 50 && percentage < 75 || filterBy === \"low\" && percentage < 50;\n                    return matchesSearch && matchesFilter;\n                }\n            }[\"AllFederationPage.useMemo[filteredAndSortedFederations].filtered\"]);\n            // Sort the filtered results\n            filtered.sort({\n                \"AllFederationPage.useMemo[filteredAndSortedFederations]\": (a, b)=>{\n                    let aValue, bValue;\n                    switch(sortBy){\n                        case \"name\":\n                            aValue = a.name || \"\";\n                            bValue = b.name || \"\";\n                            break;\n                        case \"score\":\n                            aValue = a.totalScore || 0;\n                            bValue = b.totalScore || 0;\n                            break;\n                        case \"percentage\":\n                            aValue = a.percentage || 0;\n                            bValue = b.percentage || 0;\n                            break;\n                        case \"youth\":\n                            aValue = getYouthPercentageValue(a.youth_percentage || \"0-25%\");\n                            bValue = getYouthPercentageValue(b.youth_percentage || \"0-25%\");\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (typeof aValue === \"string\") {\n                        return sortOrder === \"asc\" ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);\n                    } else {\n                        return sortOrder === \"asc\" ? aValue - bValue : bValue - aValue;\n                    }\n                }\n            }[\"AllFederationPage.useMemo[filteredAndSortedFederations]\"]);\n            return filtered;\n        }\n    }[\"AllFederationPage.useMemo[filteredAndSortedFederations]\"], [\n        federations,\n        searchTerm,\n        sortBy,\n        sortOrder,\n        filterBy\n    ]);\n    // Helper function to convert youth percentage to numeric value\n    const getYouthPercentageValue = (youthPercentage)=>{\n        switch(youthPercentage){\n            case \"above-75%\":\n                return 87.5;\n            case \"51-75%\":\n                return 63;\n            case \"26-50%\":\n                return 38;\n            case \"0-25%\":\n                return 12.5;\n            default:\n                return 0;\n        }\n    };\n    // Section titles (should match backend section numbers 3-11)\n    // Section max scores (from useAssessment.ts)\n    const sectionMaxScores = {\n        3: 14,\n        4: 4,\n        5: 11,\n        6: 9,\n        7: 7,\n        8: 8,\n        9: 11,\n        10: 16,\n        11: 10\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AllFederationPage.useEffect\": ()=>{\n            setSectionList([\n                {\n                    id: 3,\n                    title: \"Leadership\"\n                },\n                {\n                    id: 4,\n                    title: \"Organizational Structure\"\n                },\n                {\n                    id: 5,\n                    title: \"Management\"\n                },\n                {\n                    id: 6,\n                    title: \"Worker Participation\"\n                },\n                {\n                    id: 7,\n                    title: \"Culture and Gender\"\n                },\n                {\n                    id: 8,\n                    title: \"Collective Bargaining\"\n                },\n                {\n                    id: 9,\n                    title: \"Member Engagement\"\n                },\n                {\n                    id: 10,\n                    title: \"Financial Stability\"\n                },\n                {\n                    id: 11,\n                    title: \"Audit & Compliance\"\n                }\n            ]);\n        }\n    }[\"AllFederationPage.useEffect\"], []);\n    // Fetch all federations\n    const fetchFederations = async function() {\n        let showRefreshToast = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (showRefreshToast) setRefreshing(true);\n        try {\n            // This would be replaced with your actual API endpoint\n            const response = await fetch('/api/federations');\n            if (!response.ok) throw new Error('Failed to fetch federations');\n            const data = await response.json();\n            setFederations(data.federations || []);\n            // Calculate averages\n            if ((data.federations || []).length > 0) {\n                const presidentAges = data.federations.filter((fed)=>fed.president_age).map((fed)=>fed.president_age);\n                const secretaryAges = data.federations.filter((fed)=>fed.secretary_age).map((fed)=>fed.secretary_age);\n                const avgPresidentAge = presidentAges.length > 0 ? Math.round(presidentAges.reduce((sum, age)=>sum + age, 0) / presidentAges.length) : 0;\n                const avgSecretaryAge = secretaryAges.length > 0 ? Math.round(secretaryAges.reduce((sum, age)=>sum + age, 0) / secretaryAges.length) : 0;\n                setAveragePresidentAge(avgPresidentAge);\n                setAverageSecretaryAge(avgSecretaryAge);\n            }\n            // Set score analytics\n            setAverageTotalScore(data.avgTotalScore || 0);\n            setAveragePercentage(data.avgPercentage || 0);\n            setHighestScore(data.highestScore || 0);\n            setLowestScore(data.lowestScore || 0);\n            setHighestPercentage(data.highestPercentage || 0);\n            setLowestPercentage(data.lowestPercentage || 0);\n            setAverageYouthPercentage(data.avgYouthPercentage || 0);\n            setAvgSectionScores(data.avgSectionScores || {});\n            setMinSectionScores(data.minSectionScores || {});\n            setMaxSectionScores(data.maxSectionScores || {});\n            if (showRefreshToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Data refreshed successfully!\");\n            }\n        } catch (error) {\n            console.error('Error fetching federations:', error);\n            if (showRefreshToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_16__.toast.error(\"Failed to refresh data\");\n            }\n        } finally{\n            setIsLoading(false);\n            setRefreshing(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AllFederationPage.useEffect\": ()=>{\n            fetchFederations();\n        }\n    }[\"AllFederationPage.useEffect\"], []);\n    // Handle federation selection change\n    const handleFederationChange = (value)=>{\n        setSelectedFederation(value);\n        if (value === 'all') {\n            setCurrentFederation(null);\n            setCurrentSectionScores({});\n        } else {\n            const selected = federations.find((fed)=>fed.id === value);\n            setCurrentFederation(selected || null);\n            setCurrentSectionScores((selected === null || selected === void 0 ? void 0 : selected.sectionScores) || {});\n        }\n    };\n    // Export functionality\n    const exportToCSV = ()=>{\n        const headers = [\n            \"Name\",\n            \"President\",\n            \"Secretary\",\n            \"Total Score\",\n            \"Percentage\",\n            \"Youth %\"\n        ];\n        const csvData = filteredAndSortedFederations.map((fed)=>[\n                fed.name || \"\",\n                fed.president_name || \"\",\n                fed.secretary_name || \"\",\n                fed.totalScore || 0,\n                fed.percentage || 0,\n                fed.youth_percentage || \"\"\n            ]);\n        const csvContent = [\n            headers,\n            ...csvData\n        ].map((row)=>row.map((cell)=>'\"'.concat(cell, '\"')).join(\",\")).join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"federations-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        a.click();\n        URL.revokeObjectURL(url);\n        sonner__WEBPACK_IMPORTED_MODULE_16__.toast.success(\"Data exported successfully!\");\n    };\n    // Performance metrics\n    const performanceMetrics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AllFederationPage.useMemo[performanceMetrics]\": ()=>{\n            const total = federations.length;\n            const highPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) >= 75\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            const mediumPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) >= 50 && (f.percentage || 0) < 75\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            const lowPerformers = federations.filter({\n                \"AllFederationPage.useMemo[performanceMetrics]\": (f)=>(f.percentage || 0) < 50\n            }[\"AllFederationPage.useMemo[performanceMetrics]\"]).length;\n            return {\n                total,\n                highPerformers,\n                mediumPerformers,\n                lowPerformers,\n                highPercentage: total > 0 ? Math.round(highPerformers / total * 100) : 0,\n                mediumPercentage: total > 0 ? Math.round(mediumPerformers / total * 100) : 0,\n                lowPercentage: total > 0 ? Math.round(lowPerformers / total * 100) : 0\n            };\n        }\n    }[\"AllFederationPage.useMemo[performanceMetrics]\"], [\n        federations\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col bg-background/50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 md:px-6 flex h-16 items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    href: \"/assessment\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Admin Dashboard\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"All Federations Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-auto flex items-center gap-2 sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: ()=>fetchFederations(true),\n                                    disabled: refreshing,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mr-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(refreshing ? 'animate-spin' : '')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: exportToCSV,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    className: \"mr-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export CSV\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_theme_toggle__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UserNav__WEBPACK_IMPORTED_MODULE_3__.UserNav, {}, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                        className: \"grid w-full grid-cols-5 md:w-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"overview\",\n                                                children: \"Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"analytics\",\n                                                children: \"Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"qa-stats\",\n                                                children: \"Q&A Stats\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"reports\",\n                                                children: \"Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                value: \"federations\",\n                                                children: \"Federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 flex-wrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        placeholder: \"Search federations...\",\n                                                        value: searchTerm,\n                                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                                        className: \"pl-8 w-[200px]\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                value: filterBy,\n                                                onValueChange: (value)=>setFilterBy(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                        className: \"w-[130px]\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"all\",\n                                                                children: \"All Scores\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"high\",\n                                                                children: \"High (75%+)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"medium\",\n                                                                children: \"Medium (50-74%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"low\",\n                                                                children: \"Low (<50%)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                                value: sortBy,\n                                                onValueChange: (value)=>setSortBy(value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                        className: \"w-[120px]\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {}, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"score\",\n                                                                children: \"Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"name\",\n                                                                children: \"Name\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"percentage\",\n                                                                children: \"Percentage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                                value: \"youth\",\n                                                                children: \"Youth %\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setSortOrder(sortOrder === \"asc\" ? \"desc\" : \"asc\"),\n                                                children: sortOrder === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 40\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 74\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"overview\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 animate-in fade-in duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold mb-4\",\n                                                    children: \"Support for Effective and Inclusive Trade Unions in Bangladesh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center gap-8 flex-wrap\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png\",\n                                                            alt: \"German Cooperation Bangladesh\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png\",\n                                                            alt: \"International Labour Organization\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png\",\n                                                            alt: \"Growing Together OPC\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png\",\n                                                            alt: \"Government of the people's republic of Bangladesh\",\n                                                            className: \"h-16 w-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Average Performance\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-primary\",\n                                                                    children: [\n                                                                        averagePercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Overall assessment score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                        variant: averagePercentage >= 75 ? \"default\" : averagePercentage >= 50 ? \"secondary\" : \"destructive\",\n                                                                        children: averagePercentage >= 75 ? \"Excellent\" : averagePercentage >= 50 ? \"Good\" : \"Needs Improvement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Youth Engagement\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-green-600\",\n                                                                    children: [\n                                                                        averageYouthPercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Average youth representation\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-green-500 h-2 rounded-full transition-all duration-300\",\n                                                                            style: {\n                                                                                width: \"\".concat(Math.min(averageYouthPercentage, 100), \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 405,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                            className: \"pb-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 417,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Score Range\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Highest:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-green-600\",\n                                                                                children: [\n                                                                                    highestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Lowest:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold text-red-600\",\n                                                                                children: [\n                                                                                    lowestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: \"Range:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: [\n                                                                                    highestPercentage - lowestPercentage,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"analytics\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_FederationAnalyticsChart__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            federations: federations,\n                                            selectedFederation: selectedFederation,\n                                            onSelectFederation: handleFederationChange\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_SectionAverageChart__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            avgSectionScores: avgSectionScores,\n                                            minSectionScores: minSectionScores,\n                                            maxSectionScores: maxSectionScores,\n                                            sectionList: sectionList,\n                                            sectionMaxScores: sectionMaxScores\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"qa-stats\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_QAStatsCard__WEBPACK_IMPORTED_MODULE_15__.QAStatsCard, {}, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"reports\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_YouthRepresentationChart__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        federations: federations\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                value: \"federations\",\n                                className: \"mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-in fade-in duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Federation Directory\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                        children: \"Interactive table showing all federations with their performance metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 animate-pulse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-12 w-12 bg-muted rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-4 bg-muted rounded w-1/4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-muted rounded w-1/2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-8 w-16 bg-muted rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        filteredAndSortedFederations.map((federation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                                href: \"/federation/\".concat(federation.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer animate-in fade-in slide-in-from-bottom-2\",\n                                                                    style: {\n                                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                className: \"h-6 w-6 text-primary\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                lineNumber: 509,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 508,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                                    className: \"font-semibold text-lg\",\n                                                                                                    children: federation.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 512,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex items-center gap-4 text-sm text-muted-foreground\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"President: \",\n                                                                                                                federation.president_name || \"N/A\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                            lineNumber: 514,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: [\n                                                                                                                \"Secretary: \",\n                                                                                                                federation.secretary_name || \"N/A\"\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                            lineNumber: 515,\n                                                                                                            columnNumber: 35\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 513,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 511,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 507,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-4\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-right\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-2xl font-bold text-primary\",\n                                                                                                    children: [\n                                                                                                        federation.percentage || 0,\n                                                                                                        \"%\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 522,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                                    children: [\n                                                                                                        federation.totalScore || 0,\n                                                                                                        \"/\",\n                                                                                                        federation.maxScore || 0\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 525,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 521,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                                            variant: (federation.percentage || 0) >= 75 ? \"default\" : (federation.percentage || 0) >= 50 ? \"secondary\" : \"destructive\",\n                                                                                            className: \"ml-2\",\n                                                                                            children: (federation.percentage || 0) >= 75 ? \"High\" : (federation.percentage || 0) >= 50 ? \"Medium\" : \"Low\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 530,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-center\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-sm font-medium text-green-600\",\n                                                                                                    children: federation.youth_percentage || \"N/A\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 542,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                                    children: \"Youth\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                    lineNumber: 545,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 541,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                                lineNumber: 549,\n                                                                                                columnNumber: 33\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                            lineNumber: 548,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 520,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-3\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 rounded-full transition-all duration-500 \".concat((federation.percentage || 0) >= 75 ? 'bg-green-500' : (federation.percentage || 0) >= 50 ? 'bg-yellow-500' : 'bg-red-500'),\n                                                                                    style: {\n                                                                                        width: \"\".concat(Math.min(federation.percentage || 0, 100), \"%\")\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 556,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, federation.id, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        filteredAndSortedFederations.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold mb-2\",\n                                                                    children: \"No federations found\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-muted-foreground\",\n                                                                    children: \"Try adjusting your search or filter criteria\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"shadow-md animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Loading...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-20 bg-muted rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2 mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-2 bg-muted/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Leadership Overview\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Average leadership demographics across all federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-500/10 p-2.5 rounded-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-muted-foreground\",\n                                                                children: \"Average Ages\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 617,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-muted/30 p-2 rounded-md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"President\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    averagePresidentAge,\n                                                                                    \" years\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 621,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-muted/30 p-2 rounded-md\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground\",\n                                                                                children: \"Secretary\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 626,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"font-medium\",\n                                                                                children: [\n                                                                                    averageSecretaryAge,\n                                                                                    \" years\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                                lineNumber: 627,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                className: \"shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                        className: \"pb-2 bg-muted/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-xl flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_BarChart3_Building2_Download_Eye_Filter_LayoutDashboard_RefreshCw_Search_SortAsc_SortDesc_Target_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                        lineNumber: 641,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Performance Summary\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                children: \"Key performance indicators across all federations\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                lineNumber: 644,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 639,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-green-600\",\n                                                                    children: averageTotalScore\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Avg Score\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                                    children: [\n                                                                        averagePercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-muted-foreground\",\n                                                                    children: \"Avg Percentage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Highest Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-green-600\",\n                                                                    children: [\n                                                                        highestScore,\n                                                                        \" (\",\n                                                                        highestPercentage,\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Lowest Score:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-red-600\",\n                                                                    children: [\n                                                                        lowestScore,\n                                                                        \" (\",\n                                                                        lowestPercentage,\n                                                                        \"%)\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Youth Engagement:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-semibold text-purple-600\",\n                                                                    children: [\n                                                                        averageYouthPercentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                                    lineNumber: 672,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\all-federation\\\\page.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(AllFederationPage, \"Fo7Cs3et470Q19GJX6/8j65+4ms=\", false, function() {\n    return [\n        _components_AuthContext__WEBPACK_IMPORTED_MODULE_11__.useAuth\n    ];\n});\n_c = AllFederationPage;\nvar _c;\n$RefreshReg$(_c, \"AllFederationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9hbGwtZmVkZXJhdGlvbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXFEO0FBRTRDO0FBQ2xEO0FBQ1k7QUFDOUI7QUFDbUI7QUFDRjtBQUNBO0FBQ2tDO0FBTTFEO0FBQ2lGO0FBRXBEO0FBQ29DO0FBQ1Y7QUFDVTtBQUN0QjtBQUNsQztBQUVoQixTQUFTNEM7O0lBQ3RCLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdQLGlFQUFPQTtJQUN4QixNQUFNLENBQUNRLGFBQWFDLGVBQWUsR0FBRy9DLCtDQUFRQSxDQUF3QixFQUFFO0lBQ3hFLE1BQU0sQ0FBQ2dELG9CQUFvQkMsc0JBQXNCLEdBQUdqRCwrQ0FBUUEsQ0FBUztJQUNyRSxNQUFNLENBQUNrRCxXQUFXQyxhQUFhLEdBQUduRCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNvRCxZQUFZQyxjQUFjLEdBQUdyRCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNzRCxRQUFRQyxVQUFVLEdBQUd2RCwrQ0FBUUEsQ0FBNEM7SUFDaEYsTUFBTSxDQUFDd0QsV0FBV0MsYUFBYSxHQUFHekQsK0NBQVFBLENBQWlCO0lBQzNELE1BQU0sQ0FBQzBELFVBQVVDLFlBQVksR0FBRzNELCtDQUFRQSxDQUFvQztJQUM1RSxNQUFNLENBQUM0RCxXQUFXQyxhQUFhLEdBQUc3RCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUM4RCxZQUFZQyxjQUFjLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUU3QyxzQkFBc0I7SUFDdEIsTUFBTSxDQUFDZ0UscUJBQXFCQyx1QkFBdUIsR0FBR2pFLCtDQUFRQSxDQUFTO0lBQ3ZFLE1BQU0sQ0FBQ2tFLHFCQUFxQkMsdUJBQXVCLEdBQUduRSwrQ0FBUUEsQ0FBUztJQUN2RSxrQkFBa0I7SUFDbEIsTUFBTSxDQUFDb0UsbUJBQW1CQyxxQkFBcUIsR0FBR3JFLCtDQUFRQSxDQUFTO0lBQ25FLE1BQU0sQ0FBQ3NFLG1CQUFtQkMscUJBQXFCLEdBQUd2RSwrQ0FBUUEsQ0FBUztJQUNuRSxNQUFNLENBQUN3RSxjQUFjQyxnQkFBZ0IsR0FBR3pFLCtDQUFRQSxDQUFTO0lBQ3pELE1BQU0sQ0FBQzBFLGFBQWFDLGVBQWUsR0FBRzNFLCtDQUFRQSxDQUFTO0lBQ3ZELE1BQU0sQ0FBQzRFLG1CQUFtQkMscUJBQXFCLEdBQUc3RSwrQ0FBUUEsQ0FBUztJQUNuRSxNQUFNLENBQUM4RSxrQkFBa0JDLG9CQUFvQixHQUFHL0UsK0NBQVFBLENBQVM7SUFDakUsOEJBQThCO0lBQzlCLE1BQU0sQ0FBQ2dGLHdCQUF3QkMsMEJBQTBCLEdBQUdqRiwrQ0FBUUEsQ0FBUztJQUM3RSxNQUFNLENBQUNrRixrQkFBa0JDLG9CQUFvQixHQUFHbkYsK0NBQVFBLENBQWdDLENBQUM7SUFDekYsTUFBTSxDQUFDb0Ysa0JBQWtCQyxvQkFBb0IsR0FBR3JGLCtDQUFRQSxDQUFnQyxDQUFDO0lBQ3pGLE1BQU0sQ0FBQ3NGLGtCQUFrQkMsb0JBQW9CLEdBQUd2RiwrQ0FBUUEsQ0FBZ0MsQ0FBQztJQUN6RixNQUFNLENBQUN3RixhQUFhQyxlQUFlLEdBQUd6RiwrQ0FBUUEsQ0FBa0MsRUFBRTtJQUNsRiw4QkFBOEI7SUFDOUIsTUFBTSxDQUFDMEYsbUJBQW1CQyxxQkFBcUIsR0FBRzNGLCtDQUFRQSxDQUE2QjtJQUN2RixNQUFNLENBQUM0RixzQkFBc0JDLHdCQUF3QixHQUFHN0YsK0NBQVFBLENBQWdDLENBQUM7SUFFakcsa0NBQWtDO0lBQ2xDLE1BQU04RiwrQkFBK0I1Riw4Q0FBT0E7bUVBQUM7WUFDM0MsSUFBSTZGLFdBQVdqRCxZQUFZa0QsTUFBTTtvRkFBQ0MsQ0FBQUE7d0JBRTlCQSxXQUNBQSxxQkFDQUE7b0JBSEYsTUFBTUMsZ0JBQWdCLENBQUM5QyxnQkFDckI2QyxZQUFBQSxJQUFJRSxJQUFJLGNBQVJGLGdDQUFBQSxVQUFVRyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ2pELFdBQVdnRCxXQUFXLFVBQ3ZESCxzQkFBQUEsSUFBSUssY0FBYyxjQUFsQkwsMENBQUFBLG9CQUFvQkcsV0FBVyxHQUFHQyxRQUFRLENBQUNqRCxXQUFXZ0QsV0FBVyxVQUNqRUgsc0JBQUFBLElBQUlNLGNBQWMsY0FBbEJOLDBDQUFBQSxvQkFBb0JHLFdBQVcsR0FBR0MsUUFBUSxDQUFDakQsV0FBV2dELFdBQVc7b0JBRW5FLE1BQU1JLGFBQWFQLElBQUlPLFVBQVUsSUFBSTtvQkFDckMsTUFBTUMsZ0JBQWdCL0MsYUFBYSxTQUNoQ0EsYUFBYSxVQUFVOEMsY0FBYyxNQUNyQzlDLGFBQWEsWUFBWThDLGNBQWMsTUFBTUEsYUFBYSxNQUMxRDlDLGFBQWEsU0FBUzhDLGFBQWE7b0JBRXRDLE9BQU9OLGlCQUFpQk87Z0JBQzFCOztZQUVBLDRCQUE0QjtZQUM1QlYsU0FBU1csSUFBSTsyRUFBQyxDQUFDQyxHQUFHQztvQkFDaEIsSUFBSUMsUUFBYUM7b0JBRWpCLE9BQVF4RDt3QkFDTixLQUFLOzRCQUNIdUQsU0FBU0YsRUFBRVIsSUFBSSxJQUFJOzRCQUNuQlcsU0FBU0YsRUFBRVQsSUFBSSxJQUFJOzRCQUNuQjt3QkFDRixLQUFLOzRCQUNIVSxTQUFTRixFQUFFSSxVQUFVLElBQUk7NEJBQ3pCRCxTQUFTRixFQUFFRyxVQUFVLElBQUk7NEJBQ3pCO3dCQUNGLEtBQUs7NEJBQ0hGLFNBQVNGLEVBQUVILFVBQVUsSUFBSTs0QkFDekJNLFNBQVNGLEVBQUVKLFVBQVUsSUFBSTs0QkFDekI7d0JBQ0YsS0FBSzs0QkFDSEssU0FBU0csd0JBQXdCTCxFQUFFTSxnQkFBZ0IsSUFBSTs0QkFDdkRILFNBQVNFLHdCQUF3QkosRUFBRUssZ0JBQWdCLElBQUk7NEJBQ3ZEO3dCQUNGOzRCQUNFLE9BQU87b0JBQ1g7b0JBRUEsSUFBSSxPQUFPSixXQUFXLFVBQVU7d0JBQzlCLE9BQU9yRCxjQUFjLFFBQVFxRCxPQUFPSyxhQUFhLENBQUNKLFVBQVVBLE9BQU9JLGFBQWEsQ0FBQ0w7b0JBQ25GLE9BQU87d0JBQ0wsT0FBT3JELGNBQWMsUUFBUXFELFNBQVNDLFNBQVNBLFNBQVNEO29CQUMxRDtnQkFDRjs7WUFFQSxPQUFPZDtRQUNUO2tFQUFHO1FBQUNqRDtRQUFhTTtRQUFZRTtRQUFRRTtRQUFXRTtLQUFTO0lBRXpELCtEQUErRDtJQUMvRCxNQUFNc0QsMEJBQTBCLENBQUNHO1FBQy9CLE9BQVFBO1lBQ04sS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBUyxPQUFPO1lBQ3JCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLDZEQUE2RDtJQUM3RCw2Q0FBNkM7SUFDN0MsTUFBTUMsbUJBQWtEO1FBQ3RELEdBQUc7UUFDSCxHQUFHO1FBQ0gsR0FBRztRQUNILEdBQUc7UUFDSCxHQUFHO1FBQ0gsR0FBRztRQUNILEdBQUc7UUFDSCxJQUFJO1FBQ0osSUFBSTtJQUNOO0lBRUFuSCxnREFBU0E7dUNBQUM7WUFDUndGLGVBQWU7Z0JBQ2I7b0JBQUU0QixJQUFJO29CQUFHQyxPQUFPO2dCQUFhO2dCQUM3QjtvQkFBRUQsSUFBSTtvQkFBR0MsT0FBTztnQkFBMkI7Z0JBQzNDO29CQUFFRCxJQUFJO29CQUFHQyxPQUFPO2dCQUFhO2dCQUM3QjtvQkFBRUQsSUFBSTtvQkFBR0MsT0FBTztnQkFBdUI7Z0JBQ3ZDO29CQUFFRCxJQUFJO29CQUFHQyxPQUFPO2dCQUFxQjtnQkFDckM7b0JBQUVELElBQUk7b0JBQUdDLE9BQU87Z0JBQXdCO2dCQUN4QztvQkFBRUQsSUFBSTtvQkFBR0MsT0FBTztnQkFBb0I7Z0JBQ3BDO29CQUFFRCxJQUFJO29CQUFJQyxPQUFPO2dCQUFzQjtnQkFDdkM7b0JBQUVELElBQUk7b0JBQUlDLE9BQU87Z0JBQXFCO2FBQ3ZDO1FBQ0g7c0NBQUcsRUFBRTtJQUNMLHdCQUF3QjtJQUN4QixNQUFNQyxtQkFBbUI7WUFBT0Msb0ZBQW1CO1FBQ2pELElBQUlBLGtCQUFrQnpELGNBQWM7UUFFcEMsSUFBSTtZQUNGLHVEQUF1RDtZQUN2RCxNQUFNMEQsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUksQ0FBQ0QsU0FBU0UsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUVsQyxNQUFNQyxPQUFPLE1BQU1KLFNBQVNLLElBQUk7WUFDaEMvRSxlQUFlOEUsS0FBSy9FLFdBQVcsSUFBSSxFQUFFO1lBRXJDLHFCQUFxQjtZQUNyQixJQUFJLENBQUMrRSxLQUFLL0UsV0FBVyxJQUFJLEVBQUUsRUFBRWlGLE1BQU0sR0FBRyxHQUFHO2dCQUN2QyxNQUFNQyxnQkFBZ0JILEtBQUsvRSxXQUFXLENBQUNrRCxNQUFNLENBQUMsQ0FBQ0MsTUFBNkJBLElBQUlnQyxhQUFhLEVBQUVDLEdBQUcsQ0FBQyxDQUFDakMsTUFBNkJBLElBQUlnQyxhQUFhO2dCQUNsSixNQUFNRSxnQkFBZ0JOLEtBQUsvRSxXQUFXLENBQUNrRCxNQUFNLENBQUMsQ0FBQ0MsTUFBNkJBLElBQUltQyxhQUFhLEVBQUVGLEdBQUcsQ0FBQyxDQUFDakMsTUFBNkJBLElBQUltQyxhQUFhO2dCQUNsSixNQUFNQyxrQkFBa0JMLGNBQWNELE1BQU0sR0FBRyxJQUMzQ08sS0FBS0MsS0FBSyxDQUFDUCxjQUFjUSxNQUFNLENBQUMsQ0FBQ0MsS0FBYUMsTUFBZ0JELE1BQU1DLEtBQUssS0FBS1YsY0FBY0QsTUFBTSxJQUNsRztnQkFDSixNQUFNWSxrQkFBa0JSLGNBQWNKLE1BQU0sR0FBRyxJQUMzQ08sS0FBS0MsS0FBSyxDQUFDSixjQUFjSyxNQUFNLENBQUMsQ0FBQ0MsS0FBYUMsTUFBZ0JELE1BQU1DLEtBQUssS0FBS1AsY0FBY0osTUFBTSxJQUNsRztnQkFDSjlELHVCQUF1Qm9FO2dCQUN2QmxFLHVCQUF1QndFO1lBQ3pCO1lBRUEsc0JBQXNCO1lBQ3RCdEUscUJBQXFCd0QsS0FBS2UsYUFBYSxJQUFJO1lBQzNDckUscUJBQXFCc0QsS0FBS2dCLGFBQWEsSUFBSTtZQUMzQ3BFLGdCQUFnQm9ELEtBQUtyRCxZQUFZLElBQUk7WUFDckNHLGVBQWVrRCxLQUFLbkQsV0FBVyxJQUFJO1lBQ25DRyxxQkFBcUJnRCxLQUFLakQsaUJBQWlCLElBQUk7WUFDL0NHLG9CQUFvQjhDLEtBQUsvQyxnQkFBZ0IsSUFBSTtZQUM3Q0csMEJBQTBCNEMsS0FBS2lCLGtCQUFrQixJQUFJO1lBQ3JEM0Qsb0JBQW9CMEMsS0FBSzNDLGdCQUFnQixJQUFJLENBQUM7WUFDOUNHLG9CQUFvQndDLEtBQUt6QyxnQkFBZ0IsSUFBSSxDQUFDO1lBQzlDRyxvQkFBb0JzQyxLQUFLdkMsZ0JBQWdCLElBQUksQ0FBQztZQUU5QyxJQUFJa0Msa0JBQWtCO2dCQUNwQjdFLDBDQUFLQSxDQUFDb0csT0FBTyxDQUFDO1lBQ2hCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLElBQUl4QixrQkFBa0I7Z0JBQ3BCN0UsMENBQUtBLENBQUNxRyxLQUFLLENBQUM7WUFDZDtRQUNGLFNBQVU7WUFDUjdGLGFBQWE7WUFDYlksY0FBYztRQUNoQjtJQUNGO0lBRUE5RCxnREFBU0E7dUNBQUM7WUFDUnNIO1FBQ0Y7c0NBQUcsRUFBRTtJQUVMLHFDQUFxQztJQUNyQyxNQUFNMkIseUJBQXlCLENBQUNDO1FBQzlCbEcsc0JBQXNCa0c7UUFFdEIsSUFBSUEsVUFBVSxPQUFPO1lBQ25CeEQscUJBQXFCO1lBQ3JCRSx3QkFBd0IsQ0FBQztRQUMzQixPQUFPO1lBQ0wsTUFBTXVELFdBQVd0RyxZQUFZdUcsSUFBSSxDQUFDcEQsQ0FBQUEsTUFBT0EsSUFBSW9CLEVBQUUsS0FBSzhCO1lBQ3BEeEQscUJBQXFCeUQsWUFBWTtZQUNqQ3ZELHdCQUF3QnVELENBQUFBLHFCQUFBQSwrQkFBQUEsU0FBVUUsYUFBYSxLQUFJLENBQUM7UUFDdEQ7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNQyxjQUFjO1FBQ2xCLE1BQU1DLFVBQVU7WUFBQztZQUFRO1lBQWE7WUFBYTtZQUFlO1lBQWM7U0FBVTtRQUMxRixNQUFNQyxVQUFVM0QsNkJBQTZCb0MsR0FBRyxDQUFDakMsQ0FBQUEsTUFBTztnQkFDdERBLElBQUlFLElBQUksSUFBSTtnQkFDWkYsSUFBSUssY0FBYyxJQUFJO2dCQUN0QkwsSUFBSU0sY0FBYyxJQUFJO2dCQUN0Qk4sSUFBSWMsVUFBVSxJQUFJO2dCQUNsQmQsSUFBSU8sVUFBVSxJQUFJO2dCQUNsQlAsSUFBSWdCLGdCQUFnQixJQUFJO2FBQ3pCO1FBRUQsTUFBTXlDLGFBQWE7WUFBQ0Y7ZUFBWUM7U0FBUSxDQUNyQ3ZCLEdBQUcsQ0FBQ3lCLENBQUFBLE1BQU9BLElBQUl6QixHQUFHLENBQUMwQixDQUFBQSxPQUFRLElBQVMsT0FBTEEsTUFBSyxNQUFJQyxJQUFJLENBQUMsTUFDN0NBLElBQUksQ0FBQztRQUVSLE1BQU1DLE9BQU8sSUFBSUMsS0FBSztZQUFDTDtTQUFXLEVBQUU7WUFBRU0sTUFBTTtRQUFXO1FBQ3ZELE1BQU1DLE1BQU1DLElBQUlDLGVBQWUsQ0FBQ0w7UUFDaEMsTUFBTW5ELElBQUl5RCxTQUFTQyxhQUFhLENBQUM7UUFDakMxRCxFQUFFMkQsSUFBSSxHQUFHTDtRQUNUdEQsRUFBRTRELFFBQVEsR0FBRyxlQUFzRCxPQUF2QyxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxFQUFDO1FBQ25FL0QsRUFBRWdFLEtBQUs7UUFDUFQsSUFBSVUsZUFBZSxDQUFDWDtRQUNwQnRILDBDQUFLQSxDQUFDb0csT0FBTyxDQUFDO0lBQ2hCO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU04QixxQkFBcUIzSyw4Q0FBT0E7eURBQUM7WUFDakMsTUFBTTRLLFFBQVFoSSxZQUFZaUYsTUFBTTtZQUNoQyxNQUFNZ0QsaUJBQWlCakksWUFBWWtELE1BQU07aUVBQUNnRixDQUFBQSxJQUFLLENBQUNBLEVBQUV4RSxVQUFVLElBQUksTUFBTTtnRUFBSXVCLE1BQU07WUFDaEYsTUFBTWtELG1CQUFtQm5JLFlBQVlrRCxNQUFNO2lFQUFDZ0YsQ0FBQUEsSUFBSyxDQUFDQSxFQUFFeEUsVUFBVSxJQUFJLE1BQU0sTUFBTSxDQUFDd0UsRUFBRXhFLFVBQVUsSUFBSSxLQUFLO2dFQUFJdUIsTUFBTTtZQUM5RyxNQUFNbUQsZ0JBQWdCcEksWUFBWWtELE1BQU07aUVBQUNnRixDQUFBQSxJQUFLLENBQUNBLEVBQUV4RSxVQUFVLElBQUksS0FBSztnRUFBSXVCLE1BQU07WUFFOUUsT0FBTztnQkFDTCtDO2dCQUNBQztnQkFDQUU7Z0JBQ0FDO2dCQUNBQyxnQkFBZ0JMLFFBQVEsSUFBSXhDLEtBQUtDLEtBQUssQ0FBQyxpQkFBa0J1QyxRQUFTLE9BQU87Z0JBQ3pFTSxrQkFBa0JOLFFBQVEsSUFBSXhDLEtBQUtDLEtBQUssQ0FBQyxtQkFBb0J1QyxRQUFTLE9BQU87Z0JBQzdFTyxlQUFlUCxRQUFRLElBQUl4QyxLQUFLQyxLQUFLLENBQUMsZ0JBQWlCdUMsUUFBUyxPQUFPO1lBQ3pFO1FBQ0Y7d0RBQUc7UUFBQ2hJO0tBQVk7SUFFaEIscUJBQ0UsOERBQUN3STtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQU9ELFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzdLLGtEQUFJQTtvQ0FBQzRKLE1BQUs7OENBQ1QsNEVBQUMzSix5REFBTUE7d0NBQUM4SyxTQUFRO3dDQUFRQyxNQUFLO3dDQUFPSCxXQUFVO2tEQUM1Qyw0RUFBQ3JLLDhNQUFTQTs0Q0FBQ3FLLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3pCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNJOzRDQUFHSixXQUFVOzs4REFDWiw4REFBQ3ZKLDhNQUFlQTtvREFBQ3VKLFdBQVU7Ozs7OztnREFBeUI7Ozs7Ozs7c0RBR3RELDhEQUFDSzs0Q0FBRUwsV0FBVTtzREFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHakQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzVLLHlEQUFNQTtvQ0FDTGtMLFNBQVMsSUFBTXRFLGlCQUFpQjtvQ0FDaEN1RSxVQUFVaEk7b0NBQ1YySCxTQUFRO29DQUNSQyxNQUFLO29DQUNMSCxXQUFVOztzREFFViw4REFBQzNKLDhNQUFTQTs0Q0FBQzJKLFdBQVcsZ0JBQWlELE9BQWpDekgsYUFBYSxpQkFBaUI7Ozs7Ozt3Q0FBUTs7Ozs7Ozs4Q0FHOUUsOERBQUNuRCx5REFBTUE7b0NBQUNrTCxTQUFTdEM7b0NBQWFrQyxTQUFRO29DQUFVQyxNQUFLO29DQUFLSCxXQUFVOztzREFDbEUsOERBQUNoSyw4TUFBUUE7NENBQUNnSyxXQUFVOzs7Ozs7d0NBQWlCOzs7Ozs7OzhDQUd2Qyw4REFBQzlLLG9FQUFXQTs7Ozs7OENBQ1osOERBQUNELHdEQUFPQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLZCw4REFBQ3VMO2dCQUFLUixXQUFVOztrQ0FDZCw4REFBQ3pLLHFEQUFJQTt3QkFBQ3FJLE9BQU92Rjt3QkFBV29JLGVBQWVuSTt3QkFBYzBILFdBQVU7OzBDQUM3RCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDdksseURBQVFBO3dDQUFDdUssV0FBVTs7MERBQ2xCLDhEQUFDdEssNERBQVdBO2dEQUFDa0ksT0FBTTswREFBVzs7Ozs7OzBEQUM5Qiw4REFBQ2xJLDREQUFXQTtnREFBQ2tJLE9BQU07MERBQVk7Ozs7OzswREFDL0IsOERBQUNsSSw0REFBV0E7Z0RBQUNrSSxPQUFNOzBEQUFXOzs7Ozs7MERBQzlCLDhEQUFDbEksNERBQVdBO2dEQUFDa0ksT0FBTTswREFBVTs7Ozs7OzBEQUM3Qiw4REFBQ2xJLDREQUFXQTtnREFBQ2tJLE9BQU07MERBQWM7Ozs7Ozs7Ozs7OztrREFHbkMsOERBQUNtQzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2xLLDhNQUFNQTt3REFBQ2tLLFdBQVU7Ozs7OztrRUFDbEIsOERBQUMzSyx1REFBS0E7d0RBQ0pxTCxhQUFZO3dEQUNaOUMsT0FBTy9GO3dEQUNQOEksVUFBVSxDQUFDQyxJQUFNOUksY0FBYzhJLEVBQUVDLE1BQU0sQ0FBQ2pELEtBQUs7d0RBQzdDb0MsV0FBVTs7Ozs7Ozs7Ozs7OzBEQUlkLDhEQUFDdEosMERBQU1BO2dEQUFDa0gsT0FBT3pGO2dEQUFVc0ksZUFBZSxDQUFDN0MsUUFBZXhGLFlBQVl3Rjs7a0VBQ2xFLDhEQUFDL0csaUVBQWFBO3dEQUFDbUosV0FBVTs7MEVBQ3ZCLDhEQUFDakssOE1BQU1BO2dFQUFDaUssV0FBVTs7Ozs7OzBFQUNsQiw4REFBQ2xKLCtEQUFXQTs7Ozs7Ozs7Ozs7a0VBRWQsOERBQUNILGlFQUFhQTs7MEVBQ1osOERBQUNDLDhEQUFVQTtnRUFBQ2dILE9BQU07MEVBQU07Ozs7OzswRUFDeEIsOERBQUNoSCw4REFBVUE7Z0VBQUNnSCxPQUFNOzBFQUFPOzs7Ozs7MEVBQ3pCLDhEQUFDaEgsOERBQVVBO2dFQUFDZ0gsT0FBTTswRUFBUzs7Ozs7OzBFQUMzQiw4REFBQ2hILDhEQUFVQTtnRUFBQ2dILE9BQU07MEVBQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJNUIsOERBQUNsSCwwREFBTUE7Z0RBQUNrSCxPQUFPN0Y7Z0RBQVEwSSxlQUFlLENBQUM3QyxRQUFlNUYsVUFBVTRGOztrRUFDOUQsOERBQUMvRyxpRUFBYUE7d0RBQUNtSixXQUFVO2tFQUN2Qiw0RUFBQ2xKLCtEQUFXQTs7Ozs7Ozs7OztrRUFFZCw4REFBQ0gsaUVBQWFBOzswRUFDWiw4REFBQ0MsOERBQVVBO2dFQUFDZ0gsT0FBTTswRUFBUTs7Ozs7OzBFQUMxQiw4REFBQ2hILDhEQUFVQTtnRUFBQ2dILE9BQU07MEVBQU87Ozs7OzswRUFDekIsOERBQUNoSCw4REFBVUE7Z0VBQUNnSCxPQUFNOzBFQUFhOzs7Ozs7MEVBQy9CLDhEQUFDaEgsOERBQVVBO2dFQUFDZ0gsT0FBTTswRUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUk5Qiw4REFBQ3hJLHlEQUFNQTtnREFDTDhLLFNBQVE7Z0RBQ1JDLE1BQUs7Z0RBQ0xHLFNBQVMsSUFBTXBJLGFBQWFELGNBQWMsUUFBUSxTQUFTOzBEQUUxREEsY0FBYyxzQkFBUSw4REFBQzFCLDhNQUFPQTtvREFBQ3lKLFdBQVU7Ozs7O3lFQUFlLDhEQUFDeEosOE1BQVFBO29EQUFDd0osV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS25GLDhEQUFDeEssNERBQVdBO2dDQUFDb0ksT0FBTTtnQ0FBV29DLFdBQVU7MENBQ3RDLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBRWIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0k7b0RBQUdKLFdBQVU7OERBQTBCOzs7Ozs7OERBQ3hDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNjOzREQUFJQyxLQUFJOzREQUE2RkMsS0FBSTs0REFBZ0NoQixXQUFVOzs7Ozs7c0VBQ3BKLDhEQUFDYzs0REFBSUMsS0FBSTs0REFBaUdDLEtBQUk7NERBQW9DaEIsV0FBVTs7Ozs7O3NFQUM1Siw4REFBQ2M7NERBQUlDLEtBQUk7NERBQW9GQyxLQUFJOzREQUF1QmhCLFdBQVU7Ozs7OztzRUFDbEksOERBQUNjOzREQUFJQyxLQUFJOzREQUFpSEMsS0FBSTs0REFBb0RoQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS2hNLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNwTCxxREFBSUE7O3NFQUNILDhEQUFDRSwyREFBVUE7NERBQUNrTCxXQUFVO3NFQUNwQiw0RUFBQ2pMLDBEQUFTQTtnRUFBQ2lMLFdBQVU7O2tGQUNuQiw4REFBQzdKLDhNQUFLQTt3RUFBQzZKLFdBQVU7Ozs7OztvRUFBNEI7Ozs7Ozs7Ozs7OztzRUFJakQsOERBQUNuTCw0REFBV0E7OzhFQUNWLDhEQUFDa0w7b0VBQUlDLFdBQVU7O3dFQUFtQ2pIO3dFQUFrQjs7Ozs7Ozs4RUFDcEUsOERBQUNzSDtvRUFBRUwsV0FBVTs4RUFBZ0M7Ozs7Ozs4RUFDN0MsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDMUssdURBQUtBO3dFQUFDNEssU0FBU25ILHFCQUFxQixLQUFLLFlBQVlBLHFCQUFxQixLQUFLLGNBQWM7a0ZBQzNGQSxxQkFBcUIsS0FBSyxjQUFjQSxxQkFBcUIsS0FBSyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNcEYsOERBQUNuRSxxREFBSUE7O3NFQUNILDhEQUFDRSwyREFBVUE7NERBQUNrTCxXQUFVO3NFQUNwQiw0RUFBQ2pMLDBEQUFTQTtnRUFBQ2lMLFdBQVU7O2tGQUNuQiw4REFBQzlKLDhNQUFLQTt3RUFBQzhKLFdBQVU7Ozs7OztvRUFBMkI7Ozs7Ozs7Ozs7OztzRUFJaEQsOERBQUNuTCw0REFBV0E7OzhFQUNWLDhEQUFDa0w7b0VBQUlDLFdBQVU7O3dFQUFxQ3ZHO3dFQUF1Qjs7Ozs7Ozs4RUFDM0UsOERBQUM0RztvRUFBRUwsV0FBVTs4RUFBZ0M7Ozs7Ozs4RUFDN0MsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDRDt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ0Q7NEVBQ0NDLFdBQVU7NEVBQ1ZpQixPQUFPO2dGQUFFQyxPQUFPLEdBQXlDLE9BQXRDbkUsS0FBS29FLEdBQUcsQ0FBQzFILHdCQUF3QixNQUFLOzRFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU90RSw4REFBQzdFLHFEQUFJQTs7c0VBQ0gsOERBQUNFLDJEQUFVQTs0REFBQ2tMLFdBQVU7c0VBQ3BCLDRFQUFDakwsMERBQVNBO2dFQUFDaUwsV0FBVTs7a0ZBQ25CLDhEQUFDNUosOE1BQU1BO3dFQUFDNEosV0FBVTs7Ozs7O29FQUEwQjs7Ozs7Ozs7Ozs7O3NFQUloRCw4REFBQ25MLDREQUFXQTtzRUFDViw0RUFBQ2tMO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDb0I7Z0ZBQUtwQixXQUFVOzBGQUFVOzs7Ozs7MEZBQzFCLDhEQUFDb0I7Z0ZBQUtwQixXQUFVOztvRkFBZ0MzRztvRkFBa0I7Ozs7Ozs7Ozs7Ozs7a0ZBRXBFLDhEQUFDMEc7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDb0I7Z0ZBQUtwQixXQUFVOzBGQUFVOzs7Ozs7MEZBQzFCLDhEQUFDb0I7Z0ZBQUtwQixXQUFVOztvRkFBOEJ6RztvRkFBaUI7Ozs7Ozs7Ozs7Ozs7a0ZBRWpFLDhEQUFDd0c7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDb0I7Z0ZBQUtwQixXQUFVOzBGQUFVOzs7Ozs7MEZBQzFCLDhEQUFDb0I7Z0ZBQUtwQixXQUFVOztvRkFBaUIzRyxvQkFBb0JFO29GQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU3BGLDhEQUFDL0QsNERBQVdBO2dDQUFDb0ksT0FBTTtnQ0FBWW9DLFdBQVU7MENBQ3ZDLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNoSix1RkFBd0JBOzRDQUN2Qk8sYUFBYUE7NENBQ2JFLG9CQUFvQkE7NENBQ3BCNEosb0JBQW9CMUQ7Ozs7OztzREFHdEIsOERBQUMxRyxrRkFBbUJBOzRDQUNsQjBDLGtCQUFrQkE7NENBQ2xCRSxrQkFBa0JBOzRDQUNsQkUsa0JBQWtCQTs0Q0FDbEJFLGFBQWFBOzRDQUNiNEIsa0JBQWtCQTs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS3hCLDhEQUFDckcsNERBQVdBO2dDQUFDb0ksT0FBTTtnQ0FBV29DLFdBQVU7MENBQ3RDLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzdJLDJFQUFXQTs7Ozs7Ozs7Ozs7Ozs7OzBDQUloQiw4REFBQzNCLDREQUFXQTtnQ0FBQ29JLE9BQU07Z0NBQVVvQyxXQUFVOzBDQUNyQyw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUM5SSx1RkFBd0JBO3dDQUFDSyxhQUFhQTs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJM0MsOERBQUMvQiw0REFBV0E7Z0NBQUNvSSxPQUFNO2dDQUFjb0MsV0FBVTswQ0FDekMsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDcEwscURBQUlBOzswREFDSCw4REFBQ0UsMkRBQVVBOztrRUFDVCw4REFBQ0MsMERBQVNBO3dEQUFDaUwsV0FBVTs7MEVBQ25CLDhEQUFDcEssOE1BQVNBO2dFQUFDb0ssV0FBVTs7Ozs7OzREQUFZOzs7Ozs7O2tFQUduQyw4REFBQ2hMLGdFQUFlQTtrRUFBQzs7Ozs7Ozs7Ozs7OzBEQUluQiw4REFBQ0gsNERBQVdBOzBEQUNUOEMsMEJBQ0MsOERBQUNvSTtvREFBSUMsV0FBVTs4REFDWjt3REFBQzt3REFBRzt3REFBRzt3REFBRzt3REFBRztxREFBRSxDQUFDckQsR0FBRyxDQUFDLENBQUMyRSxrQkFDcEIsOERBQUN2Qjs0REFBWUMsV0FBVTs7OEVBQ3JCLDhEQUFDRDtvRUFBSUMsV0FBVTs7Ozs7OzhFQUNmLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs7Ozs7c0ZBQ2YsOERBQUNEOzRFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7OEVBRWpCLDhEQUFDRDtvRUFBSUMsV0FBVTs7Ozs7OzsyREFOUHNCOzs7Ozs7Ozs7eUVBV2QsOERBQUN2QjtvREFBSUMsV0FBVTs7d0RBQ1p6Riw2QkFBNkJvQyxHQUFHLENBQUMsQ0FBQzRFLFlBQVlDLHNCQUM3Qyw4REFBQ3JNLGtEQUFJQTtnRUFBQzRKLE1BQU0sZUFBNkIsT0FBZHdDLFdBQVd6RixFQUFFOzBFQUN0Qyw0RUFBQ2lFO29FQUNDQyxXQUFVO29FQUNWaUIsT0FBTzt3RUFBRVEsZ0JBQWdCLEdBQWMsT0FBWEQsUUFBUSxJQUFHO29FQUFJOztzRkFFN0MsOERBQUN6Qjs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNEO29GQUFJQyxXQUFVOztzR0FDYiw4REFBQ0Q7NEZBQUlDLFdBQVU7c0dBQ2IsNEVBQUNwSyw4TUFBU0E7Z0dBQUNvSyxXQUFVOzs7Ozs7Ozs7OztzR0FFdkIsOERBQUNEOzs4R0FDQyw4REFBQzJCO29HQUFHMUIsV0FBVTs4R0FBeUJ1QixXQUFXM0csSUFBSTs7Ozs7OzhHQUN0RCw4REFBQ21GO29HQUFJQyxXQUFVOztzSEFDYiw4REFBQ29COztnSEFBSztnSEFBWUcsV0FBV3hHLGNBQWMsSUFBSTs7Ozs7OztzSEFDL0MsOERBQUNxRzs7Z0hBQUs7Z0hBQVlHLFdBQVd2RyxjQUFjLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBS3JELDhEQUFDK0U7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNEO29HQUFJQyxXQUFVOzt3R0FDWnVCLFdBQVd0RyxVQUFVLElBQUk7d0dBQUU7Ozs7Ozs7OEdBRTlCLDhEQUFDOEU7b0dBQUlDLFdBQVU7O3dHQUNadUIsV0FBVy9GLFVBQVUsSUFBSTt3R0FBRTt3R0FBRStGLFdBQVdJLFFBQVEsSUFBSTs7Ozs7Ozs7Ozs7OztzR0FJekQsOERBQUNyTSx1REFBS0E7NEZBQ0o0SyxTQUNFLENBQUNxQixXQUFXdEcsVUFBVSxJQUFJLE1BQU0sS0FBSyxZQUNyQyxDQUFDc0csV0FBV3RHLFVBQVUsSUFBSSxNQUFNLEtBQUssY0FBYzs0RkFFckQrRSxXQUFVO3NHQUVULENBQUN1QixXQUFXdEcsVUFBVSxJQUFJLE1BQU0sS0FBSyxTQUNyQyxDQUFDc0csV0FBV3RHLFVBQVUsSUFBSSxNQUFNLEtBQUssV0FBVzs7Ozs7O3NHQUduRCw4REFBQzhFOzRGQUFJQyxXQUFVOzs4R0FDYiw4REFBQ0Q7b0dBQUlDLFdBQVU7OEdBQ1p1QixXQUFXN0YsZ0JBQWdCLElBQUk7Ozs7Ozs4R0FFbEMsOERBQUNxRTtvR0FBSUMsV0FBVTs4R0FBZ0M7Ozs7Ozs7Ozs7OztzR0FHakQsOERBQUM1Syx5REFBTUE7NEZBQUM4SyxTQUFROzRGQUFRQyxNQUFLO3NHQUMzQiw0RUFBQzdKLDhNQUFHQTtnR0FBQzBKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NGQU1yQiw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNEO2dGQUFJQyxXQUFVOzBGQUNiLDRFQUFDRDtvRkFDQ0MsV0FBVyxnREFHVixPQUZDLENBQUN1QixXQUFXdEcsVUFBVSxJQUFJLE1BQU0sS0FBSyxpQkFDckMsQ0FBQ3NHLFdBQVd0RyxVQUFVLElBQUksTUFBTSxLQUFLLGtCQUFrQjtvRkFFekRnRyxPQUFPO3dGQUFFQyxPQUFPLEdBQTZDLE9BQTFDbkUsS0FBS29FLEdBQUcsQ0FBQ0ksV0FBV3RHLFVBQVUsSUFBSSxHQUFHLE1BQUs7b0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7K0RBN0R2QnNHLFdBQVd6RixFQUFFOzs7Ozt3REFxRS9EdkIsNkJBQTZCaUMsTUFBTSxLQUFLLG1CQUN2Qyw4REFBQ3VEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3BLLDhNQUFTQTtvRUFBQ29LLFdBQVU7Ozs7Ozs4RUFDckIsOERBQUMwQjtvRUFBRzFCLFdBQVU7OEVBQTZCOzs7Ozs7OEVBQzNDLDhEQUFDSztvRUFBRUwsV0FBVTs4RUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFhdERySSwwQkFDQyw4REFBQ29JO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDcEwscURBQUlBOzRCQUFDb0wsV0FBVTs7OENBQ2QsOERBQUNsTCwyREFBVUE7OENBQ1QsNEVBQUNDLDBEQUFTQTtrREFBQzs7Ozs7Ozs7Ozs7OENBRWIsOERBQUNGLDREQUFXQTs4Q0FDViw0RUFBQ2tMO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NkNBS3JCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNwTCxxREFBSUE7Z0NBQUNvTCxXQUFVOztrREFDZCw4REFBQ2xMLDJEQUFVQTt3Q0FBQ2tMLFdBQVU7OzBEQUNwQiw4REFBQ2pMLDBEQUFTQTtnREFBQ2lMLFdBQVU7O2tFQUNuQiw4REFBQ3BLLDhNQUFTQTt3REFBQ29LLFdBQVU7Ozs7OztvREFBeUI7Ozs7Ozs7MERBR2hELDhEQUFDaEwsZ0VBQWVBOzBEQUFDOzs7Ozs7Ozs7Ozs7a0RBSW5CLDhEQUFDSCw0REFBV0E7d0NBQUNtTCxXQUFVO2tEQUNyQiw0RUFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNuSyw4TUFBVUE7NERBQUNtSyxXQUFVOzs7Ozs7Ozs7OztrRUFFeEIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQzBCO2dFQUFHMUIsV0FBVTswRUFBNEM7Ozs7OzswRUFDMUQsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDSztnRkFBRUwsV0FBVTswRkFBZ0M7Ozs7OzswRkFDN0MsOERBQUNLO2dGQUFFTCxXQUFVOztvRkFDVnZIO29GQUFvQjs7Ozs7Ozs7Ozs7OztrRkFHekIsOERBQUNzSDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNLO2dGQUFFTCxXQUFVOzBGQUFnQzs7Ozs7OzBGQUM3Qyw4REFBQ0s7Z0ZBQUVMLFdBQVU7O29GQUNWckg7b0ZBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FVckMsOERBQUMvRCxxREFBSUE7Z0NBQUNvTCxXQUFVOztrREFDZCw4REFBQ2xMLDJEQUFVQTt3Q0FBQ2tMLFdBQVU7OzBEQUNwQiw4REFBQ2pMLDBEQUFTQTtnREFBQ2lMLFdBQVU7O2tFQUNuQiw4REFBQy9KLDhNQUFTQTt3REFBQytKLFdBQVU7Ozs7OztvREFBeUI7Ozs7Ozs7MERBR2hELDhEQUFDaEwsZ0VBQWVBOzBEQUFDOzs7Ozs7Ozs7Ozs7a0RBSW5CLDhEQUFDSCw0REFBV0E7d0NBQUNtTCxXQUFVO2tEQUNyQiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQXFDbkg7Ozs7Ozs4RUFDcEQsOERBQUNrSDtvRUFBSUMsV0FBVTs4RUFBZ0M7Ozs7Ozs7Ozs7OztzRUFFakQsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3dFQUFvQ2pIO3dFQUFrQjs7Ozs7Ozs4RUFDckUsOERBQUNnSDtvRUFBSUMsV0FBVTs4RUFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJbkQsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDb0I7OEVBQUs7Ozs7Ozs4RUFDTiw4REFBQ0E7b0VBQUtwQixXQUFVOzt3RUFBZ0MvRzt3RUFBYTt3RUFBR0k7d0VBQWtCOzs7Ozs7Ozs7Ozs7O3NFQUVwRiw4REFBQzBHOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ29COzhFQUFLOzs7Ozs7OEVBQ04sOERBQUNBO29FQUFLcEIsV0FBVTs7d0VBQThCN0c7d0VBQVk7d0VBQUdJO3dFQUFpQjs7Ozs7Ozs7Ozs7OztzRUFFaEYsOERBQUN3Rzs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNvQjs4RUFBSzs7Ozs7OzhFQUNOLDhEQUFDQTtvRUFBS3BCLFdBQVU7O3dFQUFpQ3ZHO3dFQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVzlGO0dBL29Cd0JwQzs7UUFDTE4sNkRBQU9BOzs7S0FERk0iLCJzb3VyY2VzIjpbIkQ6XFx0dWNhdFxcdHVjYXRcXGFwcFxcYWxsLWZlZGVyYXRpb25cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgRGFzaGJvYXJkUHJvdmlkZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9EYXNoYm9hcmRQcm92aWRlclwiO1xyXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIjtcclxuaW1wb3J0IHsgVXNlck5hdiB9IGZyb20gXCJAL2NvbXBvbmVudHMvVXNlck5hdlwiO1xyXG5pbXBvcnQgeyBUaGVtZVRvZ2dsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGhlbWUtdG9nZ2xlXCI7XHJcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcclxuaW1wb3J0IHsgSW5wdXQgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2lucHV0XCI7XHJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xyXG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90YWJzXCI7XHJcbmltcG9ydCB7XHJcbiAgQXJyb3dMZWZ0LCBCdWlsZGluZzIsIFVzZXJDaXJjbGUsIFNlYXJjaCwgRmlsdGVyLCBEb3dubG9hZCxcclxuICBUcmVuZGluZ1VwLCBUcmVuZGluZ0Rvd24sIEJhckNoYXJ0MywgUGllQ2hhcnQsIFVzZXJzLFxyXG4gIENhbGVuZGFyLCBBd2FyZCwgVGFyZ2V0LCBSZWZyZXNoQ3csIEV5ZSwgU2V0dGluZ3MsXHJcbiAgQ2hldnJvbkRvd24sIENoZXZyb25VcCwgU29ydEFzYywgU29ydERlc2MsIExheW91dERhc2hib2FyZFxyXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcclxuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2VsZWN0XCI7XHJcbmltcG9ydCB7IEZlZGVyYXRpb24gfSBmcm9tIFwiQC9saWIvdHlwZXNcIjtcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCJAL2NvbXBvbmVudHMvQXV0aENvbnRleHRcIjtcclxuaW1wb3J0IEZlZGVyYXRpb25BbmFseXRpY3NDaGFydCBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9GZWRlcmF0aW9uQW5hbHl0aWNzQ2hhcnRcIjtcclxuaW1wb3J0IFNlY3Rpb25BdmVyYWdlQ2hhcnQgZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvU2VjdGlvbkF2ZXJhZ2VDaGFydFwiO1xyXG5pbXBvcnQgWW91dGhSZXByZXNlbnRhdGlvbkNoYXJ0IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1lvdXRoUmVwcmVzZW50YXRpb25DaGFydFwiO1xyXG5pbXBvcnQgeyBRQVN0YXRzQ2FyZCB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL1FBU3RhdHNDYXJkXCI7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcInNvbm5lclwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWxsRmVkZXJhdGlvblBhZ2UoKSB7XHJcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgW2ZlZGVyYXRpb25zLCBzZXRGZWRlcmF0aW9uc10gPSB1c2VTdGF0ZTxQYXJ0aWFsPEZlZGVyYXRpb24+W10+KFtdKTtcclxuICBjb25zdCBbc2VsZWN0ZWRGZWRlcmF0aW9uLCBzZXRTZWxlY3RlZEZlZGVyYXRpb25dID0gdXNlU3RhdGU8c3RyaW5nPihcImFsbFwiKTtcclxuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3NvcnRCeSwgc2V0U29ydEJ5XSA9IHVzZVN0YXRlPFwibmFtZVwiIHwgXCJzY29yZVwiIHwgXCJwZXJjZW50YWdlXCIgfCBcInlvdXRoXCI+KFwic2NvcmVcIik7XHJcbiAgY29uc3QgW3NvcnRPcmRlciwgc2V0U29ydE9yZGVyXSA9IHVzZVN0YXRlPFwiYXNjXCIgfCBcImRlc2NcIj4oXCJkZXNjXCIpO1xyXG4gIGNvbnN0IFtmaWx0ZXJCeSwgc2V0RmlsdGVyQnldID0gdXNlU3RhdGU8XCJhbGxcIiB8IFwiaGlnaFwiIHwgXCJtZWRpdW1cIiB8IFwibG93XCI+KFwiYWxsXCIpO1xyXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZShcIm92ZXJ2aWV3XCIpO1xyXG4gIGNvbnN0IFtyZWZyZXNoaW5nLCBzZXRSZWZyZXNoaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgLy8gQ2FsY3VsYXRlZCBhdmVyYWdlc1xyXG4gIGNvbnN0IFthdmVyYWdlUHJlc2lkZW50QWdlLCBzZXRBdmVyYWdlUHJlc2lkZW50QWdlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW2F2ZXJhZ2VTZWNyZXRhcnlBZ2UsIHNldEF2ZXJhZ2VTZWNyZXRhcnlBZ2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcclxuICAvLyBTY29yZSBhbmFseXRpY3NcclxuICBjb25zdCBbYXZlcmFnZVRvdGFsU2NvcmUsIHNldEF2ZXJhZ2VUb3RhbFNjb3JlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW2F2ZXJhZ2VQZXJjZW50YWdlLCBzZXRBdmVyYWdlUGVyY2VudGFnZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIGNvbnN0IFtoaWdoZXN0U2NvcmUsIHNldEhpZ2hlc3RTY29yZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIGNvbnN0IFtsb3dlc3RTY29yZSwgc2V0TG93ZXN0U2NvcmVdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcclxuICBjb25zdCBbaGlnaGVzdFBlcmNlbnRhZ2UsIHNldEhpZ2hlc3RQZXJjZW50YWdlXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7XHJcbiAgY29uc3QgW2xvd2VzdFBlcmNlbnRhZ2UsIHNldExvd2VzdFBlcmNlbnRhZ2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcclxuICAvLyBZb3V0aCBhbmQgc2VjdGlvbiBhbmFseXRpY3NcclxuICBjb25zdCBbYXZlcmFnZVlvdXRoUGVyY2VudGFnZSwgc2V0QXZlcmFnZVlvdXRoUGVyY2VudGFnZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xyXG4gIGNvbnN0IFthdmdTZWN0aW9uU2NvcmVzLCBzZXRBdmdTZWN0aW9uU2NvcmVzXSA9IHVzZVN0YXRlPHsgW3NlY3Rpb246IG51bWJlcl06IG51bWJlciB9Pih7fSk7XHJcbiAgY29uc3QgW21pblNlY3Rpb25TY29yZXMsIHNldE1pblNlY3Rpb25TY29yZXNdID0gdXNlU3RhdGU8eyBbc2VjdGlvbjogbnVtYmVyXTogbnVtYmVyIH0+KHt9KTtcclxuICBjb25zdCBbbWF4U2VjdGlvblNjb3Jlcywgc2V0TWF4U2VjdGlvblNjb3Jlc10gPSB1c2VTdGF0ZTx7IFtzZWN0aW9uOiBudW1iZXJdOiBudW1iZXIgfT4oe30pO1xyXG4gIGNvbnN0IFtzZWN0aW9uTGlzdCwgc2V0U2VjdGlvbkxpc3RdID0gdXNlU3RhdGU8eyBpZDogbnVtYmVyOyB0aXRsZTogc3RyaW5nIH1bXT4oW10pO1xyXG4gIC8vIFNlbGVjdGVkIGZlZGVyYXRpb24gZGV0YWlsc1xyXG4gIGNvbnN0IFtjdXJyZW50RmVkZXJhdGlvbiwgc2V0Q3VycmVudEZlZGVyYXRpb25dID0gdXNlU3RhdGU8UGFydGlhbDxGZWRlcmF0aW9uPiB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtjdXJyZW50U2VjdGlvblNjb3Jlcywgc2V0Q3VycmVudFNlY3Rpb25TY29yZXNdID0gdXNlU3RhdGU8eyBbc2VjdGlvbjogbnVtYmVyXTogbnVtYmVyIH0+KHt9KTtcclxuXHJcbiAgLy8gRmlsdGVyZWQgYW5kIHNvcnRlZCBmZWRlcmF0aW9uc1xyXG4gIGNvbnN0IGZpbHRlcmVkQW5kU29ydGVkRmVkZXJhdGlvbnMgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIGxldCBmaWx0ZXJlZCA9IGZlZGVyYXRpb25zLmZpbHRlcihmZWQgPT4ge1xyXG4gICAgICBjb25zdCBtYXRjaGVzU2VhcmNoID0gIXNlYXJjaFRlcm0gfHxcclxuICAgICAgICBmZWQubmFtZT8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XHJcbiAgICAgICAgZmVkLnByZXNpZGVudF9uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSkgfHxcclxuICAgICAgICBmZWQuc2VjcmV0YXJ5X25hbWU/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKTtcclxuXHJcbiAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBmZWQucGVyY2VudGFnZSB8fCAwO1xyXG4gICAgICBjb25zdCBtYXRjaGVzRmlsdGVyID0gZmlsdGVyQnkgPT09IFwiYWxsXCIgfHxcclxuICAgICAgICAoZmlsdGVyQnkgPT09IFwiaGlnaFwiICYmIHBlcmNlbnRhZ2UgPj0gNzUpIHx8XHJcbiAgICAgICAgKGZpbHRlckJ5ID09PSBcIm1lZGl1bVwiICYmIHBlcmNlbnRhZ2UgPj0gNTAgJiYgcGVyY2VudGFnZSA8IDc1KSB8fFxyXG4gICAgICAgIChmaWx0ZXJCeSA9PT0gXCJsb3dcIiAmJiBwZXJjZW50YWdlIDwgNTApO1xyXG5cclxuICAgICAgcmV0dXJuIG1hdGNoZXNTZWFyY2ggJiYgbWF0Y2hlc0ZpbHRlcjtcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIFNvcnQgdGhlIGZpbHRlcmVkIHJlc3VsdHNcclxuICAgIGZpbHRlcmVkLnNvcnQoKGEsIGIpID0+IHtcclxuICAgICAgbGV0IGFWYWx1ZTogYW55LCBiVmFsdWU6IGFueTtcclxuXHJcbiAgICAgIHN3aXRjaCAoc29ydEJ5KSB7XHJcbiAgICAgICAgY2FzZSBcIm5hbWVcIjpcclxuICAgICAgICAgIGFWYWx1ZSA9IGEubmFtZSB8fCBcIlwiO1xyXG4gICAgICAgICAgYlZhbHVlID0gYi5uYW1lIHx8IFwiXCI7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlIFwic2NvcmVcIjpcclxuICAgICAgICAgIGFWYWx1ZSA9IGEudG90YWxTY29yZSB8fCAwO1xyXG4gICAgICAgICAgYlZhbHVlID0gYi50b3RhbFNjb3JlIHx8IDA7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICBjYXNlIFwicGVyY2VudGFnZVwiOlxyXG4gICAgICAgICAgYVZhbHVlID0gYS5wZXJjZW50YWdlIHx8IDA7XHJcbiAgICAgICAgICBiVmFsdWUgPSBiLnBlcmNlbnRhZ2UgfHwgMDtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgXCJ5b3V0aFwiOlxyXG4gICAgICAgICAgYVZhbHVlID0gZ2V0WW91dGhQZXJjZW50YWdlVmFsdWUoYS55b3V0aF9wZXJjZW50YWdlIHx8IFwiMC0yNSVcIik7XHJcbiAgICAgICAgICBiVmFsdWUgPSBnZXRZb3V0aFBlcmNlbnRhZ2VWYWx1ZShiLnlvdXRoX3BlcmNlbnRhZ2UgfHwgXCIwLTI1JVwiKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICByZXR1cm4gMDtcclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHR5cGVvZiBhVmFsdWUgPT09IFwic3RyaW5nXCIpIHtcclxuICAgICAgICByZXR1cm4gc29ydE9yZGVyID09PSBcImFzY1wiID8gYVZhbHVlLmxvY2FsZUNvbXBhcmUoYlZhbHVlKSA6IGJWYWx1ZS5sb2NhbGVDb21wYXJlKGFWYWx1ZSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgcmV0dXJuIHNvcnRPcmRlciA9PT0gXCJhc2NcIiA/IGFWYWx1ZSAtIGJWYWx1ZSA6IGJWYWx1ZSAtIGFWYWx1ZTtcclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIGZpbHRlcmVkO1xyXG4gIH0sIFtmZWRlcmF0aW9ucywgc2VhcmNoVGVybSwgc29ydEJ5LCBzb3J0T3JkZXIsIGZpbHRlckJ5XSk7XHJcblxyXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBjb252ZXJ0IHlvdXRoIHBlcmNlbnRhZ2UgdG8gbnVtZXJpYyB2YWx1ZVxyXG4gIGNvbnN0IGdldFlvdXRoUGVyY2VudGFnZVZhbHVlID0gKHlvdXRoUGVyY2VudGFnZTogc3RyaW5nKTogbnVtYmVyID0+IHtcclxuICAgIHN3aXRjaCAoeW91dGhQZXJjZW50YWdlKSB7XHJcbiAgICAgIGNhc2UgXCJhYm92ZS03NSVcIjogcmV0dXJuIDg3LjU7XHJcbiAgICAgIGNhc2UgXCI1MS03NSVcIjogcmV0dXJuIDYzO1xyXG4gICAgICBjYXNlIFwiMjYtNTAlXCI6IHJldHVybiAzODtcclxuICAgICAgY2FzZSBcIjAtMjUlXCI6IHJldHVybiAxMi41O1xyXG4gICAgICBkZWZhdWx0OiByZXR1cm4gMDtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBTZWN0aW9uIHRpdGxlcyAoc2hvdWxkIG1hdGNoIGJhY2tlbmQgc2VjdGlvbiBudW1iZXJzIDMtMTEpXHJcbiAgLy8gU2VjdGlvbiBtYXggc2NvcmVzIChmcm9tIHVzZUFzc2Vzc21lbnQudHMpXHJcbiAgY29uc3Qgc2VjdGlvbk1heFNjb3JlczogeyBbc2VjdGlvbjogbnVtYmVyXTogbnVtYmVyIH0gPSB7XHJcbiAgICAzOiAxNCxcclxuICAgIDQ6IDQsXHJcbiAgICA1OiAxMSxcclxuICAgIDY6IDksXHJcbiAgICA3OiA3LFxyXG4gICAgODogOCxcclxuICAgIDk6IDExLFxyXG4gICAgMTA6IDE2LFxyXG4gICAgMTE6IDEwLFxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRTZWN0aW9uTGlzdChbXHJcbiAgICAgIHsgaWQ6IDMsIHRpdGxlOiBcIkxlYWRlcnNoaXBcIiB9LFxyXG4gICAgICB7IGlkOiA0LCB0aXRsZTogXCJPcmdhbml6YXRpb25hbCBTdHJ1Y3R1cmVcIiB9LFxyXG4gICAgICB7IGlkOiA1LCB0aXRsZTogXCJNYW5hZ2VtZW50XCIgfSxcclxuICAgICAgeyBpZDogNiwgdGl0bGU6IFwiV29ya2VyIFBhcnRpY2lwYXRpb25cIiB9LFxyXG4gICAgICB7IGlkOiA3LCB0aXRsZTogXCJDdWx0dXJlIGFuZCBHZW5kZXJcIiB9LFxyXG4gICAgICB7IGlkOiA4LCB0aXRsZTogXCJDb2xsZWN0aXZlIEJhcmdhaW5pbmdcIiB9LFxyXG4gICAgICB7IGlkOiA5LCB0aXRsZTogXCJNZW1iZXIgRW5nYWdlbWVudFwiIH0sXHJcbiAgICAgIHsgaWQ6IDEwLCB0aXRsZTogXCJGaW5hbmNpYWwgU3RhYmlsaXR5XCIgfSxcclxuICAgICAgeyBpZDogMTEsIHRpdGxlOiBcIkF1ZGl0ICYgQ29tcGxpYW5jZVwiIH0sXHJcbiAgICBdKTtcclxuICB9LCBbXSk7XHJcbiAgLy8gRmV0Y2ggYWxsIGZlZGVyYXRpb25zXHJcbiAgY29uc3QgZmV0Y2hGZWRlcmF0aW9ucyA9IGFzeW5jIChzaG93UmVmcmVzaFRvYXN0ID0gZmFsc2UpID0+IHtcclxuICAgIGlmIChzaG93UmVmcmVzaFRvYXN0KSBzZXRSZWZyZXNoaW5nKHRydWUpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIFRoaXMgd291bGQgYmUgcmVwbGFjZWQgd2l0aCB5b3VyIGFjdHVhbCBBUEkgZW5kcG9pbnRcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9mZWRlcmF0aW9ucycpO1xyXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBmZWRlcmF0aW9ucycpO1xyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgc2V0RmVkZXJhdGlvbnMoZGF0YS5mZWRlcmF0aW9ucyB8fCBbXSk7XHJcblxyXG4gICAgICAvLyBDYWxjdWxhdGUgYXZlcmFnZXNcclxuICAgICAgaWYgKChkYXRhLmZlZGVyYXRpb25zIHx8IFtdKS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc3QgcHJlc2lkZW50QWdlcyA9IGRhdGEuZmVkZXJhdGlvbnMuZmlsdGVyKChmZWQ6IFBhcnRpYWw8RmVkZXJhdGlvbj4pID0+IGZlZC5wcmVzaWRlbnRfYWdlKS5tYXAoKGZlZDogUGFydGlhbDxGZWRlcmF0aW9uPikgPT4gZmVkLnByZXNpZGVudF9hZ2UgYXMgbnVtYmVyKTtcclxuICAgICAgICBjb25zdCBzZWNyZXRhcnlBZ2VzID0gZGF0YS5mZWRlcmF0aW9ucy5maWx0ZXIoKGZlZDogUGFydGlhbDxGZWRlcmF0aW9uPikgPT4gZmVkLnNlY3JldGFyeV9hZ2UpLm1hcCgoZmVkOiBQYXJ0aWFsPEZlZGVyYXRpb24+KSA9PiBmZWQuc2VjcmV0YXJ5X2FnZSBhcyBudW1iZXIpO1xyXG4gICAgICAgIGNvbnN0IGF2Z1ByZXNpZGVudEFnZSA9IHByZXNpZGVudEFnZXMubGVuZ3RoID4gMFxyXG4gICAgICAgICAgPyBNYXRoLnJvdW5kKHByZXNpZGVudEFnZXMucmVkdWNlKChzdW06IG51bWJlciwgYWdlOiBudW1iZXIpID0+IHN1bSArIGFnZSwgMCkgLyBwcmVzaWRlbnRBZ2VzLmxlbmd0aClcclxuICAgICAgICAgIDogMDtcclxuICAgICAgICBjb25zdCBhdmdTZWNyZXRhcnlBZ2UgPSBzZWNyZXRhcnlBZ2VzLmxlbmd0aCA+IDBcclxuICAgICAgICAgID8gTWF0aC5yb3VuZChzZWNyZXRhcnlBZ2VzLnJlZHVjZSgoc3VtOiBudW1iZXIsIGFnZTogbnVtYmVyKSA9PiBzdW0gKyBhZ2UsIDApIC8gc2VjcmV0YXJ5QWdlcy5sZW5ndGgpXHJcbiAgICAgICAgICA6IDA7XHJcbiAgICAgICAgc2V0QXZlcmFnZVByZXNpZGVudEFnZShhdmdQcmVzaWRlbnRBZ2UpO1xyXG4gICAgICAgIHNldEF2ZXJhZ2VTZWNyZXRhcnlBZ2UoYXZnU2VjcmV0YXJ5QWdlKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU2V0IHNjb3JlIGFuYWx5dGljc1xyXG4gICAgICBzZXRBdmVyYWdlVG90YWxTY29yZShkYXRhLmF2Z1RvdGFsU2NvcmUgfHwgMCk7XHJcbiAgICAgIHNldEF2ZXJhZ2VQZXJjZW50YWdlKGRhdGEuYXZnUGVyY2VudGFnZSB8fCAwKTtcclxuICAgICAgc2V0SGlnaGVzdFNjb3JlKGRhdGEuaGlnaGVzdFNjb3JlIHx8IDApO1xyXG4gICAgICBzZXRMb3dlc3RTY29yZShkYXRhLmxvd2VzdFNjb3JlIHx8IDApO1xyXG4gICAgICBzZXRIaWdoZXN0UGVyY2VudGFnZShkYXRhLmhpZ2hlc3RQZXJjZW50YWdlIHx8IDApO1xyXG4gICAgICBzZXRMb3dlc3RQZXJjZW50YWdlKGRhdGEubG93ZXN0UGVyY2VudGFnZSB8fCAwKTtcclxuICAgICAgc2V0QXZlcmFnZVlvdXRoUGVyY2VudGFnZShkYXRhLmF2Z1lvdXRoUGVyY2VudGFnZSB8fCAwKTtcclxuICAgICAgc2V0QXZnU2VjdGlvblNjb3JlcyhkYXRhLmF2Z1NlY3Rpb25TY29yZXMgfHwge30pO1xyXG4gICAgICBzZXRNaW5TZWN0aW9uU2NvcmVzKGRhdGEubWluU2VjdGlvblNjb3JlcyB8fCB7fSk7XHJcbiAgICAgIHNldE1heFNlY3Rpb25TY29yZXMoZGF0YS5tYXhTZWN0aW9uU2NvcmVzIHx8IHt9KTtcclxuXHJcbiAgICAgIGlmIChzaG93UmVmcmVzaFRvYXN0KSB7XHJcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkRhdGEgcmVmcmVzaGVkIHN1Y2Nlc3NmdWxseSFcIik7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGZlZGVyYXRpb25zOicsIGVycm9yKTtcclxuICAgICAgaWYgKHNob3dSZWZyZXNoVG9hc3QpIHtcclxuICAgICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byByZWZyZXNoIGRhdGFcIik7XHJcbiAgICAgIH1cclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgIHNldFJlZnJlc2hpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaEZlZGVyYXRpb25zKCk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBIYW5kbGUgZmVkZXJhdGlvbiBzZWxlY3Rpb24gY2hhbmdlXHJcbiAgY29uc3QgaGFuZGxlRmVkZXJhdGlvbkNoYW5nZSA9ICh2YWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZEZlZGVyYXRpb24odmFsdWUpO1xyXG5cclxuICAgIGlmICh2YWx1ZSA9PT0gJ2FsbCcpIHtcclxuICAgICAgc2V0Q3VycmVudEZlZGVyYXRpb24obnVsbCk7XHJcbiAgICAgIHNldEN1cnJlbnRTZWN0aW9uU2NvcmVzKHt9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGNvbnN0IHNlbGVjdGVkID0gZmVkZXJhdGlvbnMuZmluZChmZWQgPT4gZmVkLmlkID09PSB2YWx1ZSk7XHJcbiAgICAgIHNldEN1cnJlbnRGZWRlcmF0aW9uKHNlbGVjdGVkIHx8IG51bGwpO1xyXG4gICAgICBzZXRDdXJyZW50U2VjdGlvblNjb3JlcyhzZWxlY3RlZD8uc2VjdGlvblNjb3JlcyB8fCB7fSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gRXhwb3J0IGZ1bmN0aW9uYWxpdHlcclxuICBjb25zdCBleHBvcnRUb0NTViA9ICgpID0+IHtcclxuICAgIGNvbnN0IGhlYWRlcnMgPSBbXCJOYW1lXCIsIFwiUHJlc2lkZW50XCIsIFwiU2VjcmV0YXJ5XCIsIFwiVG90YWwgU2NvcmVcIiwgXCJQZXJjZW50YWdlXCIsIFwiWW91dGggJVwiXTtcclxuICAgIGNvbnN0IGNzdkRhdGEgPSBmaWx0ZXJlZEFuZFNvcnRlZEZlZGVyYXRpb25zLm1hcChmZWQgPT4gW1xyXG4gICAgICBmZWQubmFtZSB8fCBcIlwiLFxyXG4gICAgICBmZWQucHJlc2lkZW50X25hbWUgfHwgXCJcIixcclxuICAgICAgZmVkLnNlY3JldGFyeV9uYW1lIHx8IFwiXCIsXHJcbiAgICAgIGZlZC50b3RhbFNjb3JlIHx8IDAsXHJcbiAgICAgIGZlZC5wZXJjZW50YWdlIHx8IDAsXHJcbiAgICAgIGZlZC55b3V0aF9wZXJjZW50YWdlIHx8IFwiXCJcclxuICAgIF0pO1xyXG5cclxuICAgIGNvbnN0IGNzdkNvbnRlbnQgPSBbaGVhZGVycywgLi4uY3N2RGF0YV1cclxuICAgICAgLm1hcChyb3cgPT4gcm93Lm1hcChjZWxsID0+IGBcIiR7Y2VsbH1cImApLmpvaW4oXCIsXCIpKVxyXG4gICAgICAuam9pbihcIlxcblwiKTtcclxuXHJcbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NzdkNvbnRlbnRdLCB7IHR5cGU6IFwidGV4dC9jc3ZcIiB9KTtcclxuICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7XHJcbiAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImFcIik7XHJcbiAgICBhLmhyZWYgPSB1cmw7XHJcbiAgICBhLmRvd25sb2FkID0gYGZlZGVyYXRpb25zLSR7bmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF19LmNzdmA7XHJcbiAgICBhLmNsaWNrKCk7XHJcbiAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7XHJcbiAgICB0b2FzdC5zdWNjZXNzKFwiRGF0YSBleHBvcnRlZCBzdWNjZXNzZnVsbHkhXCIpO1xyXG4gIH07XHJcblxyXG4gIC8vIFBlcmZvcm1hbmNlIG1ldHJpY3NcclxuICBjb25zdCBwZXJmb3JtYW5jZU1ldHJpY3MgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIGNvbnN0IHRvdGFsID0gZmVkZXJhdGlvbnMubGVuZ3RoO1xyXG4gICAgY29uc3QgaGlnaFBlcmZvcm1lcnMgPSBmZWRlcmF0aW9ucy5maWx0ZXIoZiA9PiAoZi5wZXJjZW50YWdlIHx8IDApID49IDc1KS5sZW5ndGg7XHJcbiAgICBjb25zdCBtZWRpdW1QZXJmb3JtZXJzID0gZmVkZXJhdGlvbnMuZmlsdGVyKGYgPT4gKGYucGVyY2VudGFnZSB8fCAwKSA+PSA1MCAmJiAoZi5wZXJjZW50YWdlIHx8IDApIDwgNzUpLmxlbmd0aDtcclxuICAgIGNvbnN0IGxvd1BlcmZvcm1lcnMgPSBmZWRlcmF0aW9ucy5maWx0ZXIoZiA9PiAoZi5wZXJjZW50YWdlIHx8IDApIDwgNTApLmxlbmd0aDtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICB0b3RhbCxcclxuICAgICAgaGlnaFBlcmZvcm1lcnMsXHJcbiAgICAgIG1lZGl1bVBlcmZvcm1lcnMsXHJcbiAgICAgIGxvd1BlcmZvcm1lcnMsXHJcbiAgICAgIGhpZ2hQZXJjZW50YWdlOiB0b3RhbCA+IDAgPyBNYXRoLnJvdW5kKChoaWdoUGVyZm9ybWVycyAvIHRvdGFsKSAqIDEwMCkgOiAwLFxyXG4gICAgICBtZWRpdW1QZXJjZW50YWdlOiB0b3RhbCA+IDAgPyBNYXRoLnJvdW5kKChtZWRpdW1QZXJmb3JtZXJzIC8gdG90YWwpICogMTAwKSA6IDAsXHJcbiAgICAgIGxvd1BlcmNlbnRhZ2U6IHRvdGFsID4gMCA/IE1hdGgucm91bmQoKGxvd1BlcmZvcm1lcnMgLyB0b3RhbCkgKiAxMDApIDogMFxyXG4gICAgfTtcclxuICB9LCBbZmVkZXJhdGlvbnNdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBtaW4taC1zY3JlZW4gZmxleC1jb2wgYmctYmFja2dyb3VuZC81MFwiPlxyXG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cInN0aWNreSB0b3AtMCB6LTQwIGJvcmRlci1iIGJnLWJhY2tncm91bmQvOTUgYmFja2Ryb3AtYmx1ciBzdXBwb3J0cy1bYmFja2Ryb3AtZmlsdGVyXTpiZy1iYWNrZ3JvdW5kLzYwXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBtZDpweC02IGZsZXggaC0xNiBpdGVtcy1jZW50ZXIgcHktNFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxyXG4gICAgICAgICAgICA8TGluayBocmVmPVwiL2Fzc2Vzc21lbnRcIj5cclxuICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJpY29uXCIgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbFwiPlxyXG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgPExheW91dERhc2hib2FyZCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcHJpbWFyeVwiIC8+XHJcbiAgICAgICAgICAgICAgICBBZG1pbiBEYXNoYm9hcmRcclxuICAgICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+QWxsIEZlZGVyYXRpb25zIE92ZXJ2aWV3PC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC1hdXRvIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHNtOmdhcC00XCI+XHJcbiAgICAgICAgICAgIDxCdXR0b24gXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZmV0Y2hGZWRlcmF0aW9ucyh0cnVlKX0gXHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3JlZnJlc2hpbmd9XHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXItMlwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YGgtNCB3LTQgbXItMiAke3JlZnJlc2hpbmcgPyAnYW5pbWF0ZS1zcGluJyA6ICcnfWB9IC8+XHJcbiAgICAgICAgICAgICAgUmVmcmVzaFxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtleHBvcnRUb0NTVn0gdmFyaWFudD1cIm91dGxpbmVcIiBzaXplPVwic21cIiBjbGFzc05hbWU9XCJtci0yXCI+XHJcbiAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgRXhwb3J0IENTVlxyXG4gICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPFRoZW1lVG9nZ2xlIC8+XHJcbiAgICAgICAgICAgIDxVc2VyTmF2IC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9oZWFkZXI+XHJcblxyXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJmbGV4LTEgY29udGFpbmVyIHB5LTYgcHgtNCBtZDpweC02IG1heC13LTd4bCBteC1hdXRvXCI+XHJcbiAgICAgICAgPFRhYnMgdmFsdWU9e2FjdGl2ZVRhYn0gb25WYWx1ZUNoYW5nZT17c2V0QWN0aXZlVGFifSBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC00IGl0ZW1zLXN0YXJ0IHNtOml0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy01IG1kOnctYXV0b1wiPlxyXG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cIm92ZXJ2aWV3XCI+T3ZlcnZpZXc8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImFuYWx5dGljc1wiPkFuYWx5dGljczwvVGFic1RyaWdnZXI+XHJcbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwicWEtc3RhdHNcIj5RJkEgU3RhdHM8L1RhYnNUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInJlcG9ydHNcIj5SZXBvcnRzPC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgICA8VGFic1RyaWdnZXIgdmFsdWU9XCJmZWRlcmF0aW9uc1wiPkZlZGVyYXRpb25zPC9UYWJzVHJpZ2dlcj5cclxuICAgICAgICAgICAgPC9UYWJzTGlzdD5cclxuXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMiBmbGV4LXdyYXBcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMiB0b3AtMi41IGgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cclxuICAgICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBmZWRlcmF0aW9ucy4uLlwiXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWFyY2hUZXJtfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwbC04IHctWzIwMHB4XVwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e2ZpbHRlckJ5fSBvblZhbHVlQ2hhbmdlPXsodmFsdWU6IGFueSkgPT4gc2V0RmlsdGVyQnkodmFsdWUpfT5cclxuICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT1cInctWzEzMHB4XVwiPlxyXG4gICAgICAgICAgICAgICAgICA8RmlsdGVyIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxyXG4gICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwiYWxsXCI+QWxsIFNjb3JlczwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJoaWdoXCI+SGlnaCAoNzUlKyk8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwibWVkaXVtXCI+TWVkaXVtICg1MC03NCUpPC9TZWxlY3RJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImxvd1wiPkxvdyAoJmx0OzUwJSk8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgPC9TZWxlY3Q+XHJcblxyXG4gICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3NvcnRCeX0gb25WYWx1ZUNoYW5nZT17KHZhbHVlOiBhbnkpID0+IHNldFNvcnRCeSh2YWx1ZSl9PlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1bMTIwcHhdXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxyXG4gICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwic2NvcmVcIj5TY29yZTwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJuYW1lXCI+TmFtZTwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJwZXJjZW50YWdlXCI+UGVyY2VudGFnZTwvU2VsZWN0SXRlbT5cclxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJ5b3V0aFwiPllvdXRoICU8L1NlbGVjdEl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgPC9TZWxlY3Q+XHJcblxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTb3J0T3JkZXIoc29ydE9yZGVyID09PSBcImFzY1wiID8gXCJkZXNjXCIgOiBcImFzY1wiKX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7c29ydE9yZGVyID09PSBcImFzY1wiID8gPFNvcnRBc2MgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPFNvcnREZXNjIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPn1cclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJvdmVydmlld1wiIGNsYXNzTmFtZT1cIm10LTBcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgYW5pbWF0ZS1pbiBmYWRlLWluIGR1cmF0aW9uLTMwMFwiPlxyXG4gICAgICAgICAgICAgIHsvKiBUaXRsZSBhbmQgTG9nb3MgU2VjdGlvbiAqL31cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLWluZGlnby01MCBkYXJrOmZyb20tYmx1ZS05NTAvMjAgZGFyazp0by1pbmRpZ28tOTUwLzIwIHJvdW5kZWQtbGcgcC02XCI+XHJcbiAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj5TdXBwb3J0IGZvciBFZmZlY3RpdmUgYW5kIEluY2x1c2l2ZSBUcmFkZSBVbmlvbnMgaW4gQmFuZ2xhZGVzaDwvaDE+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgZ2FwLTggZmxleC13cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPVwiaHR0cHM6Ly9yZXMuY2xvdWRpbmFyeS5jb20vZHJha2N5eXJpL2ltYWdlL3VwbG9hZC9nZXJtYW5fY29vcGVyYXRpb25fYmFuZ2xhZGVzaF9pZTN0YnMucG5nXCIgYWx0PVwiR2VybWFuIENvb3BlcmF0aW9uIEJhbmdsYWRlc2hcIiBjbGFzc05hbWU9XCJoLTE2IHctYXV0b1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPVwiaHR0cHM6Ly9yZXMuY2xvdWRpbmFyeS5jb20vZHJha2N5eXJpL2ltYWdlL3VwbG9hZC9JbnRlcm5hdGlvbmFsX0xhYm91cl9Pcmdhbml6YXRpb25fbHlpeGFkLnBuZ1wiIGFsdD1cIkludGVybmF0aW9uYWwgTGFib3VyIE9yZ2FuaXphdGlvblwiIGNsYXNzTmFtZT1cImgtMTYgdy1hdXRvXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCJodHRwczovL3Jlcy5jbG91ZGluYXJ5LmNvbS9kcmFrY3l5cmkvaW1hZ2UvdXBsb2FkL2dyb3dpbmctdG9nZXRoZXItb3BjX2ppajVmcC5wbmdcIiBhbHQ9XCJHcm93aW5nIFRvZ2V0aGVyIE9QQ1wiIGNsYXNzTmFtZT1cImgtMTYgdy1hdXRvXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGltZyBzcmM9XCJodHRwczovL3Jlcy5jbG91ZGluYXJ5LmNvbS9kcmFrY3l5cmkvaW1hZ2UvdXBsb2FkL2dvdmVybm1lbnQtb2YtdGhlLXBlcm9wbGVzLXJlcHVibGljLW9mLWJhbmdsYWRlc2hfcWdobGtxLnBuZ1wiIGFsdD1cIkdvdmVybm1lbnQgb2YgdGhlIHBlb3BsZSdzIHJlcHVibGljIG9mIEJhbmdsYWRlc2hcIiBjbGFzc05hbWU9XCJoLTE2IHctYXV0b1wiIC8+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgey8qIFF1aWNrIFN0YXRzIEdyaWQgKi99XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02XCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwicGItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEF3YXJkIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIEF2ZXJhZ2UgUGVyZm9ybWFuY2VcclxuICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+e2F2ZXJhZ2VQZXJjZW50YWdlfSU8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPk92ZXJhbGwgYXNzZXNzbWVudCBzY29yZTwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTIgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PXthdmVyYWdlUGVyY2VudGFnZSA+PSA3NSA/IFwiZGVmYXVsdFwiIDogYXZlcmFnZVBlcmNlbnRhZ2UgPj0gNTAgPyBcInNlY29uZGFyeVwiIDogXCJkZXN0cnVjdGl2ZVwifT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2F2ZXJhZ2VQZXJjZW50YWdlID49IDc1ID8gXCJFeGNlbGxlbnRcIiA6IGF2ZXJhZ2VQZXJjZW50YWdlID49IDUwID8gXCJHb29kXCIgOiBcIk5lZWRzIEltcHJvdmVtZW50XCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG5cclxuICAgICAgICAgICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTUwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICBZb3V0aCBFbmdhZ2VtZW50XHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+e2F2ZXJhZ2VZb3V0aFBlcmNlbnRhZ2V9JTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+QXZlcmFnZSB5b3V0aCByZXByZXNlbnRhdGlvbjwvcD5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWluKGF2ZXJhZ2VZb3V0aFBlcmNlbnRhZ2UsIDEwMCl9JWAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGcgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUYXJnZXQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWJsdWUtNTAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIFNjb3JlIFJhbmdlXHJcbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5IaWdoZXN0Ojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyZWVuLTYwMFwiPntoaWdoZXN0UGVyY2VudGFnZX0lPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5Mb3dlc3Q6PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtcmVkLTYwMFwiPntsb3dlc3RQZXJjZW50YWdlfSU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPlJhbmdlOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPntoaWdoZXN0UGVyY2VudGFnZSAtIGxvd2VzdFBlcmNlbnRhZ2V9JTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcblxyXG4gICAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiYW5hbHl0aWNzXCIgY2xhc3NOYW1lPVwibXQtMFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOCBhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICAgICAgPEZlZGVyYXRpb25BbmFseXRpY3NDaGFydFxyXG4gICAgICAgICAgICAgICAgZmVkZXJhdGlvbnM9e2ZlZGVyYXRpb25zfVxyXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRGZWRlcmF0aW9uPXtzZWxlY3RlZEZlZGVyYXRpb259XHJcbiAgICAgICAgICAgICAgICBvblNlbGVjdEZlZGVyYXRpb249e2hhbmRsZUZlZGVyYXRpb25DaGFuZ2V9XHJcbiAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgPFNlY3Rpb25BdmVyYWdlQ2hhcnRcclxuICAgICAgICAgICAgICAgIGF2Z1NlY3Rpb25TY29yZXM9e2F2Z1NlY3Rpb25TY29yZXN9XHJcbiAgICAgICAgICAgICAgICBtaW5TZWN0aW9uU2NvcmVzPXttaW5TZWN0aW9uU2NvcmVzfVxyXG4gICAgICAgICAgICAgICAgbWF4U2VjdGlvblNjb3Jlcz17bWF4U2VjdGlvblNjb3Jlc31cclxuICAgICAgICAgICAgICAgIHNlY3Rpb25MaXN0PXtzZWN0aW9uTGlzdH1cclxuICAgICAgICAgICAgICAgIHNlY3Rpb25NYXhTY29yZXM9e3NlY3Rpb25NYXhTY29yZXN9XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxyXG5cclxuICAgICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cInFhLXN0YXRzXCIgY2xhc3NOYW1lPVwibXQtMFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOCBhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICAgICAgPFFBU3RhdHNDYXJkIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cclxuXHJcbiAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJyZXBvcnRzXCIgY2xhc3NOYW1lPVwibXQtMFwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktOCBhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICAgICAgPFlvdXRoUmVwcmVzZW50YXRpb25DaGFydCBmZWRlcmF0aW9ucz17ZmVkZXJhdGlvbnN9IC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cclxuXHJcbiAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJmZWRlcmF0aW9uc1wiIGNsYXNzTmFtZT1cIm10LTBcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCdWlsZGluZzIgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgRmVkZXJhdGlvbiBEaXJlY3RvcnlcclxuICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgSW50ZXJhY3RpdmUgdGFibGUgc2hvd2luZyBhbGwgZmVkZXJhdGlvbnMgd2l0aCB0aGVpciBwZXJmb3JtYW5jZSBtZXRyaWNzXHJcbiAgICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7WzEsIDIsIDMsIDQsIDVdLm1hcCgoaSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00IGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1tdXRlZCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiBmbGV4LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0xLzRcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0zIGJnLW11dGVkIHJvdW5kZWQgdy0xLzJcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTE2IGJnLW11dGVkIHJvdW5kZWRcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQW5kU29ydGVkRmVkZXJhdGlvbnMubWFwKChmZWRlcmF0aW9uLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8TGluayBocmVmPXtgL2ZlZGVyYXRpb24vJHtmZWRlcmF0aW9uLmlkfWB9IGtleT17ZmVkZXJhdGlvbi5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgcC00IGhvdmVyOnNoYWRvdy1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgY3Vyc29yLXBvaW50ZXIgYW5pbWF0ZS1pbiBmYWRlLWluIHNsaWRlLWluLWZyb20tYm90dG9tLTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6IGAke2luZGV4ICogNTB9bXNgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTIgdy0xMiBiZy1wcmltYXJ5LzEwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdWlsZGluZzIgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXByaW1hcnlcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWxnXCI+e2ZlZGVyYXRpb24ubmFtZX08L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTQgdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlByZXNpZGVudDoge2ZlZGVyYXRpb24ucHJlc2lkZW50X25hbWUgfHwgXCJOL0FcIn08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5TZWNyZXRhcnk6IHtmZWRlcmF0aW9uLnNlY3JldGFyeV9uYW1lIHx8IFwiTi9BXCJ9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDB9JVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmZWRlcmF0aW9uLnRvdGFsU2NvcmUgfHwgMH0ve2ZlZGVyYXRpb24ubWF4U2NvcmUgfHwgMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2VcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChmZWRlcmF0aW9uLnBlcmNlbnRhZ2UgfHwgMCkgPj0gNzUgPyBcImRlZmF1bHRcIiA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDApID49IDUwID8gXCJzZWNvbmRhcnlcIiA6IFwiZGVzdHJ1Y3RpdmVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsoZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDApID49IDc1ID8gXCJIaWdoXCIgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDApID49IDUwID8gXCJNZWRpdW1cIiA6IFwiTG93XCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVkZXJhdGlvbi55b3V0aF9wZXJjZW50YWdlIHx8IFwiTi9BXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPllvdXRoPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RXllIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUHJvZ3Jlc3MgYmFyICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgaC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi01MDAgJHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChmZWRlcmF0aW9uLnBlcmNlbnRhZ2UgfHwgMCkgPj0gNzUgPyAnYmctZ3JlZW4tNTAwJyA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZmVkZXJhdGlvbi5wZXJjZW50YWdlIHx8IDApID49IDUwID8gJ2JnLXllbGxvdy01MDAnIDogJ2JnLXJlZC01MDAnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWluKGZlZGVyYXRpb24ucGVyY2VudGFnZSB8fCAwLCAxMDApfSVgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICApKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRBbmRTb3J0ZWRGZWRlcmF0aW9ucy5sZW5ndGggPT09IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1aWxkaW5nMiBjbGFzc05hbWU9XCJoLTEyIHctMTIgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG14LWF1dG8gbWItNFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+Tm8gZmVkZXJhdGlvbnMgZm91bmQ8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgVHJ5IGFkanVzdGluZyB5b3VyIHNlYXJjaCBvciBmaWx0ZXIgY3JpdGVyaWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvVGFic0NvbnRlbnQ+XHJcbiAgICAgICAgPC9UYWJzPlxyXG5cclxuICAgICAgICB7aXNMb2FkaW5nID8gKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC02IG1kOmdyaWQtY29scy0yIG10LThcIj5cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwic2hhZG93LW1kIGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+TG9hZGluZy4uLjwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMjAgYmctbXV0ZWQgcm91bmRlZFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICAgIDwvQ2FyZD5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICkgOiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ2FwLTYgbWQ6Z3JpZC1jb2xzLTIgbXQtOFwiPlxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNoYWRvdy1sZyBoLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuIGJvcmRlci1tdXRlZC80MFwiPlxyXG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTIgYmctbXV0ZWQvMTBcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC14bCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8QnVpbGRpbmcyIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cclxuICAgICAgICAgICAgICAgICAgTGVhZGVyc2hpcCBPdmVydmlld1xyXG4gICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgICBBdmVyYWdlIGxlYWRlcnNoaXAgZGVtb2dyYXBoaWNzIGFjcm9zcyBhbGwgZmVkZXJhdGlvbnNcclxuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicHQtNlwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAvMTAgcC0yLjUgcm91bmRlZC1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VXNlckNpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BdmVyYWdlIEFnZXM8L2gzPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC0yIG10LTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1tdXRlZC8zMCBwLTIgcm91bmRlZC1tZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+UHJlc2lkZW50PC9wPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXZlcmFnZVByZXNpZGVudEFnZX0geWVhcnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLW11dGVkLzMwIHAtMiByb3VuZGVkLW1kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5TZWNyZXRhcnk8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthdmVyYWdlU2VjcmV0YXJ5QWdlfSB5ZWFyc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcblxyXG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNoYWRvdy1sZyBoLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuIGJvcmRlci1tdXRlZC80MFwiPlxyXG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTIgYmctbXV0ZWQvMTBcIj5cclxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC14bCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cclxuICAgICAgICAgICAgICAgICAgUGVyZm9ybWFuY2UgU3VtbWFyeVxyXG4gICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgICAgICAgICAgICBLZXkgcGVyZm9ybWFuY2UgaW5kaWNhdG9ycyBhY3Jvc3MgYWxsIGZlZGVyYXRpb25zXHJcbiAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB0LTZcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC0zIGJnLWdyZWVuLTUwIGRhcms6YmctZ3JlZW4tOTUwLzIwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+e2F2ZXJhZ2VUb3RhbFNjb3JlfTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkF2ZyBTY29yZTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC0zIGJnLWJsdWUtNTAgZGFyazpiZy1ibHVlLTk1MC8yMCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+e2F2ZXJhZ2VQZXJjZW50YWdlfSU8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5BdmcgUGVyY2VudGFnZTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5IaWdoZXN0IFNjb3JlOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi02MDBcIj57aGlnaGVzdFNjb3JlfSAoe2hpZ2hlc3RQZXJjZW50YWdlfSUpPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TG93ZXN0IFNjb3JlOjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1yZWQtNjAwXCI+e2xvd2VzdFNjb3JlfSAoe2xvd2VzdFBlcmNlbnRhZ2V9JSk8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5Zb3V0aCBFbmdhZ2VtZW50Ojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1wdXJwbGUtNjAwXCI+e2F2ZXJhZ2VZb3V0aFBlcmNlbnRhZ2V9JTwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApfVxyXG4gICAgICA8L21haW4+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTWVtbyIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkRGVzY3JpcHRpb24iLCJVc2VyTmF2IiwiVGhlbWVUb2dnbGUiLCJMaW5rIiwiQnV0dG9uIiwiSW5wdXQiLCJCYWRnZSIsIlRhYnMiLCJUYWJzQ29udGVudCIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJBcnJvd0xlZnQiLCJCdWlsZGluZzIiLCJVc2VyQ2lyY2xlIiwiU2VhcmNoIiwiRmlsdGVyIiwiRG93bmxvYWQiLCJCYXJDaGFydDMiLCJVc2VycyIsIkF3YXJkIiwiVGFyZ2V0IiwiUmVmcmVzaEN3IiwiRXllIiwiU29ydEFzYyIsIlNvcnREZXNjIiwiTGF5b3V0RGFzaGJvYXJkIiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJ1c2VBdXRoIiwiRmVkZXJhdGlvbkFuYWx5dGljc0NoYXJ0IiwiU2VjdGlvbkF2ZXJhZ2VDaGFydCIsIllvdXRoUmVwcmVzZW50YXRpb25DaGFydCIsIlFBU3RhdHNDYXJkIiwidG9hc3QiLCJBbGxGZWRlcmF0aW9uUGFnZSIsInVzZXIiLCJmZWRlcmF0aW9ucyIsInNldEZlZGVyYXRpb25zIiwic2VsZWN0ZWRGZWRlcmF0aW9uIiwic2V0U2VsZWN0ZWRGZWRlcmF0aW9uIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzb3J0QnkiLCJzZXRTb3J0QnkiLCJzb3J0T3JkZXIiLCJzZXRTb3J0T3JkZXIiLCJmaWx0ZXJCeSIsInNldEZpbHRlckJ5IiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwicmVmcmVzaGluZyIsInNldFJlZnJlc2hpbmciLCJhdmVyYWdlUHJlc2lkZW50QWdlIiwic2V0QXZlcmFnZVByZXNpZGVudEFnZSIsImF2ZXJhZ2VTZWNyZXRhcnlBZ2UiLCJzZXRBdmVyYWdlU2VjcmV0YXJ5QWdlIiwiYXZlcmFnZVRvdGFsU2NvcmUiLCJzZXRBdmVyYWdlVG90YWxTY29yZSIsImF2ZXJhZ2VQZXJjZW50YWdlIiwic2V0QXZlcmFnZVBlcmNlbnRhZ2UiLCJoaWdoZXN0U2NvcmUiLCJzZXRIaWdoZXN0U2NvcmUiLCJsb3dlc3RTY29yZSIsInNldExvd2VzdFNjb3JlIiwiaGlnaGVzdFBlcmNlbnRhZ2UiLCJzZXRIaWdoZXN0UGVyY2VudGFnZSIsImxvd2VzdFBlcmNlbnRhZ2UiLCJzZXRMb3dlc3RQZXJjZW50YWdlIiwiYXZlcmFnZVlvdXRoUGVyY2VudGFnZSIsInNldEF2ZXJhZ2VZb3V0aFBlcmNlbnRhZ2UiLCJhdmdTZWN0aW9uU2NvcmVzIiwic2V0QXZnU2VjdGlvblNjb3JlcyIsIm1pblNlY3Rpb25TY29yZXMiLCJzZXRNaW5TZWN0aW9uU2NvcmVzIiwibWF4U2VjdGlvblNjb3JlcyIsInNldE1heFNlY3Rpb25TY29yZXMiLCJzZWN0aW9uTGlzdCIsInNldFNlY3Rpb25MaXN0IiwiY3VycmVudEZlZGVyYXRpb24iLCJzZXRDdXJyZW50RmVkZXJhdGlvbiIsImN1cnJlbnRTZWN0aW9uU2NvcmVzIiwic2V0Q3VycmVudFNlY3Rpb25TY29yZXMiLCJmaWx0ZXJlZEFuZFNvcnRlZEZlZGVyYXRpb25zIiwiZmlsdGVyZWQiLCJmaWx0ZXIiLCJmZWQiLCJtYXRjaGVzU2VhcmNoIiwibmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJwcmVzaWRlbnRfbmFtZSIsInNlY3JldGFyeV9uYW1lIiwicGVyY2VudGFnZSIsIm1hdGNoZXNGaWx0ZXIiLCJzb3J0IiwiYSIsImIiLCJhVmFsdWUiLCJiVmFsdWUiLCJ0b3RhbFNjb3JlIiwiZ2V0WW91dGhQZXJjZW50YWdlVmFsdWUiLCJ5b3V0aF9wZXJjZW50YWdlIiwibG9jYWxlQ29tcGFyZSIsInlvdXRoUGVyY2VudGFnZSIsInNlY3Rpb25NYXhTY29yZXMiLCJpZCIsInRpdGxlIiwiZmV0Y2hGZWRlcmF0aW9ucyIsInNob3dSZWZyZXNoVG9hc3QiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJFcnJvciIsImRhdGEiLCJqc29uIiwibGVuZ3RoIiwicHJlc2lkZW50QWdlcyIsInByZXNpZGVudF9hZ2UiLCJtYXAiLCJzZWNyZXRhcnlBZ2VzIiwic2VjcmV0YXJ5X2FnZSIsImF2Z1ByZXNpZGVudEFnZSIsIk1hdGgiLCJyb3VuZCIsInJlZHVjZSIsInN1bSIsImFnZSIsImF2Z1NlY3JldGFyeUFnZSIsImF2Z1RvdGFsU2NvcmUiLCJhdmdQZXJjZW50YWdlIiwiYXZnWW91dGhQZXJjZW50YWdlIiwic3VjY2VzcyIsImVycm9yIiwiY29uc29sZSIsImhhbmRsZUZlZGVyYXRpb25DaGFuZ2UiLCJ2YWx1ZSIsInNlbGVjdGVkIiwiZmluZCIsInNlY3Rpb25TY29yZXMiLCJleHBvcnRUb0NTViIsImhlYWRlcnMiLCJjc3ZEYXRhIiwiY3N2Q29udGVudCIsInJvdyIsImNlbGwiLCJqb2luIiwiYmxvYiIsIkJsb2IiLCJ0eXBlIiwidXJsIiwiVVJMIiwiY3JlYXRlT2JqZWN0VVJMIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJjbGljayIsInJldm9rZU9iamVjdFVSTCIsInBlcmZvcm1hbmNlTWV0cmljcyIsInRvdGFsIiwiaGlnaFBlcmZvcm1lcnMiLCJmIiwibWVkaXVtUGVyZm9ybWVycyIsImxvd1BlcmZvcm1lcnMiLCJoaWdoUGVyY2VudGFnZSIsIm1lZGl1bVBlcmNlbnRhZ2UiLCJsb3dQZXJjZW50YWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaGVhZGVyIiwidmFyaWFudCIsInNpemUiLCJoMSIsInAiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJtYWluIiwib25WYWx1ZUNoYW5nZSIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwiaW1nIiwic3JjIiwiYWx0Iiwic3R5bGUiLCJ3aWR0aCIsIm1pbiIsInNwYW4iLCJvblNlbGVjdEZlZGVyYXRpb24iLCJpIiwiZmVkZXJhdGlvbiIsImluZGV4IiwiYW5pbWF0aW9uRGVsYXkiLCJoMyIsIm1heFNjb3JlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/all-federation/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/dashboard/QAStatsCard.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/QAStatsCard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QAStatsCard: () => (/* binding */ QAStatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ QAStatsCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst COLORS = [\n    '#0088FE',\n    '#00C49F',\n    '#FFBB28',\n    '#FF8042',\n    '#8884D8',\n    '#82CA9D',\n    '#FFC658',\n    '#FF7C7C',\n    '#8DD1E1',\n    '#D084D0',\n    '#87D068',\n    '#FFB347'\n];\nconst sectionTitles = {\n    3: \"Leadership\",\n    4: \"Organizational Structure\",\n    5: \"Management\",\n    6: \"Worker Participation\",\n    7: \"Culture and Gender\",\n    8: \"Collective Bargaining\",\n    9: \"Member Engagement\",\n    10: \"Financial Stability\",\n    11: \"Audit & Compliance\"\n};\nfunction QAStatsCard() {\n    _s();\n    const [qaStats, setQAStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedSection, setSelectedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bar');\n    const [expandedQuestions, setExpandedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QAStatsCard.useEffect\": ()=>{\n            fetchQAStats();\n        }\n    }[\"QAStatsCard.useEffect\"], []);\n    const fetchQAStats = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch('/api/qa-stats');\n            if (!response.ok) {\n                throw new Error('Failed to fetch Q&A statistics');\n            }\n            const data = await response.json();\n            setQAStats(data);\n        } catch (error) {\n            console.error('Error fetching Q&A stats:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load Q&A statistics\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleQuestionExpansion = (questionField)=>{\n        const newExpanded = new Set(expandedQuestions);\n        if (newExpanded.has(questionField)) {\n            newExpanded.delete(questionField);\n        } else {\n            newExpanded.add(questionField);\n        }\n        setExpandedQuestions(newExpanded);\n    };\n    const renderBarChart = (questionData)=>{\n        const data = questionData.responses.map((response)=>({\n                answer: response.answer.length > 20 ? response.answer.substring(0, 20) + '...' : response.answer,\n                fullAnswer: response.answer,\n                count: response.count,\n                percentage: response.percentage\n            }));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.BarChart, {\n                data: data,\n                margin: {\n                    top: 20,\n                    right: 30,\n                    left: 20,\n                    bottom: 60\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                        strokeDasharray: \"3 3\",\n                        stroke: \"#e5e7eb\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                        dataKey: \"answer\",\n                        angle: -45,\n                        textAnchor: \"end\",\n                        height: 80,\n                        fontSize: 12,\n                        interval: 0\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                        label: {\n                            value: 'Number of Federations',\n                            angle: -90,\n                            position: 'insideLeft'\n                        },\n                        fontSize: 12\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        formatter: (value, name, props)=>[\n                                \"\".concat(value, \" federations (\").concat(props.payload.percentage.toFixed(1), \"%)\"),\n                                'Responses'\n                            ],\n                        labelFormatter: (label, payload)=>{\n                            var _payload__payload, _payload_;\n                            return (payload === null || payload === void 0 ? void 0 : (_payload_ = payload[0]) === null || _payload_ === void 0 ? void 0 : (_payload__payload = _payload_.payload) === null || _payload__payload === void 0 ? void 0 : _payload__payload.fullAnswer) || label;\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                        dataKey: \"count\",\n                        fill: \"#0088FE\",\n                        radius: [\n                            4,\n                            4,\n                            0,\n                            0\n                        ]\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPieChart = (questionData)=>{\n        const data = questionData.responses.map((response, index)=>({\n                name: response.answer.length > 15 ? response.answer.substring(0, 15) + '...' : response.answer,\n                fullName: response.answer,\n                value: response.count,\n                percentage: response.percentage\n            }));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.PieChart, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Pie, {\n                        data: data,\n                        cx: \"50%\",\n                        cy: \"50%\",\n                        labelLine: false,\n                        label: (param)=>{\n                            let { percentage } = param;\n                            return \"\".concat(percentage.toFixed(1), \"%\");\n                        },\n                        outerRadius: 80,\n                        fill: \"#8884d8\",\n                        dataKey: \"value\",\n                        children: data.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Cell, {\n                                fill: COLORS[index % COLORS.length]\n                            }, \"cell-\".concat(index), false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        formatter: (value, name, props)=>[\n                                \"\".concat(value, \" federations (\").concat(props.payload.percentage.toFixed(1), \"%)\"),\n                                props.payload.fullName\n                            ]\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Legend, {\n                        formatter: (value, entry)=>entry.payload.fullName,\n                        wrapperStyle: {\n                            fontSize: '12px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                \"Q&A Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"Loading question and answer statistics...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            1,\n                            2,\n                            3\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-20 bg-muted rounded-md animate-pulse\"\n                            }, i, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    const currentSectionData = qaStats.find((section)=>section.sectionNumber === selectedSection);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-xl flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Q&A Statistics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"Analysis of how federations answered assessment questions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                value: chartType,\n                                onValueChange: (value)=>setChartType(value),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid grid-cols-2 w-32\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"bar\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Bar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"pie\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pie\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium mb-3 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Select Assessment Section\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-2\",\n                                children: Object.entries(sectionTitles).map((param)=>{\n                                    let [sectionNum, title] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: selectedSection === parseInt(sectionNum) ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedSection(parseInt(sectionNum)),\n                                        className: \"text-xs\",\n                                        children: sectionNum\n                                    }, sectionNum, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    currentSectionData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Section \",\n                                            selectedSection,\n                                            \": \",\n                                            currentSectionData.sectionTitle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        children: [\n                                            currentSectionData.questions.length,\n                                            \" questions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: currentSectionData.questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-l-4 border-l-primary/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: [\n                                                                        \"Q\",\n                                                                        index + 1,\n                                                                        \": \",\n                                                                        question.question\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                    className: \"flex items-center gap-4 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 283,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                question.totalResponses,\n                                                                                \" responses\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 287,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                question.responses.length,\n                                                                                \" unique answers\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>toggleQuestionExpansion(question.field),\n                                                            children: expandedQuestions.has(question.field) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            expandedQuestions.has(question.field) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: question.responses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: chartType === 'bar' ? renderBarChart(question) : renderPieChart(question)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\",\n                                                            children: question.responses.map((response, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-lg border bg-muted/20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-3 h-3 rounded-full\",\n                                                                                    style: {\n                                                                                        backgroundColor: COLORS[idx % COLORS.length]\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 323,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        response.count,\n                                                                                        \" (\",\n                                                                                        response.percentage.toFixed(1),\n                                                                                        \"%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: response.answer\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                                            children: [\n                                                                                \"Federations: \",\n                                                                                response.federations.join(', ')\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"No responses available for this question\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, question.field, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No Data Available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"No statistics available for Section \",\n                                    selectedSection\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(QAStatsCard, \"WPXcFe8wDyl3krN0KffCtb+K0ro=\");\n_c = QAStatsCard;\nvar _c;\n$RefreshReg$(_c, \"QAStatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/QAStatsCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileText)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.446.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"FileText\", [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 9H8\",\n            key: \"b1mrlr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 13H8\",\n            key: \"t4e002\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 17H8\",\n            key: \"z1uh3a\"\n        }\n    ]\n]);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ })

});