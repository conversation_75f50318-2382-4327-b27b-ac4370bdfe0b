/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6c07ae23b386\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFx0dWNhdFxcdHVjYXRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2YzA3YWUyM2IzODZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./components/Footer.tsx\");\n/* harmony import */ var _components_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AuthContext */ \"(rsc)/./components/AuthContext.tsx\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./components/ThemeProvider.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'TUCAT',\n    description: 'Trade Union Capacity Assessment Tool by itoc International'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"system\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                            position: \"top-right\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/AuthContext.tsx":
/*!************************************!*\
  !*** ./components/AuthContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\tucat\\tucat\\components\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\tucat\\tucat\\components\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(rsc)/./components/Footer.tsx":
/*!*******************************!*\
  !*** ./components/Footer.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Globe,Mail,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Globe,Mail,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Globe,Mail,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,Globe,Mail,Phone!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/separator */ \"(rsc)/./components/ui/separator.tsx\");\n\n\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"mt-auto bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border-t transition-colors duration-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl px-6 py-8 md:py-12 lg:py-16 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-8 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 dark:text-white\",\n                                            children: \"Growing Together OPC\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 12,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm leading-6 text-gray-600 dark:text-gray-300\",\n                                            children: \"A research, capacity-development, knowledge-management, and policy-advocacy\\xa0consulting\\xa0firm.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 13,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 11,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                            children: \"Thank you for using our assessment tool!\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                            children: \"We are available to extend our cooperation should you require any further assistance.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-md font-semibold text-gray-900 dark:text-white\",\n                                                    children: \"Contact Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 28,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                            children: \"M. Anowar Hossain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                            lineNumber: 30,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                                                            children: \"Executive Chairman\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                            lineNumber: 31,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 29,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 27,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"http://www.growingtogether-int.org\",\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 40,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"www.growingtogether-int.org\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 34,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"tel:+8801714039746\",\n                                                                className: \"flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                        lineNumber: 48,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"01714039746\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                lineNumber: 44,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"mailto:<EMAIL>\",\n                                                            className: \"flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_Globe_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                                    lineNumber: 56,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"<EMAIL>\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 52,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 26,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 md:pl-8 md:border-l md:border-gray-200 md:dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-md font-medium text-gray-900 dark:text-white\",\n                                                    children: \"Our sincere gratitude\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png\",\n                                                    alt: \"German Cooperation Bangladesh\",\n                                                    className: \"h-18 w-auto mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png\",\n                                                    alt: \"International Labour Organization\",\n                                                    className: \"h-18 w-auto mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png\",\n                                                    alt: \"Growing Together OPC\",\n                                                    className: \"h-18 w-auto mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png\",\n                                                    alt: \"Government of the people's republic of Bangladesh\",\n                                                    className: \"h-18 w-auto mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_1__.Separator, {\n                    className: \"my-6 md:my-8\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Growing Together. All rights reserved. Developed by itoc International.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\Footer.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ThemeProvider.tsx":
/*!**************************************!*\
  !*** ./components/ThemeProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\tucat\\tucat\\components\\ThemeProvider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Separator: () => (/* binding */ Separator)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Separator = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\tucat\\tucat\\components\\ui\\separator.tsx",
"Separator",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\tucat\\\\tucat\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5Cui%5C%5Cseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5Cui%5C%5Cseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/AuthContext.tsx */ \"(rsc)/./components/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ThemeProvider.tsx */ \"(rsc)/./components/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/separator.tsx */ \"(rsc)/./components/ui/separator.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5Cui%5C%5Cseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/AuthContext.tsx":
/*!************************************!*\
  !*** ./components/AuthContext.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(ssr)/./lib/types.ts\");\n/* harmony import */ var _lib_upstash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/upstash */ \"(ssr)/./lib/upstash.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        isAuthenticated: false,\n        isLoading: true,\n        error: null\n    });\n    // Check if user is authenticated on initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const checkUserAuth = {\n                \"AuthProvider.useEffect.checkUserAuth\": async ()=>{\n                    try {\n                        const storedUser = localStorage.getItem('user');\n                        if (storedUser) {\n                            let user;\n                            try {\n                                user = JSON.parse(storedUser);\n                            } catch (parseError) {\n                                console.error('Error parsing user data from localStorage:', parseError);\n                                localStorage.removeItem('user'); // Remove invalid data\n                                throw new Error('Invalid user data format');\n                            }\n                            if (!user || !user.id) {\n                                localStorage.removeItem('user'); // Remove invalid data\n                                throw new Error('Invalid user data structure');\n                            }\n                            // Verify user exists in database\n                            const dbUser = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)({\n                                \"AuthProvider.useEffect.checkUserAuth\": ()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.get(`user:${user.id}`)\n                            }[\"AuthProvider.useEffect.checkUserAuth\"]);\n                            if (dbUser) {\n                                // Parse the database user to ensure we have the latest data\n                                let parsedDbUser;\n                                try {\n                                    // Check if dbUser is already an object or needs to be parsed\n                                    if (typeof dbUser === 'object' && !Array.isArray(dbUser)) {\n                                        parsedDbUser = dbUser;\n                                    } else if (typeof dbUser === 'string') {\n                                        parsedDbUser = JSON.parse(dbUser);\n                                    } else {\n                                        throw new Error('Invalid database user data format');\n                                    }\n                                } catch (parseError) {\n                                    console.error('Error parsing user data from database:', parseError);\n                                    throw new Error('Invalid database user data format');\n                                }\n                                setAuthState({\n                                    user,\n                                    isAuthenticated: true,\n                                    isLoading: false,\n                                    error: null\n                                });\n                                return;\n                            }\n                        }\n                        // No valid user found\n                        setAuthState({\n                            user: null,\n                            isAuthenticated: false,\n                            isLoading: false,\n                            error: null\n                        });\n                    } catch (error) {\n                        console.error('Auth check error:', error);\n                        localStorage.removeItem('user'); // Clean up potentially corrupted data\n                        setAuthState({\n                            user: null,\n                            isAuthenticated: false,\n                            isLoading: false,\n                            error: error instanceof Error ? error.message : 'Authentication check failed'\n                        });\n                    }\n                }\n            }[\"AuthProvider.useEffect.checkUserAuth\"];\n            checkUserAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            // In a real app, we would hash the password before comparing\n            const users = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.get('users'));\n            let userList = [];\n            try {\n                // Check if users is already an object or needs to be parsed\n                if (users && typeof users === 'string') {\n                    userList = JSON.parse(users);\n                } else if (users && typeof users === 'object') {\n                    // Ensure userList is an array\n                    userList = Array.isArray(users) ? users : [];\n                }\n                // Final check to ensure userList is always an array\n                if (!Array.isArray(userList)) {\n                    console.error('userList is not an array:', userList);\n                    userList = [];\n                }\n            } catch (parseError) {\n                console.error('Error parsing users data:', parseError);\n                throw new Error('Error retrieving user data');\n            }\n            const user = userList.find((u)=>u.email === email);\n            if (!user || user.password !== password) {\n                throw new Error('Invalid email or password');\n            }\n            // Remove password from user object before storing in state\n            const { password: _, ...userWithoutPassword } = user;\n            try {\n                localStorage.setItem('user', JSON.stringify(userWithoutPassword));\n                // Also set a cookie for server-side auth checks\n                document.cookie = `user=${JSON.stringify(userWithoutPassword)}; path=/; max-age=86400`;\n            } catch (storageError) {\n                console.error('Error storing user data in localStorage:', storageError);\n            // Continue with login even if localStorage fails\n            }\n            setAuthState({\n                user: userWithoutPassword,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Logged in successfully');\n            // Ensure state is updated before navigation\n            await new Promise((resolve)=>{\n                setAuthState((prev)=>{\n                    resolve(null);\n                    return {\n                        user: userWithoutPassword,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        error: null\n                    };\n                });\n            });\n            // Set a cookie for server-side auth checks\n            document.cookie = `user=${JSON.stringify(userWithoutPassword)}; path=/; max-age=86400; SameSite=Strict`;\n            // Redirect based on user role and email\n            if (userWithoutPassword.email === '<EMAIL>' || userWithoutPassword.role === _lib_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.ADMIN) {\n                // Admin users go directly to admin dashboard\n                window.location.href = '/all-federation';\n            } else {\n                // Regular users go to assessment\n                window.location.href = '/assessment';\n            }\n        } catch (error) {\n            console.error('Login error:', error);\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: error.message || 'Login failed'\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.message || 'Login failed');\n        }\n    };\n    const register = async (username, email, password, federation_id, role)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            // Check if email already exists\n            const users = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.get('users'));\n            let userList = [];\n            try {\n                // Check if users is already an object or needs to be parsed\n                if (users && typeof users === 'string') {\n                    userList = JSON.parse(users);\n                } else if (users && typeof users === 'object') {\n                    // Ensure userList is an array\n                    userList = Array.isArray(users) ? users : [];\n                }\n                // Final check to ensure userList is always an array\n                if (!Array.isArray(userList)) {\n                    console.error('userList is not an array:', userList);\n                    userList = [];\n                }\n            } catch (parseError) {\n                console.error('Error parsing users data:', parseError);\n                throw new Error('Error retrieving user data');\n            }\n            if (userList.some((u)=>u.email === email)) {\n                throw new Error('Email already in use');\n            }\n            // Validate role - only allow FEDERATION_MEMBER for regular registration\n            // In a real app, ADMIN and FEDERATION_ADMIN roles would be assigned through a separate process\n            if (role !== _lib_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.FEDERATION_MEMBER && role !== _lib_types__WEBPACK_IMPORTED_MODULE_2__.UserRole.GUEST) {\n                throw new Error('Invalid role selection');\n            }\n            // Federation selection is now optional during registration\n            // Users will select their federation in Section I\n            // Create new user\n            const newUser = {\n                id: `user_${Date.now()}_${Math.random().toString(36).slice(2)}`,\n                username,\n                email,\n                password,\n                federation_id,\n                role,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n            // Add user to list and save\n            userList.push(newUser);\n            await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.set('users', JSON.stringify(userList)));\n            // Also store individual user record for faster lookups\n            await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.set(`user:${newUser.id}`, JSON.stringify(newUser)));\n            // Remove password from user object before storing in state\n            const { password: _, ...userWithoutPassword } = newUser;\n            try {\n                // Ensure we're storing a serializable object\n                const userToStore = JSON.parse(JSON.stringify(userWithoutPassword));\n                localStorage.setItem('user', JSON.stringify(userToStore));\n            } catch (storageError) {\n                console.error('Error storing user data in localStorage:', storageError);\n            // Continue with registration even if localStorage fails\n            }\n            setAuthState({\n                user: userWithoutPassword,\n                isAuthenticated: true,\n                isLoading: false,\n                error: null\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Registration successful');\n            // Set a cookie for server-side auth checks\n            document.cookie = `user=${JSON.stringify(userWithoutPassword)}; path=/; max-age=86400; SameSite=Strict`;\n            // Use window.location.href for more reliable navigation after registration\n            window.location.href = '/assessment';\n        } catch (error) {\n            console.error('Registration error:', error);\n            setAuthState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: error.message || 'Registration failed'\n                }));\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(error.message || 'Registration failed');\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        setAuthState({\n            user: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null\n        });\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Logged out successfully');\n        router.push('/');\n    };\n    const checkAuth = async ()=>{\n        if (authState.isAuthenticated && authState.user) {\n            return true;\n        }\n        try {\n            const storedUser = localStorage.getItem('user');\n            if (storedUser) {\n                let user;\n                try {\n                    user = JSON.parse(storedUser);\n                } catch (parseError) {\n                    console.error('Error parsing user data from localStorage:', parseError);\n                    localStorage.removeItem('user'); // Remove invalid data\n                    return false;\n                }\n                if (!user || !user.id) {\n                    console.error('Invalid user data structure:', user);\n                    localStorage.removeItem('user'); // Remove invalid data\n                    return false;\n                }\n                // Verify user exists in database\n                const dbUser = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.get(`user:${user.id}`));\n                // Check if dbUser exists and is valid\n                if (dbUser && (typeof dbUser === 'object' || typeof dbUser === 'string')) {\n                    // Parse the database user to ensure we have the latest role information\n                    let parsedDbUser;\n                    try {\n                        // Check if dbUser is already an object or needs to be parsed\n                        if (typeof dbUser === 'object' && !Array.isArray(dbUser)) {\n                            parsedDbUser = dbUser;\n                        } else if (typeof dbUser === 'string') {\n                            parsedDbUser = JSON.parse(dbUser);\n                        } else {\n                            throw new Error('Invalid database user data format');\n                        }\n                    } catch (parseError) {\n                        console.error('Error parsing user data from database:', parseError);\n                        return false;\n                    }\n                    if (!parsedDbUser || typeof parsedDbUser !== 'object') {\n                        console.error('Invalid database user data structure:', parsedDbUser);\n                        return false;\n                    }\n                    if (!parsedDbUser || typeof parsedDbUser !== 'object') {\n                        console.error('Invalid database user data structure:', parsedDbUser);\n                        return false;\n                    }\n                    // Use the database user's role but keep the user object from localStorage\n                    // to avoid exposing the password hash\n                    const updatedUser = {\n                        ...user,\n                        role: parsedDbUser.role,\n                        federation_id: parsedDbUser.federation_id\n                    };\n                    setAuthState({\n                        user: updatedUser,\n                        isAuthenticated: true,\n                        isLoading: false,\n                        error: null\n                    });\n                    // Update localStorage with the latest user data\n                    try {\n                        // Ensure we're storing a serializable object\n                        const userToStore = JSON.parse(JSON.stringify(updatedUser));\n                        localStorage.setItem('user', JSON.stringify(userToStore));\n                    } catch (serializeError) {\n                        console.error('Error serializing user data:', serializeError);\n                    // Continue with authentication even if storage fails\n                    }\n                    return true;\n                }\n            }\n            return false;\n        } catch (error) {\n            console.error('Auth check error:', error);\n            localStorage.removeItem('user'); // Clean up potentially corrupted data\n            return false;\n        }\n    };\n    const getCurrentFederationId = ()=>{\n        return authState.user?.federation_id || null;\n    };\n    const updateUserFederation = async (federationId)=>{\n        if (!authState.user || !authState.user.id) {\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('You must be logged in to update your federation');\n            return false;\n        }\n        try {\n            // Get the current user from the database\n            const dbUser = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.get(`user:${authState.user.id}`));\n            if (!dbUser) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('User not found in database');\n                return false;\n            }\n            // Parse the user data\n            let userData;\n            try {\n                if (typeof dbUser === 'object' && !Array.isArray(dbUser)) {\n                    userData = dbUser;\n                } else if (typeof dbUser === 'string') {\n                    userData = JSON.parse(dbUser);\n                } else {\n                    throw new Error('Invalid database user data format');\n                }\n            } catch (parseError) {\n                console.error('Error parsing user data from database:', parseError);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Error updating federation');\n                return false;\n            }\n            // Update the federation_id\n            userData.federation_id = federationId;\n            userData.updated_at = new Date().toISOString();\n            // Save the updated user back to the database\n            await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.set(`user:${authState.user.id}`, JSON.stringify(userData)));\n            // Also update the user in the users list\n            const users = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.get('users'));\n            let userList = [];\n            try {\n                if (users && typeof users === 'string') {\n                    userList = JSON.parse(users);\n                } else if (users && typeof users === 'object') {\n                    userList = Array.isArray(users) ? users : [];\n                }\n            } catch (parseError) {\n                console.error('Error parsing users data:', parseError);\n            }\n            if (Array.isArray(userList)) {\n                const updatedUserList = userList.map((u)=>{\n                    if (u.id === authState.user.id) {\n                        return {\n                            ...u,\n                            federation_id: federationId,\n                            updated_at: new Date().toISOString()\n                        };\n                    }\n                    return u;\n                });\n                await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_3__.redis.set('users', JSON.stringify(updatedUserList)));\n            }\n            // Update the user in the auth state\n            const updatedUser = {\n                ...authState.user,\n                federation_id: federationId\n            };\n            setAuthState({\n                ...authState,\n                user: updatedUser\n            });\n            // Update localStorage\n            try {\n                localStorage.setItem('user', JSON.stringify(updatedUser));\n            } catch (storageError) {\n                console.error('Error storing user data in localStorage:', storageError);\n            }\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('Federation updated successfully');\n            return true;\n        } catch (error) {\n            console.error('Error updating federation:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('Failed to update federation');\n            return false;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            ...authState,\n            login,\n            register,\n            logout,\n            checkAuth,\n            getCurrentFederationId,\n            updateUserFederation\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\AuthContext.tsx\",\n        lineNumber: 471,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ThemeProvider.tsx":
/*!**************************************!*\
  !*** ./components/ThemeProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRWtFO0FBRzNELFNBQVNBLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsiRDpcXHR1Y2F0XFx0dWNhdFxcY29tcG9uZW50c1xcVGhlbWVQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiO1xyXG5pbXBvcnQgeyB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gXCJuZXh0LXRoZW1lcy9kaXN0L3R5cGVzXCI7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcclxuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj47XHJcbn1cclxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/separator.tsx":
/*!*************************************!*\
  !*** ./components/ui/separator.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = 'horizontal', decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('shrink-0 bg-border', orientation === 'horizontal' ? 'h-[1px] w-full' : 'h-full w-[1px]', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\ui\\\\separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/types.ts":
/*!**********************!*\
  !*** ./lib/types.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SCORE_MESSAGES: () => (/* binding */ SCORE_MESSAGES),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\nconst SCORE_MESSAGES = {\n    excellent: \"Excellent! Your federation demonstrates strong organizational capacity and effective leadership.\",\n    good: \"Good progress! Your federation shows promising development with room for strategic improvements.\",\n    fair: \"Fair standing. Consider focusing on key areas for development to strengthen your federation.\",\n    needsImprovement: \"Your federation would benefit from focused capacity building in several key areas.\"\n};\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"ADMIN\"] = \"admin\";\n    UserRole[\"FEDERATION_ADMIN\"] = \"federation_admin\";\n    UserRole[\"FEDERATION_MEMBER\"] = \"federation_member\";\n    UserRole[\"GUEST\"] = \"guest\";\n    return UserRole;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/types.ts\n");

/***/ }),

/***/ "(ssr)/./lib/upstash.ts":
/*!************************!*\
  !*** ./lib/upstash.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   safeRedisOperation: () => (/* binding */ safeRedisOperation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(ssr)/./node_modules/@upstash/redis/nodejs.mjs\");\n\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: \"https://ample-bear-14398.upstash.io\",\n    token: \"ATg-AAIjcDExMDY2ODQwYjIwNDI0MWQxODg4MjExMzg3YTMzYTRhNnAxMA\"\n});\n// Helper function to handle Redis operations with error checking\nasync function safeRedisOperation(operation, fallback) {\n    try {\n        const result = await operation();\n        if (result === null && fallback !== undefined) {\n            return fallback;\n        }\n        if (result === null) {\n            return {};\n        }\n        return result;\n    } catch (error) {\n        if (fallback !== undefined) {\n            console.error('Redis operation failed, using fallback:', error);\n            return fallback;\n        }\n        if (error.message?.includes('NOPERM')) {\n            console.error('Redis authentication failed:', error);\n            throw new Error('Database access denied. Please check your credentials.');\n        }\n        if (error.message?.includes('connect')) {\n            console.error('Redis connection error:', error);\n            throw new Error('Failed to connect to database. Please try again.');\n        }\n        if (error.message?.includes('JSON')) {\n            console.error('Redis JSON parsing error:', error);\n            throw new Error('Error retrieving user data. Invalid data format.');\n        }\n        console.error('Redis operation error:', error);\n        throw new Error('Error retrieving user data. Please try again.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/upstash.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ0o7QUFFbEMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcdHVjYXRcXHR1Y2F0XFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gJ2Nsc3gnO1xyXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSAndGFpbHdpbmQtbWVyZ2UnO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XHJcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcclxufVxyXG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5Cui%5C%5Cseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5Cui%5C%5Cseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/AuthContext.tsx */ \"(ssr)/./components/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ThemeProvider.tsx */ \"(ssr)/./components/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/separator.tsx */ \"(ssr)/./components/ui/separator.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Ccomponents%5C%5Cui%5C%5Cseparator.tsx%22%2C%22ids%22%3A%5B%22Separator%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Ctucat%5C%5Ctucat%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();