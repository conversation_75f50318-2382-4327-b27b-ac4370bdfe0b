/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/federations/route";
exports.ids = ["app/api/federations/route"];
exports.modules = {

/***/ "(rsc)/./app/api/federations/route.ts":
/*!**************************************!*\
  !*** ./app/api/federations/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_upstash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/upstash */ \"(rsc)/./lib/upstash.ts\");\n\n\nasync function GET() {\n    try {\n        // Get all federation keys with proper pattern matching\n        const keys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys('federation:*'));\n        if (!Array.isArray(keys)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                federations: []\n            });\n        }\n        // Fetch all federation data\n        const federationsRaw = await Promise.all(keys.map((key)=>(0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key))));\n        // Parse federation data and ensure we keep all valid entries\n        const federations = federationsRaw.map((item)=>{\n            if (!item) return null;\n            if (typeof item === 'string') {\n                try {\n                    return JSON.parse(item);\n                } catch  {\n                    return null;\n                }\n            }\n            return item;\n        }).filter((fed)=>fed !== null && typeof fed === 'object' && 'id' in fed);\n        // Helper function to convert youth percentage string to numeric value for sorting\n        const getYouthPercentageValue = (percentage)=>{\n            switch(percentage){\n                case \"above-75%\":\n                    return 87.5;\n                case \"51-75%\":\n                    return 63;\n                case \"26-50%\":\n                    return 38;\n                case \"0-25%\":\n                    return 12.5;\n                default:\n                    return 0;\n            }\n        };\n        const sectionCount = 11;\n        const sectionScoresAll = {};\n        for(let i = 1; i <= sectionCount; i++){\n            sectionScoresAll[i] = {\n                total: 0,\n                count: 0,\n                min: 10,\n                max: 0\n            };\n        }\n        const federationStats = await Promise.all(federations.map(async (federation)=>{\n            // Get all assessments for this federation\n            const assessmentKeys = await (0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.keys(`assessment:${federation.id}:*`));\n            const assessmentsRaw = await Promise.all(assessmentKeys.map((key)=>(0,_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.safeRedisOperation)(()=>_lib_upstash__WEBPACK_IMPORTED_MODULE_1__.redis.get(key))));\n            // Parse assessments\n            const assessments = assessmentsRaw.map((a)=>{\n                if (!a) return null;\n                if (typeof a === 'string') {\n                    try {\n                        return JSON.parse(a);\n                    } catch  {\n                        return null;\n                    }\n                }\n                return a;\n            }).filter((assessment)=>assessment !== null && typeof assessment === 'object' && 'section_number' in assessment);\n            // ...rest of the scoring logic remains the same...\n            // Scoring logic (copied from useAssessment.ts)\n            let total = 0;\n            let maxPossible = 0;\n            let sectionScores = {};\n            assessments.forEach((assessment)=>{\n                let sectionScore = 0;\n                let sectionMaxScore = 0;\n                const sn = assessment.section_number;\n                if (sn >= 3 && sn <= 11) {\n                    sectionScores[sn] = 0;\n                }\n                // Continue with existing scoring logic...\n                // Section III\n                if (sn === 3) {\n                    sectionMaxScore = 14;\n                    if (assessment.vision_mission_status === '4') sectionScore += 3;\n                    else if (assessment.vision_mission_status === '3') sectionScore += 2;\n                    else if (assessment.vision_mission_status === '2') sectionScore += 1;\n                    if (assessment.vision_posted === 'yes') sectionScore += 1;\n                    if (assessment.vision_in_documents === 'yes') sectionScore += 1;\n                    if (assessment.vision_for_planning === 'yes') sectionScore += 1;\n                    if (assessment.decision_making === 'a') sectionScore += 3;\n                    else if (assessment.decision_making === 'b') sectionScore += 2;\n                    else if (assessment.decision_making === 'c') sectionScore += 1;\n                    if (assessment.emerging_issues_handling === 'a') sectionScore += 3;\n                    else if (assessment.emerging_issues_handling === 'b') sectionScore += 2;\n                    else if (assessment.emerging_issues_handling === 'c') sectionScore += 1;\n                    if (assessment.leadership_development_plan === 'a') sectionScore += 2;\n                    else if (assessment.leadership_development_plan === 'b') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section IV\n                if (sn === 4) {\n                    sectionMaxScore = 4;\n                    if (assessment.organizational_structure === 'a') sectionScore += 2;\n                    else if (assessment.organizational_structure === 'b') sectionScore += 1;\n                    if (assessment.roles_defined === 'a') sectionScore += 2;\n                    else if (assessment.roles_defined === 'b') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section V\n                if (sn === 5) {\n                    sectionMaxScore = 11;\n                    if (assessment.management_approach === 'a') sectionScore += 2;\n                    else if (assessment.management_approach === 'c') sectionScore += 1;\n                    else if (assessment.management_approach === 'd') sectionScore += 0;\n                    if (assessment.authority_delegation === 'a') sectionScore += 2;\n                    else if (assessment.authority_delegation === 'c') sectionScore += 1;\n                    if (assessment.decision_making_process === '4') sectionScore += 3;\n                    else if (assessment.decision_making_process === '3') sectionScore += 2;\n                    else if (assessment.decision_making_process === '2') sectionScore += 1;\n                    if (assessment.deputy_availability === 'a') sectionScore += 2;\n                    else if (assessment.deputy_availability === 'b') sectionScore += 1;\n                    if (assessment.transition_plan === 'a') sectionScore += 2;\n                    else if (assessment.transition_plan === 'b') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section VI\n                if (sn === 6) {\n                    sectionMaxScore = 9;\n                    if (assessment.workers_involvement_level === '4') sectionScore += 4;\n                    else if (assessment.workers_involvement_level === '3') sectionScore += 3;\n                    else if (assessment.workers_involvement_level === '2') sectionScore += 2;\n                    else if (assessment.workers_involvement_level === '1') sectionScore += 1;\n                    if (assessment.involve_program_activities === 'yes') sectionScore += 1;\n                    if (assessment.involve_leaders_orientation === 'yes') sectionScore += 1;\n                    if (assessment.solicit_feedback === 'yes') sectionScore += 1;\n                    if (assessment.regular_interaction === 'yes') sectionScore += 1;\n                    if (assessment.share_results === 'yes') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section VII\n                if (sn === 7) {\n                    sectionMaxScore = 7;\n                    if (assessment.cultural_gender_consideration === '4') sectionScore += 3;\n                    else if (assessment.cultural_gender_consideration === '3') sectionScore += 2;\n                    else if (assessment.cultural_gender_consideration === '2') sectionScore += 1;\n                    if (assessment.consider_local_culture === 'yes') sectionScore += 1;\n                    if (assessment.documented_guidelines === 'yes') sectionScore += 1;\n                    if (assessment.provide_training === 'yes') sectionScore += 1;\n                    if (assessment.use_assessment_findings === 'yes') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section VIII\n                if (sn === 8) {\n                    sectionMaxScore = 8;\n                    if (assessment.representation_effectiveness === '4') sectionScore += 3;\n                    else if (assessment.representation_effectiveness === '3') sectionScore += 2;\n                    else if (assessment.representation_effectiveness === '2') sectionScore += 1;\n                    if (assessment.member_involvement === '4') sectionScore += 3;\n                    else if (assessment.member_involvement === '3') sectionScore += 2;\n                    else if (assessment.member_involvement === '2') sectionScore += 1;\n                    if (assessment.bargaining_strategy === 'a') sectionScore += 2;\n                    else if (assessment.bargaining_strategy === 'b') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section IX\n                if (sn === 9) {\n                    sectionMaxScore = 11;\n                    if (assessment.communication_effectiveness === '4') sectionScore += 3;\n                    else if (assessment.communication_effectiveness === '3') sectionScore += 2;\n                    else if (assessment.communication_effectiveness === '2') sectionScore += 1;\n                    if (assessment.member_engagement === '4') sectionScore += 3;\n                    else if (assessment.member_engagement === '3') sectionScore += 2;\n                    else if (assessment.member_engagement === '2') sectionScore += 1;\n                    if (assessment.participation_opportunities === '4') sectionScore += 3;\n                    else if (assessment.participation_opportunities === '3') sectionScore += 2;\n                    else if (assessment.participation_opportunities === '2') sectionScore += 1;\n                    if (assessment.feedback_collection === 'a') sectionScore += 2;\n                    else if (assessment.feedback_collection === 'b') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section X\n                if (sn === 10) {\n                    sectionMaxScore = 16;\n                    if (assessment.fee_collection === '4') sectionScore += 3;\n                    else if (assessment.fee_collection === '3') sectionScore += 2;\n                    else if (assessment.fee_collection === '2') sectionScore += 1;\n                    if (assessment.financial_management === '4') sectionScore += 3;\n                    else if (assessment.financial_management === '3') sectionScore += 2;\n                    else if (assessment.financial_management === '2') sectionScore += 1;\n                    if (assessment.financial_planning === '4') sectionScore += 3;\n                    else if (assessment.financial_planning === '3') sectionScore += 2;\n                    else if (assessment.financial_planning === '2') sectionScore += 1;\n                    if (assessment.financial_system_quality === '4') sectionScore += 3;\n                    else if (assessment.financial_system_quality === '3') sectionScore += 2;\n                    else if (assessment.financial_system_quality === '2') sectionScore += 1;\n                    if (assessment.has_cash_system === 'yes') sectionScore += 1;\n                    if (assessment.uses_accounting_software === 'yes') sectionScore += 1;\n                    if (assessment.has_chart_accounts === 'yes') sectionScore += 1;\n                    if (assessment.reconciles_monthly === 'yes') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Section XI\n                if (sn === 11) {\n                    sectionMaxScore = 10;\n                    if (assessment.audit_system_quality === '4') sectionScore += 3;\n                    else if (assessment.audit_system_quality === '3') sectionScore += 2;\n                    else if (assessment.audit_system_quality === '2') sectionScore += 1;\n                    if (assessment.requires_annual_audit === 'yes') sectionScore += 1;\n                    if (assessment.regularly_audited === 'yes') sectionScore += 1;\n                    if (assessment.auditor_selection === 'yes') sectionScore += 1;\n                    if (assessment.audit_manager === 'yes') sectionScore += 1;\n                    if (assessment.implements_recommendations === 'yes') sectionScore += 1;\n                    if (assessment.shares_reports === 'yes') sectionScore += 1;\n                    if (assessment.report_provides_info === 'yes') sectionScore += 1;\n                    total += sectionScore;\n                    maxPossible += sectionMaxScore;\n                }\n                // Track section-by-section scores\n                if (sn >= 3 && sn <= 11) {\n                    // Convert to normalized score out of 10 for consistent comparison\n                    const normalizedScore = sectionMaxScore > 0 ? Math.round(sectionScore / sectionMaxScore * 10 * 100) / 100 : 0;\n                    sectionScores[sn] = normalizedScore;\n                    // Update aggregated section stats\n                    sectionScoresAll[sn].total += normalizedScore;\n                    sectionScoresAll[sn].count += 1;\n                    // Update min and max scores\n                    sectionScoresAll[sn].min = Math.min(sectionScoresAll[sn].min, normalizedScore);\n                    sectionScoresAll[sn].max = Math.max(sectionScoresAll[sn].max, normalizedScore);\n                }\n            });\n            // Handle youth percentage properly\n            const youthPercentage = federation.youth_percentage || \"0-25%\";\n            return {\n                ...federation,\n                totalScore: total,\n                maxScore: maxPossible,\n                percentage: maxPossible > 0 ? Math.round(total / maxPossible * 100) : 0,\n                youth_percentage: youthPercentage,\n                youthPercentageValue: getYouthPercentageValue(youthPercentage),\n                sectionScores\n            };\n        }));\n        // Sort federations by youth percentage for consistent display\n        federationStats.sort((a, b)=>b.youthPercentageValue - a.youthPercentageValue);\n        // Calculate overall stats\n        const totalScores = federationStats.map((f)=>f.totalScore);\n        // We need maxScores for potential future calculations\n        const percentages = federationStats.map((f)=>f.percentage);\n        const avgTotalScore = federationStats.length > 0 ? Math.round(totalScores.reduce((a, b)=>a + b, 0) / federationStats.length) : 0;\n        const avgPercentage = federationStats.length > 0 ? Math.round(percentages.reduce((a, b)=>a + b, 0) / federationStats.length) : 0;\n        // Calculate section-by-section averages, mins, and maxes\n        const avgSectionScores = {};\n        const minSectionScores = {};\n        const maxSectionScores = {};\n        for(let i = 3; i <= 11; i++){\n            // Calculate average scores\n            avgSectionScores[i] = sectionScoresAll[i].count > 0 ? Math.round(sectionScoresAll[i].total / sectionScoresAll[i].count * 100) / 100 : 0;\n            // Set min scores (default to 0 if no data)\n            minSectionScores[i] = sectionScoresAll[i].count > 0 ? sectionScoresAll[i].min : 0;\n            // Set max scores (default to 0 if no data)\n            maxSectionScores[i] = sectionScoresAll[i].count > 0 ? sectionScoresAll[i].max : 0;\n        }\n        // Calculate statistics...\n        const youthCounts = {\n            high: federationStats.filter((f)=>f.youth_percentage === \"above-75%\" || f.youth_percentage === \"51-75%\").length,\n            moderate: federationStats.filter((f)=>f.youth_percentage === \"26-50%\").length,\n            low: federationStats.filter((f)=>f.youth_percentage === \"0-25%\" || !f.youth_percentage).length\n        };\n        // Calculate averages...\n        const avgYouthPercentage = federationStats.reduce((sum, fed)=>sum + fed.youthPercentageValue, 0) / federationStats.length;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            federations: federationStats,\n            avgTotalScore: Math.round(avgTotalScore),\n            avgPercentage: Math.round(avgPercentage),\n            highestScore: Math.max(...totalScores),\n            lowestScore: Math.min(...totalScores),\n            highestPercentage: Math.max(...percentages),\n            lowestPercentage: Math.min(...percentages),\n            avgYouthPercentage: Math.round(avgYouthPercentage * 100) / 100,\n            avgSectionScores,\n            minSectionScores,\n            maxSectionScores,\n            youthCounts\n        });\n    } catch (error) {\n        console.error('Error in GET /api/federations:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch federations',\n            details: error?.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/federations/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/upstash.ts":
/*!************************!*\
  !*** ./lib/upstash.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   redis: () => (/* binding */ redis),\n/* harmony export */   safeRedisOperation: () => (/* binding */ safeRedisOperation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\nconst redis = new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: \"https://ample-bear-14398.upstash.io\",\n    token: \"ATg-AAIjcDExMDY2ODQwYjIwNDI0MWQxODg4MjExMzg3YTMzYTRhNnAxMA\"\n});\n// Helper function to handle Redis operations with error checking\nasync function safeRedisOperation(operation, fallback) {\n    try {\n        const result = await operation();\n        if (result === null && fallback !== undefined) {\n            return fallback;\n        }\n        if (result === null) {\n            return {};\n        }\n        return result;\n    } catch (error) {\n        if (fallback !== undefined) {\n            console.error('Redis operation failed, using fallback:', error);\n            return fallback;\n        }\n        if (error.message?.includes('NOPERM')) {\n            console.error('Redis authentication failed:', error);\n            throw new Error('Database access denied. Please check your credentials.');\n        }\n        if (error.message?.includes('connect')) {\n            console.error('Redis connection error:', error);\n            throw new Error('Failed to connect to database. Please try again.');\n        }\n        if (error.message?.includes('JSON')) {\n            console.error('Redis JSON parsing error:', error);\n            throw new Error('Error retrieving user data. Invalid data format.');\n        }\n        console.error('Redis operation error:', error);\n        throw new Error('Error retrieving user data. Please try again.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/upstash.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederations%2Froute&page=%2Fapi%2Ffederations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederations%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederations%2Froute&page=%2Fapi%2Ffederations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederations%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_tucat_tucat_app_api_federations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/federations/route.ts */ \"(rsc)/./app/api/federations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/federations/route\",\n        pathname: \"/api/federations\",\n        filename: \"route\",\n        bundlePath: \"app/api/federations/route\"\n    },\n    resolvedPagePath: \"D:\\\\tucat\\\\tucat\\\\app\\\\api\\\\federations\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_tucat_tucat_app_api_federations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederations%2Froute&page=%2Fapi%2Ffederations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederations%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ffederations%2Froute&page=%2Fapi%2Ffederations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ffederations%2Froute.ts&appDir=D%3A%5Ctucat%5Ctucat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ctucat%5Ctucat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();