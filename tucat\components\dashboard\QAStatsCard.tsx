"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hart<PERSON><PERSON>, 
  <PERSON>rendingUp, 
  Users, 
  FileText,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { toast } from "sonner";

interface QAStatsData {
  sectionNumber: number;
  sectionTitle: string;
  questions: {
    field: string;
    question: string;
    responses: {
      answer: string;
      count: number;
      percentage: number;
      federations: string[];
    }[];
    totalResponses: number;
  }[];
}

const COLORS = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', 
  '#8884D8', '#82CA9D', '#FFC658', '#FF7C7C',
  '#8DD1E1', '#D084D0', '#87D068', '#FFB347'
];

const sectionTitles: { [key: number]: string } = {
  3: "Leadership",
  4: "Organizational Structure", 
  5: "Management",
  6: "Worker Participation",
  7: "Culture and Gender",
  8: "Collective Bargaining",
  9: "Member Engagement",
  10: "Financial Stability",
  11: "Audit & Compliance"
};

export function QAStatsCard() {
  const [qaStats, setQAStats] = useState<QAStatsData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSection, setSelectedSection] = useState<number>(3);
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchQAStats();
  }, []);

  const fetchQAStats = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/qa-stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch Q&A statistics');
      }
      
      const data = await response.json();
      setQAStats(data);
    } catch (error) {
      console.error('Error fetching Q&A stats:', error);
      toast.error("Failed to load Q&A statistics");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleQuestionExpansion = (questionField: string) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionField)) {
      newExpanded.delete(questionField);
    } else {
      newExpanded.add(questionField);
    }
    setExpandedQuestions(newExpanded);
  };

  const expandAllQuestions = () => {
    if (currentSectionData) {
      const allFields = new Set(currentSectionData.questions.map(q => q.field));
      setExpandedQuestions(allFields);
    }
  };

  const collapseAllQuestions = () => {
    setExpandedQuestions(new Set());
  };

  const renderBarChart = (questionData: QAStatsData['questions'][0]) => {
    const data = questionData.responses.map(response => ({
      answer: response.answer.length > 20 ? response.answer.substring(0, 20) + '...' : response.answer,
      fullAnswer: response.answer,
      count: response.count,
      percentage: response.percentage
    }));

    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
          <XAxis 
            dataKey="answer" 
            angle={-45}
            textAnchor="end"
            height={80}
            fontSize={12}
            interval={0}
          />
          <YAxis 
            label={{ value: 'Number of Federations', angle: -90, position: 'insideLeft' }}
            fontSize={12}
          />
          <Tooltip 
            formatter={(value: any, name: any, props: any) => [
              `${value} federations (${props.payload.percentage.toFixed(1)}%)`,
              'Responses'
            ]}
            labelFormatter={(label: any, payload: any) => 
              payload?.[0]?.payload?.fullAnswer || label
            }
          />
          <Bar dataKey="count" fill="#0088FE" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderPieChart = (questionData: QAStatsData['questions'][0]) => {
    const data = questionData.responses.map((response, index) => ({
      name: response.answer.length > 15 ? response.answer.substring(0, 15) + '...' : response.answer,
      fullName: response.answer,
      value: response.count,
      percentage: response.percentage
    }));

    return (
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ percentage }) => `${percentage.toFixed(1)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip 
            formatter={(value: any, name: any, props: any) => [
              `${value} federations (${props.payload.percentage.toFixed(1)}%)`,
              props.payload.fullName
            ]}
          />
          <Legend 
            formatter={(value: any, entry: any) => entry.payload.fullName}
            wrapperStyle={{ fontSize: '12px' }}
          />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            Q&A Statistics
          </CardTitle>
          <CardDescription>Loading question and answer statistics...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-20 bg-muted rounded-md animate-pulse"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentSectionData = qaStats.find(section => section.sectionNumber === selectedSection);

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              Q&A Statistics
            </CardTitle>
            <CardDescription>
              Analysis of how federations answered assessment questions
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Tabs value={chartType} onValueChange={(value) => setChartType(value as 'bar' | 'pie')}>
              <TabsList className="grid grid-cols-2 w-32">
                <TabsTrigger value="bar" className="text-xs">
                  <BarChart3 className="h-3 w-3 mr-1" />
                  Bar
                </TabsTrigger>
                <TabsTrigger value="pie" className="text-xs">
                  <PieChartIcon className="h-3 w-3 mr-1" />
                  Pie
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Section Selector */}
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Select Assessment Section
          </h3>
          <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-2">
            {Object.entries(sectionTitles).map(([sectionNum, title]) => (
              <Button
                key={sectionNum}
                variant={selectedSection === parseInt(sectionNum) ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedSection(parseInt(sectionNum))}
                className="text-xs"
              >
                {sectionNum}
              </Button>
            ))}
          </div>
        </div>

        {/* Current Section Content */}
        {currentSectionData ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">
                Section {selectedSection}: {currentSectionData.sectionTitle}
              </h2>
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={expandAllQuestions}
                    className="text-xs"
                  >
                    Expand All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={collapseAllQuestions}
                    className="text-xs"
                  >
                    Collapse All
                  </Button>
                </div>
                <Badge variant="secondary">
                  {currentSectionData.questions.length} questions
                </Badge>
              </div>
            </div>

            {/* Questions */}
            <div className="space-y-6">
              {currentSectionData.questions.map((question, index) => (
                <Card key={question.field} className="border-l-4 border-l-primary/20">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-base font-medium">
                          Q{index + 1}: {question.question}
                        </CardTitle>
                        <CardDescription className="flex items-center gap-4 mt-2">
                          <span className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {question.totalResponses} responses
                          </span>
                          <span className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3" />
                            {question.responses.length} unique answers
                          </span>
                        </CardDescription>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleQuestionExpansion(question.field)}
                      >
                        {expandedQuestions.has(question.field) ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </CardHeader>
                  
                  {expandedQuestions.has(question.field) && (
                    <CardContent>
                      {question.responses.length > 0 ? (
                        <div className="space-y-4">
                          {/* Chart */}
                          <div>
                            {chartType === 'bar' ? renderBarChart(question) : renderPieChart(question)}
                          </div>
                          
                          {/* Response Summary */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            {question.responses.map((response, idx) => (
                              <div 
                                key={idx}
                                className="p-3 rounded-lg border bg-muted/20"
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <div 
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: COLORS[idx % COLORS.length] }}
                                  ></div>
                                  <Badge variant="outline" className="text-xs">
                                    {response.count} ({response.percentage.toFixed(1)}%)
                                  </Badge>
                                </div>
                                <p className="text-sm font-medium">{response.answer}</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Federations: {response.federations.join(', ')}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <FileText className="h-8 w-8 mx-auto mb-2" />
                          <p>No responses available for this question</p>
                        </div>
                      )}
                    </CardContent>
                  )}
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-12 text-muted-foreground">
            <BarChart3 className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Data Available</h3>
            <p>No statistics available for Section {selectedSection}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
