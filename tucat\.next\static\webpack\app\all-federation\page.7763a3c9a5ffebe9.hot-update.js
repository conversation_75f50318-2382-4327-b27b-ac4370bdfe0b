"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/all-federation/page",{

/***/ "(app-pages-browser)/./components/dashboard/QAStatsCard.tsx":
/*!**********************************************!*\
  !*** ./components/dashboard/QAStatsCard.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QAStatsCard: () => (/* binding */ QAStatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-pie.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronUp,FileText,PieChart,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/./node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ QAStatsCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst COLORS = [\n    '#0088FE',\n    '#00C49F',\n    '#FFBB28',\n    '#FF8042',\n    '#8884D8',\n    '#82CA9D',\n    '#FFC658',\n    '#FF7C7C',\n    '#8DD1E1',\n    '#D084D0',\n    '#87D068',\n    '#FFB347'\n];\nconst sectionTitles = {\n    3: \"Leadership\",\n    4: \"Organizational Structure\",\n    5: \"Management\",\n    6: \"Worker Participation\",\n    7: \"Culture and Gender\",\n    8: \"Collective Bargaining\",\n    9: \"Member Engagement\",\n    10: \"Financial Stability\",\n    11: \"Audit & Compliance\"\n};\nfunction QAStatsCard() {\n    _s();\n    const [qaStats, setQAStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedSection, setSelectedSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('bar');\n    const [expandedQuestions, setExpandedQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QAStatsCard.useEffect\": ()=>{\n            fetchQAStats();\n        }\n    }[\"QAStatsCard.useEffect\"], []);\n    const fetchQAStats = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch('/api/qa-stats');\n            if (!response.ok) {\n                throw new Error('Failed to fetch Q&A statistics');\n            }\n            const data = await response.json();\n            setQAStats(data);\n        } catch (error) {\n            console.error('Error fetching Q&A stats:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load Q&A statistics\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleQuestionExpansion = (questionField)=>{\n        const newExpanded = new Set(expandedQuestions);\n        if (newExpanded.has(questionField)) {\n            newExpanded.delete(questionField);\n        } else {\n            newExpanded.add(questionField);\n        }\n        setExpandedQuestions(newExpanded);\n    };\n    const expandAllQuestions = ()=>{\n        if (currentSectionData) {\n            const allFields = new Set(currentSectionData.questions.map((q)=>q.field));\n            setExpandedQuestions(allFields);\n        }\n    };\n    const collapseAllQuestions = ()=>{\n        setExpandedQuestions(new Set());\n    };\n    const renderBarChart = (questionData)=>{\n        const data = questionData.responses.map((response)=>({\n                answer: response.answer.length > 20 ? response.answer.substring(0, 20) + '...' : response.answer,\n                fullAnswer: response.answer,\n                count: response.count,\n                percentage: response.percentage\n            }));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.BarChart, {\n                data: data,\n                margin: {\n                    top: 20,\n                    right: 30,\n                    left: 20,\n                    bottom: 60\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                        strokeDasharray: \"3 3\",\n                        stroke: \"#e5e7eb\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                        dataKey: \"answer\",\n                        angle: -45,\n                        textAnchor: \"end\",\n                        height: 80,\n                        fontSize: 12,\n                        interval: 0\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                        label: {\n                            value: 'Number of Federations',\n                            angle: -90,\n                            position: 'insideLeft'\n                        },\n                        fontSize: 12\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        formatter: (value, name, props)=>[\n                                \"\".concat(value, \" federations (\").concat(props.payload.percentage.toFixed(1), \"%)\"),\n                                'Responses'\n                            ],\n                        labelFormatter: (label, payload)=>{\n                            var _payload__payload, _payload_;\n                            return (payload === null || payload === void 0 ? void 0 : (_payload_ = payload[0]) === null || _payload_ === void 0 ? void 0 : (_payload__payload = _payload_.payload) === null || _payload__payload === void 0 ? void 0 : _payload__payload.fullAnswer) || label;\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                        dataKey: \"count\",\n                        fill: \"#0088FE\",\n                        radius: [\n                            4,\n                            4,\n                            0,\n                            0\n                        ]\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    };\n    const renderPieChart = (questionData)=>{\n        const data = questionData.responses.map((response, index)=>({\n                name: response.answer.length > 15 ? response.answer.substring(0, 15) + '...' : response.answer,\n                fullName: response.answer,\n                value: response.count,\n                percentage: response.percentage\n            }));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.PieChart, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Pie, {\n                        data: data,\n                        cx: \"50%\",\n                        cy: \"50%\",\n                        labelLine: false,\n                        label: (param)=>{\n                            let { percentage } = param;\n                            return \"\".concat(percentage.toFixed(1), \"%\");\n                        },\n                        outerRadius: 80,\n                        fill: \"#8884d8\",\n                        dataKey: \"value\",\n                        children: data.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_16__.Cell, {\n                                fill: COLORS[index % COLORS.length]\n                            }, \"cell-\".concat(index), false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                        formatter: (value, name, props)=>[\n                                \"\".concat(value, \" federations (\").concat(props.payload.percentage.toFixed(1), \"%)\"),\n                                props.payload.fullName\n                            ]\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_17__.Legend, {\n                        formatter: (value, entry)=>entry.payload.fullName,\n                        wrapperStyle: {\n                            fontSize: '12px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 165,\n            columnNumber: 7\n        }, this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                \"Q&A Statistics\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                            children: \"Loading question and answer statistics...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            1,\n                            2,\n                            3\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-20 bg-muted rounded-md animate-pulse\"\n                            }, i, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    const currentSectionData = qaStats.find((section)=>section.sectionNumber === selectedSection);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-xl flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Q&A Statistics\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"Analysis of how federations answered assessment questions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                value: chartType,\n                                onValueChange: (value)=>setChartType(value),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"grid grid-cols-2 w-32\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"bar\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Bar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: \"pie\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Pie\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium mb-3 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Select Assessment Section\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-2\",\n                                children: Object.entries(sectionTitles).map((param)=>{\n                                    let [sectionNum, title] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: selectedSection === parseInt(sectionNum) ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedSection(parseInt(sectionNum)),\n                                        className: \"text-xs\",\n                                        children: sectionNum\n                                    }, sectionNum, false, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    currentSectionData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: [\n                                            \"Section \",\n                                            selectedSection,\n                                            \": \",\n                                            currentSectionData.sectionTitle\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        children: [\n                                            currentSectionData.questions.length,\n                                            \" questions\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: currentSectionData.questions.map((question, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"border-l-4 border-l-primary/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                    className: \"text-base font-medium\",\n                                                                    children: [\n                                                                        \"Q\",\n                                                                        index + 1,\n                                                                        \": \",\n                                                                        question.question\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                                    className: \"flex items-center gap-4 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                question.totalResponses,\n                                                                                \" responses\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 293,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 298,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                question.responses.length,\n                                                                                \" unique answers\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>toggleQuestionExpansion(question.field),\n                                                            children: expandedQuestions.has(question.field) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            expandedQuestions.has(question.field) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                children: question.responses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: chartType === 'bar' ? renderBarChart(question) : renderPieChart(question)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\",\n                                                            children: question.responses.map((response, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-3 rounded-lg border bg-muted/20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between mb-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-3 h-3 rounded-full\",\n                                                                                    style: {\n                                                                                        backgroundColor: COLORS[idx % COLORS.length]\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 334,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                    variant: \"outline\",\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        response.count,\n                                                                                        \" (\",\n                                                                                        response.percentage.toFixed(1),\n                                                                                        \"%)\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                                    lineNumber: 338,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: response.answer\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground mt-1\",\n                                                                            children: [\n                                                                                \"Federations: \",\n                                                                                response.federations.join(', ')\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, idx, true, {\n                                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 31\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-8 w-8 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"No responses available for this question\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, question.field, true, {\n                                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronUp_FileText_PieChart_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"No Data Available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"No statistics available for Section \",\n                                    selectedSection\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\tucat\\\\tucat\\\\components\\\\dashboard\\\\QAStatsCard.tsx\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, this);\n}\n_s(QAStatsCard, \"WPXcFe8wDyl3krN0KffCtb+K0ro=\");\n_c = QAStatsCard;\nvar _c;\n$RefreshReg$(_c, \"QAStatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/QAStatsCard.tsx\n"));

/***/ })

});