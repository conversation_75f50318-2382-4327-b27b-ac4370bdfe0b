import { NextRequest, NextResponse } from 'next/server';
import { redis, safeRedisOperation } from '@/lib/upstash';

// Question mappings for each section
const sectionQuestions: { [key: number]: { [field: string]: { question: string; answerMap?: { [key: string]: string } } } } = {
  3: {
    'vision_mission_status': {
      question: 'Does the Federation have a clear vision and mission?',
      answerMap: {
        '1': 'No vision or mission statement',
        '2': 'Vision/mission exists but not well communicated',
        '3': 'Vision/mission exists and somewhat communicated',
        '4': 'Clear vision/mission, well communicated to all'
      }
    },
    'decision_making': {
      question: 'How are decisions made in the Federation?',
      answerMap: {
        'a': 'Democratic process with member consultation',
        'b': 'Executive committee decisions',
        'c': 'Top-down leadership decisions',
        'd': 'Ad-hoc decision making'
      }
    },
    'emerging_issues_handling': {
      question: 'How does the Federation handle emerging issues?',
      answerMap: {
        'a': 'Proactive approach with systematic response',
        'b': 'Reactive approach with some planning',
        'c': 'Minimal response to issues',
        'd': 'No systematic approach'
      }
    },
    'leadership_development_plan': {
      question: 'Does the Federation have a leadership development plan?',
      answerMap: {
        'a': 'Comprehensive leadership development program',
        'b': 'Basic leadership training available',
        'c': 'Informal leadership development',
        'd': 'No leadership development plan'
      }
    }
  },
  4: {
    'organizational_structure': {
      question: 'What is the organizational structure of the Federation?',
      answerMap: {
        'a': 'Well-defined hierarchical structure',
        'b': 'Basic organizational structure',
        'c': 'Informal structure',
        'd': 'No clear structure'
      }
    },
    'roles_defined': {
      question: 'Are roles and responsibilities clearly defined?',
      answerMap: {
        'a': 'All roles clearly documented and communicated',
        'b': 'Most roles defined',
        'c': 'Some roles defined',
        'd': 'Roles not clearly defined'
      }
    }
  },
  5: {
    'management_approach': {
      question: 'How is the Federation managed?',
      answerMap: {
        'a': 'Professional management with clear processes',
        'b': 'Structured management approach',
        'c': 'Basic management practices',
        'd': 'Informal management'
      }
    },
    'authority_delegation': {
      question: 'How is authority delegated in the Federation?',
      answerMap: {
        'a': 'Clear delegation with proper oversight',
        'b': 'Some delegation with monitoring',
        'c': 'Limited delegation',
        'd': 'Centralized authority, no delegation'
      }
    },
    'decision_making_process': {
      question: 'What is the decision-making process?',
      answerMap: {
        '1': 'No formal process',
        '2': 'Basic process exists',
        '3': 'Structured process with documentation',
        '4': 'Comprehensive process with stakeholder involvement'
      }
    }
  },
  6: {
    'workers_involvement_level': {
      question: 'What is the level of workers\' involvement in the Federation?',
      answerMap: {
        '1': 'Minimal involvement',
        '2': 'Basic involvement in some activities',
        '3': 'Good involvement in most activities',
        '4': 'High level of involvement in all activities'
      }
    }
  },
  7: {
    'cultural_gender_consideration': {
      question: 'How does the Federation consider cultural and gender aspects?',
      answerMap: {
        '1': 'No consideration',
        '2': 'Basic awareness',
        '3': 'Good consideration with some policies',
        '4': 'Comprehensive cultural and gender policies'
      }
    }
  },
  8: {
    'representation_effectiveness': {
      question: 'How effective is the Federation\'s representation?',
      answerMap: {
        '1': 'Poor representation',
        '2': 'Basic representation',
        '3': 'Good representation',
        '4': 'Excellent representation'
      }
    },
    'member_involvement': {
      question: 'What is the level of member involvement in collective bargaining?',
      answerMap: {
        '1': 'No member involvement',
        '2': 'Limited involvement',
        '3': 'Good member involvement',
        '4': 'High member involvement'
      }
    }
  },
  9: {
    'communication_effectiveness': {
      question: 'How effective is the Federation\'s communication?',
      answerMap: {
        '1': 'Poor communication',
        '2': 'Basic communication',
        '3': 'Good communication',
        '4': 'Excellent communication'
      }
    },
    'member_engagement': {
      question: 'What is the level of member engagement?',
      answerMap: {
        '1': 'Low engagement',
        '2': 'Basic engagement',
        '3': 'Good engagement',
        '4': 'High engagement'
      }
    }
  },
  10: {
    'fee_collection': {
      question: 'How does the Federation collect fees?',
      answerMap: {
        '1': 'No systematic collection',
        '2': 'Basic collection system',
        '3': 'Good collection system',
        '4': 'Excellent systematic collection'
      }
    },
    'financial_management': {
      question: 'How is financial management handled?',
      answerMap: {
        '1': 'Poor financial management',
        '2': 'Basic financial management',
        '3': 'Good financial management',
        '4': 'Excellent financial management'
      }
    }
  },
  11: {
    'audit_system_quality': {
      question: 'What is the quality of the audit system?',
      answerMap: {
        '1': 'No audit system',
        '2': 'Basic audit system',
        '3': 'Good audit system',
        '4': 'Excellent audit system'
      }
    },
    'requires_annual_audit': {
      question: 'Does the Federation require annual audits?',
      answerMap: { 'yes': 'Yes', 'no': 'No', 'na': 'Not Applicable' }
    }
  }
};

const sectionTitles: { [key: number]: string } = {
  3: "Leadership",
  4: "Organizational Structure", 
  5: "Management",
  6: "Worker Participation",
  7: "Culture and Gender",
  8: "Collective Bargaining",
  9: "Member Engagement",
  10: "Financial Stability",
  11: "Audit & Compliance"
};

export async function GET(request: NextRequest) {
  try {
    // Get all federations
    const federationKeys = await safeRedisOperation(() => redis.keys('federation:*'));
    const federations = await Promise.all(
      federationKeys.map(async (key) => {
        const data = await safeRedisOperation(() => redis.get(key));
        if (typeof data === 'string') {
          try {
            return JSON.parse(data);
          } catch {
            return null;
          }
        }
        return data;
      })
    );

    const validFederations = federations.filter(f => f && f.id);

    // Get all assessments
    const assessmentKeys = await safeRedisOperation(() => redis.keys('assessment:*'));
    const assessments = await Promise.all(
      assessmentKeys.map(async (key) => {
        const data = await safeRedisOperation(() => redis.get(key));
        if (typeof data === 'string') {
          try {
            return JSON.parse(data);
          } catch {
            return null;
          }
        }
        return data;
      })
    );

    const validAssessments = assessments.filter(a => a && a.federation_id && a.section_number);

    // Process Q&A statistics for sections 3-11
    const qaStats = [];

    for (const sectionNum of [3, 4, 5, 6, 7, 8, 9, 10, 11]) {
      const sectionAssessments = validAssessments.filter(a => a.section_number === sectionNum);
      const questions = sectionQuestions[sectionNum] || {};
      
      const questionStats = [];

      for (const [field, config] of Object.entries(questions)) {
        const responses: { [answer: string]: { count: number; federations: string[] } } = {};
        let totalResponses = 0;

        // Count responses for this question
        sectionAssessments.forEach(assessment => {
          const rawAnswer = assessment[field];
          if (rawAnswer !== undefined && rawAnswer !== null && rawAnswer !== '') {
            let decodedAnswer = rawAnswer;
            
            // Decode answer if mapping exists
            if (config.answerMap && config.answerMap[rawAnswer]) {
              decodedAnswer = config.answerMap[rawAnswer];
            }

            if (!responses[decodedAnswer]) {
              responses[decodedAnswer] = { count: 0, federations: [] };
            }
            
            responses[decodedAnswer].count++;
            responses[decodedAnswer].federations.push(assessment.federation_id.toUpperCase());
            totalResponses++;
          }
        });

        // Convert to array format with percentages
        const responseArray = Object.entries(responses).map(([answer, data]) => ({
          answer,
          count: data.count,
          percentage: totalResponses > 0 ? (data.count / totalResponses) * 100 : 0,
          federations: data.federations.sort()
        })).sort((a, b) => b.count - a.count); // Sort by count descending

        questionStats.push({
          field,
          question: config.question,
          responses: responseArray,
          totalResponses
        });
      }

      qaStats.push({
        sectionNumber: sectionNum,
        sectionTitle: sectionTitles[sectionNum],
        questions: questionStats
      });
    }

    return NextResponse.json(qaStats);

  } catch (error) {
    console.error('Error in GET /api/qa-stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch Q&A statistics', details: error?.message },
      { status: 500 }
    );
  }
}
