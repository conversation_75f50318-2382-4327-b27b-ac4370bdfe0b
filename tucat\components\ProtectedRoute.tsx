"use client";

import { ReactNode, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from './AuthContext';
import { UserRole } from '@/lib/types';
import { toast } from 'sonner';

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: UserRole[];
  publicPaths?: string[];
}

export function ProtectedRoute({ 
  children, 
  allowedRoles = [],
  publicPaths = ['/register'] 
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  
  // More flexible path checking - check if the current path includes any of the public paths
  const isPublicPath = publicPaths.some(path => 
    pathname === path || pathname.startsWith(`${path}/`)
  );

  useEffect(() => {
    // Log the current path for debugging
    console.log('Current pathname:', pathname);
    console.log('Is public path:', isPublicPath);
    
    // Skip auth checks for public paths like register
    if (isPublicPath) return;

    // If not loading and not authenticated, redirect to login
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    // If authenticated but role is not allowed, redirect based on user type
    if (
      isAuthenticated &&
      user &&
      allowedRoles.length > 0 &&
      !allowedRoles.includes(user.role)
    ) {
      toast.error(`Access denied. Your role (${user.role}) does not have permission to view this page.`);

      // Redirect based on user role and email
      if (user.email === '<EMAIL>' || user.role === 'admin') {
        router.push('/all-federation');
      } else {
        router.push('/assessment');
      }
      return;
    }
  }, [isLoading, isAuthenticated, user, router, allowedRoles, isPublicPath, pathname]);

  // Show content immediately for public paths
  if (isPublicPath) {
    return <>{children}</>;
  }

  // Show loading or nothing while checking authentication
  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary mx-auto"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  // If role check passes, render children
  return <>{children}</>;
}